# Local PostgreSQL Database Setup

This guide will help you set up a local PostgreSQL database instead of using NeonDB, ensuring all your existing data is preserved.

## Prerequisites

1. **Docker Desktop** - Download and install from [https://www.docker.com/products/docker-desktop](https://www.docker.com/products/docker-desktop)
2. **Node.js** - Already installed (since the app is running)

## Quick Setup (Recommended)

### Step 1: Install Required Dependencies
```bash
npm install @types/pg
```

### Step 2: Start Local Database
```bash
node setup-local-db.js
```

### Step 3: Migrate Data from Neon
```bash
node migrate-data.js
```

### Step 4: Start the Application
```bash
npm run dev
```

## Manual Setup (Alternative)

If you prefer to set up manually or the quick setup doesn't work:

### 1. Start PostgreSQL with Docker
```bash
docker-compose up -d
```

### 2. Wait for Database to be Ready
```bash
docker-compose exec postgres pg_isready -U admin -d aluminum_optimizer
```

### 3. Create Database Schema
```bash
npm run db:push
```

### 4. Migrate Data
```bash
node migrate-data.js
```

## Database Configuration

The local database is configured with:
- **Host**: localhost
- **Port**: 5432
- **Database**: aluminum_optimizer
- **Username**: admin
- **Password**: admin123

## Connecting to the Database

You can connect to your local database using any PostgreSQL client:

### Using psql (Command Line)
```bash
docker-compose exec postgres psql -U admin -d aluminum_optimizer
```

### Using pgAdmin or other GUI tools
- Host: localhost
- Port: 5432
- Database: aluminum_optimizer
- Username: admin
- Password: admin123

## Data Migration Details

The migration script will transfer the following tables:
- ✅ users
- ✅ profiles
- ✅ window_designs
- ✅ window_design_components
- ✅ window_design_glass_specifications
- ✅ window_projects
- ✅ project_window_items
- ✅ accessories
- ✅ invoices
- ✅ subscription_plans

## Troubleshooting

### Docker Issues
```bash
# Check if Docker is running
docker --version

# Restart Docker containers
docker-compose down
docker-compose up -d

# View container logs
docker-compose logs postgres
```

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps

# Test connection
docker-compose exec postgres pg_isready -U admin -d aluminum_optimizer
```

### Migration Issues
```bash
# Check Neon connection
node -e "
const { Pool } = require('@neondatabase/serverless');
const pool = new Pool({connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'});
pool.query('SELECT 1').then(() => console.log('Neon OK')).catch(console.error);
"

# Check local connection
node -e "
const { Pool } = require('pg');
const pool = new Pool({connectionString: 'postgresql://admin:admin123@localhost:5432/aluminum_optimizer'});
pool.query('SELECT 1').then(() => console.log('Local OK')).catch(console.error);
"
```

## Switching Back to Neon (If Needed)

To switch back to NeonDB, simply update your `.env` file:

```env
# Comment out local database
# DATABASE_URL=postgresql://admin:admin123@localhost:5432/aluminum_optimizer

# Uncomment Neon database
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

## Benefits of Local Database

✅ **No Connection Timeouts** - Eliminates network-related issues
✅ **Faster Performance** - Local database is much faster
✅ **Offline Development** - Work without internet connection
✅ **Full Control** - Complete control over database configuration
✅ **Cost Effective** - No cloud database costs
✅ **Data Privacy** - All data stays on your local machine

## Maintenance

### Backup Your Data
```bash
# Create backup
docker-compose exec postgres pg_dump -U admin aluminum_optimizer > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U admin aluminum_optimizer < backup.sql
```

### Update Database
```bash
# Apply new migrations
npm run db:push
```

### Clean Up
```bash
# Stop and remove containers
docker-compose down

# Remove volumes (WARNING: This will delete all data)
docker-compose down -v
```
