import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';

neonConfig.webSocketConstructor = ws;

// SQL to create window projects table
const createWindowProjectsTable = `
CREATE TABLE IF NOT EXISTS window_projects (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  client_name TEXT,
  client_contact TEXT,
  location TEXT,
  due_date TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  user_id INTEGER REFERENCES users(id),
  status TEXT DEFAULT 'draft' NOT NULL,
  notes TEXT
);`;

// SQL to create project window items table
const createProjectWindowItemsTable = `
CREATE TABLE IF NOT EXISTS project_window_items (
  id SERIAL PRIMARY KEY,
  project_id INTEGER REFERENCES window_projects(id) NOT NULL,
  window_design_id INTEGER REFERENCES window_designs(id) NOT NULL,
  window_cutting_list_id INTEGER REFERENCES window_cutting_lists(id),
  width REAL NOT NULL,
  height REAL NOT NULL,
  quantity INTEGER DEFAULT 1 NOT NULL,
  position INTEGER DEFAULT 0 NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);`;

// Function to check database connection and create missing tables
async function setupDatabase() {
  if (!process.env.DATABASE_URL) {
    console.error("DATABASE_URL environment variable is not set");
    return;
  }

  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  
  try {
    // Test the connection by running a simple query
    const result = await pool.query('SELECT NOW()');
    console.log('Database connection successful!');
    console.log('Current timestamp from database:', result.rows[0].now);
    
    // List tables in the public schema
    const tables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log('\nExisting tables:');
    if (tables.rowCount === 0) {
      console.log('No tables found');
    } else {
      tables.rows.forEach(row => {
        console.log(`- ${row.table_name}`);
      });
    }

    // Create window_projects table
    console.log('\nCreating window_projects table...');
    await pool.query(createWindowProjectsTable);
    console.log('window_projects table created successfully.');
    
    // Create project_window_items table
    console.log('Creating project_window_items table...');
    await pool.query(createProjectWindowItemsTable);
    console.log('project_window_items table created successfully.');
    
    console.log('\nDatabase setup completed successfully.');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

// Run the setup
setupDatabase();