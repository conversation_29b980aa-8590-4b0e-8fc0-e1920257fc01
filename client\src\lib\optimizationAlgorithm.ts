import { CutPiece, StockSettings, OptimizationResult, CuttingPlan, CutItem, ProfileResult } from './types';

/**
 * First-Fit Decreasing (FFD) algorithm for bin packing for a specific profile type
 * 1. Sort pieces by length in descending order
 * 2. Place each piece in the first bin where it fits
 * 3. If no bin has enough space, create a new bin
 */
export function optimizeCuttingForProfile(
  cutList: CutPiece[],
  settings: StockSettings,
  profileType: string
): ProfileResult {
  // Validate inputs
  if (!cutList || cutList.length === 0) {
    throw new Error(`Cut list for ${profileType} profile is empty`);
  }
  
  if (settings.stockLength <= 0) {
    throw new Error("Stock length must be positive");
  }
  
  if (settings.kerf < 0) {
    throw new Error("Kerf cannot be negative");
  }
  
  if (settings.endTrim < 0) {
    throw new Error("End trim cannot be negative");
  }
  
  // Filter and expand the cut list based on profile type and quantity
  const expandedCutList: CutItem[] = [];
  cutList
    .filter(piece => piece.profileType === profileType)
    .forEach(piece => {
      for (let i = 0; i < piece.quantity; i++) {
        expandedCutList.push({
          length: piece.length,
          description: piece.description,
          profileType: piece.profileType
        });
      }
    });
  
  // If no pieces of this profile type, return empty result
  if (expandedCutList.length === 0) {
    return {
      stockPiecesCount: 0,
      materialUsagePercent: 0,
      totalWaste: 0,
      totalCuts: 0,
      cuttingPlans: [],
      profileType
    };
  }
  
  // Sort pieces by length in descending order
  expandedCutList.sort((a, b) => b.length - a.length);
  
  // Initialize bins (stock pieces)
  const bins: CutItem[][] = [];
  const remainingLengths: number[] = [];
  
  // Account for end trim in effective stock length
  const effectiveStockLength = settings.stockLength - settings.endTrim;
  
  // Place each piece using First-Fit Decreasing algorithm
  expandedCutList.forEach(piece => {
    if (piece.length <= 0) return; // Skip invalid pieces
    
    // First, try to place the piece in an existing bin
    let placed = false;
    for (let i = 0; i < bins.length; i++) {
      // Calculate required space including kerf
      const requiredSpace = piece.length + (bins[i].length > 0 ? settings.kerf : 0);
      
      if (remainingLengths[i] >= requiredSpace) {
        bins[i].push(piece);
        remainingLengths[i] -= requiredSpace;
        placed = true;
        break;
      }
    }
    
    // If the piece couldn't be placed in existing bins, create a new bin
    if (!placed) {
      bins.push([piece]);
      remainingLengths.push(effectiveStockLength - piece.length);
    }
  });
  
  // Calculate total cuts, waste, etc.
  const totalCuts = expandedCutList.length - bins.length; // Each bin needs (pieces-1) cuts
  const totalWaste = remainingLengths.reduce((sum, waste) => sum + waste, 0) + (bins.length * settings.endTrim);
  const materialUsagePercent = bins.length > 0 ? 1 - (totalWaste / (bins.length * settings.stockLength)) : 0;
  
  // Create cutting plans
  const cuttingPlans: CuttingPlan[] = bins.map((bin, index) => {
    return {
      stockPieceId: index + 1,
      cuts: bin,
      waste: remainingLengths[index],
      materialUsagePercent: 1 - (remainingLengths[index] / effectiveStockLength),
      profileType // Add profile type to the cutting plan
    };
  });
  
  return {
    stockPiecesCount: bins.length,
    materialUsagePercent,
    totalWaste,
    totalCuts,
    cuttingPlans,
    profileType
  };
}

/**
 * Gets unique profile types from the cut list
 */
function getUniqueProfileTypes(cutList: CutPiece[]): string[] {
  const profileSet = new Set<string>();
  cutList.forEach(piece => {
    if (piece.profileType && piece.profileType.trim() !== '') {
      profileSet.add(piece.profileType.trim());
    } else {
      // If no profile type is specified, use the description as the profile type
      const profileFromDescription = extractProfileFromDescription(piece.description);
      if (profileFromDescription) {
        profileSet.add(profileFromDescription);
      } else {
        // Default profile if nothing can be extracted
        profileSet.add('Default');
      }
    }
  });
  return Array.from(profileSet);
}

/**
 * Extracts profile type from the description
 * Looks for common patterns in descriptions like "Profile: X" or "[X Profile]"
 */
function extractProfileFromDescription(description: string): string | null {
  if (!description) return null;
  
  const desc = description.trim();
  
  // Try to extract profile from description using patterns
  const profilePattern = /profile[\s:]+([^\s,;]+)/i;
  const bracketPattern = /\[([^\]]+)(?:\s+profile)\]/i;
  const typePattern = /type[\s:]+([^\s,;]+)/i;
  
  const profileMatch = desc.match(profilePattern);
  if (profileMatch && profileMatch[1]) {
    return profileMatch[1];
  }
  
  const bracketMatch = desc.match(bracketPattern);
  if (bracketMatch && bracketMatch[1]) {
    return bracketMatch[1];
  }
  
  const typeMatch = desc.match(typePattern);
  if (typeMatch && typeMatch[1]) {
    return typeMatch[1];
  }
  
  // If no pattern matched, use the first word of the description
  const firstWord = desc.split(/\s+/)[0];
  if (firstWord && firstWord.length > 1) {
    return firstWord;
  }
  
  return null;
}

/**
 * Ensures all cut pieces have a profile type, extracting from description if needed
 */
function ensureProfileTypes(cutList: CutPiece[]): CutPiece[] {
  return cutList.map(piece => {
    if (!piece.profileType || piece.profileType.trim() === '') {
      const profileFromDescription = extractProfileFromDescription(piece.description);
      return {
        ...piece,
        profileType: profileFromDescription || 'Default'
      };
    }
    return piece;
  });
}

/**
 * Main optimization function that handles all profile types separately
 */
export function optimizeCutting(
  cutList: CutPiece[],
  settings: StockSettings
): OptimizationResult {
  // Validate inputs
  if (!cutList || cutList.length === 0) {
    throw new Error("Cut list is empty");
  }
  
  // Ensure profile types are set for all pieces
  const processedCutList = ensureProfileTypes(cutList);
  
  // Get all unique profile types
  const profileTypes = getUniqueProfileTypes(processedCutList);
  
  // Process each profile type separately
  const profileResults: ProfileResult[] = profileTypes.map(profileType => 
    optimizeCuttingForProfile(processedCutList, settings, profileType)
  );
  
  // Calculate total stock pieces
  const totalStockPieces = profileResults.reduce(
    (total, result) => total + result.stockPiecesCount, 
    0
  );
  
  return {
    profileResults,
    totalStockPieces
  };
}

/**
 * Alternative optimization strategy - Cutting Stock Problem solver using 
 * a modified version of the First-Fit Decreasing algorithm
 * which tries to optimize for minimum waste
 */
export function optimizeCuttingAdvanced(
  cutList: CutPiece[],
  settings: StockSettings
): OptimizationResult {
  // Implementation can be expanded with more sophisticated algorithms
  // like Branch-and-Bound, Column Generation, or dynamic programming approaches
  // for better optimization results
  
  // For now, just use the same approach as the basic optimization
  return optimizeCutting(cutList, settings);
}