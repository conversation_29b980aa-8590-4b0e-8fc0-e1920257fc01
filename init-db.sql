-- Initialize the aluminum optimizer database
-- This script will run when the Docker container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- The tables will be created by Drizzle migrations
-- This file is just to ensure the database is properly initialized

-- You can add any initial data here if needed
-- For example, default profiles, window designs, etc.

SELECT 'Database initialized successfully' as status;
