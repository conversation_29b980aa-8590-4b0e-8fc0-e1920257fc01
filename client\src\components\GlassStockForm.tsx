import { useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { GlassStockSettings } from "@/lib/types";

// Validation schema
const formSchema = z.object({
  sheetWidth: z.coerce.number().positive("Width must be positive").min(1, "Width must be at least 1mm"),
  sheetHeight: z.coerce.number().positive("Height must be positive").min(1, "Height must be at least 1mm"),
  kerf: z.coerce.number().min(0, "Kerf cannot be negative"),
  edgeTrim: z.coerce.number().min(0, "Edge trim cannot be negative")
});

interface GlassStockFormProps {
  glassStockSettings: GlassStockSettings;
  onUpdateSettings: (settings: GlassStockSettings) => void;
}

export default function GlassStockForm({ glassStockSettings, onUpdateSettings }: GlassStockFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sheetWidth: glassStockSettings.sheetWidth,
      sheetHeight: glassStockSettings.sheetHeight,
      kerf: glassStockSettings.kerf,
      edgeTrim: glassStockSettings.edgeTrim
    }
  });
  
  function onSubmit(values: z.infer<typeof formSchema>) {
    onUpdateSettings({
      sheetWidth: values.sheetWidth,
      sheetHeight: values.sheetHeight,
      kerf: values.kerf,
      edgeTrim: values.edgeTrim
    });
    setIsEditing(false);
  }
  
  const handleEdit = () => {
    setIsEditing(true);
  };
  
  const handleCancel = () => {
    form.reset({
      sheetWidth: glassStockSettings.sheetWidth,
      sheetHeight: glassStockSettings.sheetHeight,
      kerf: glassStockSettings.kerf,
      edgeTrim: glassStockSettings.edgeTrim
    });
    setIsEditing(false);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Glass Sheet Settings</CardTitle>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="sheetWidth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sheet Width (mm)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          step="any"
                          min="0"
                        />
                      </FormControl>
                      <FormDescription>
                        Width of standard glass sheet
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="sheetHeight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sheet Height (mm)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          step="any"
                          min="0"
                        />
                      </FormControl>
                      <FormDescription>
                        Height of standard glass sheet
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="kerf"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kerf Width (mm)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          step="any"
                          min="0"
                        />
                      </FormControl>
                      <FormDescription>
                        Width of blade cut (material lost in cutting)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="edgeTrim"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Edge Trim (mm)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          step="any"
                          min="0"
                        />
                      </FormControl>
                      <FormDescription>
                        Trimmed edges of the sheet (unusable margin)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                
                <Button type="submit">
                  Save Settings
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="text-neutral-600 text-sm font-medium mb-1">Sheet Width</h3>
                <p className="text-lg font-semibold">{glassStockSettings.sheetWidth} mm</p>
              </div>
              
              <div>
                <h3 className="text-neutral-600 text-sm font-medium mb-1">Sheet Height</h3>
                <p className="text-lg font-semibold">{glassStockSettings.sheetHeight} mm</p>
              </div>
              
              <div>
                <h3 className="text-neutral-600 text-sm font-medium mb-1">Kerf Width</h3>
                <p className="text-lg font-semibold">{glassStockSettings.kerf} mm</p>
              </div>
              
              <div>
                <h3 className="text-neutral-600 text-sm font-medium mb-1">Edge Trim</h3>
                <p className="text-lg font-semibold">{glassStockSettings.edgeTrim} mm</p>
              </div>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button 
                variant="outline" 
                onClick={handleEdit}
              >
                Edit Settings
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}