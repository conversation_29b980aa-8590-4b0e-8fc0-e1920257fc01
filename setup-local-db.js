import { execSync } from 'child_process';
import { existsSync } from 'fs';

console.log('🚀 Setting up local PostgreSQL database...\n');

// Check if Docker is installed
try {
  execSync('docker --version', { stdio: 'ignore' });
  console.log('✅ Docker is installed');
} catch (error) {
  console.error('❌ Docker is not installed or not running');
  console.log('Please install Docker Desktop from: https://www.docker.com/products/docker-desktop');
  process.exit(1);
}

// Check if docker-compose.yml exists
if (!existsSync('docker-compose.yml')) {
  console.error('❌ docker-compose.yml not found');
  process.exit(1);
}

try {
  console.log('🐳 Starting PostgreSQL container...');
  
  // Stop any existing container
  try {
    execSync('docker-compose down', { stdio: 'inherit' });
  } catch (error) {
    // Ignore if no container was running
  }
  
  // Start the PostgreSQL container
  execSync('docker-compose up -d', { stdio: 'inherit' });
  
  console.log('⏳ Waiting for PostgreSQL to be ready...');
  
  // Wait for PostgreSQL to be ready
  let retries = 30;
  while (retries > 0) {
    try {
      execSync('docker-compose exec -T postgres pg_isready -U admin -d aluminum_optimizer', { stdio: 'ignore' });
      break;
    } catch (error) {
      retries--;
      if (retries === 0) {
        throw new Error('PostgreSQL failed to start');
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('✅ PostgreSQL is ready!');
  
  console.log('📋 Running database migrations...');
  
  // Run Drizzle migrations to create tables
  execSync('npm run db:push', { stdio: 'inherit' });
  
  console.log('✅ Database schema created successfully!');
  
  console.log('\n🎯 Local PostgreSQL database is ready!');
  console.log('📊 Database URL: postgresql://admin:admin123@localhost:5432/aluminum_optimizer');
  console.log('🔧 You can connect using any PostgreSQL client');
  
  console.log('\n📝 Next steps:');
  console.log('1. Run: node migrate-data.js (to migrate data from Neon)');
  console.log('2. Run: npm run dev (to start the application)');
  
} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}
