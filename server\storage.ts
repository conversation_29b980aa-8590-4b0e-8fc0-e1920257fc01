import { 
  users, type User, type InsertUser,
  cutPieces, type <PERSON><PERSON>iece, type InsertCutPiece,
  projects, type Project, type InsertProject,
  profiles, type Profile, type InsertProfile,
  windowDesigns, type WindowDesign, type InsertWindowDesign,
  components, type Component, type InsertComponent,
  glassSpecifications, type GlassSpecification, type InsertGlassSpecification,
  windowCuttingLists, type WindowCuttingList, type InsertWindowCuttingList,
  windowProjects, type WindowProject, type InsertWindowProject,
  projectWindowItems, type ProjectWindowItem, type InsertProjectWindowItem,
  accessories, type Accessory, type InsertAccessory,
  componentAccessories, type ComponentAccessory, type InsertComponentAccessory,
  machiningOperations, type MachiningOperation, type InsertMachiningOperation,
  cncTools, type CncTool, type InsertCncTool,
  invoices, type Invoice, type InsertInvoice
} from "@shared/schema";
import { db } from "./db";
import { eq, and, isNull, desc, asc } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import { pool } from "./db";
import createMemoryStore from "memorystore";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getAllUsers(): Promise<User[]>;
  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;
  deleteUser(id: number): Promise<void>;
  updateStripeCustomerId(userId: number, stripeCustomerId: string): Promise<User | undefined>;
  updateUserStripeInfo(userId: number, stripeInfo: { stripeCustomerId: string, stripeSubscriptionId: string }): Promise<User | undefined>;
  
  // Cut pieces operations
  getCutPieces(userId?: number): Promise<CutPiece[]>;
  getCutPiece(id: number): Promise<CutPiece | undefined>;
  createCutPiece(cutPiece: InsertCutPiece): Promise<CutPiece>;
  updateCutPiece(id: number, cutPiece: Partial<InsertCutPiece>): Promise<CutPiece | undefined>;
  deleteCutPiece(id: number): Promise<void>;
  
  // Project operations
  getProjects(userId?: number): Promise<Project[]>;
  getProject(id: number): Promise<Project | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, project: Partial<InsertProject>): Promise<Project | undefined>;
  deleteProject(id: number): Promise<void>;
  
  // Profile operations
  getProfiles(userId?: number, category?: string): Promise<Profile[]>;
  getProfile(id: number): Promise<Profile | undefined>;
  createProfile(profile: InsertProfile): Promise<Profile>;
  updateProfile(id: number, profile: Partial<InsertProfile>): Promise<Profile | undefined>;
  deleteProfile(id: number): Promise<void>;
  
  // Window design operations
  getWindowDesigns(userId?: number, category?: string): Promise<WindowDesign[]>;
  getWindowDesign(id: number): Promise<WindowDesign | undefined>;
  createWindowDesign(windowDesign: InsertWindowDesign): Promise<WindowDesign>;
  updateWindowDesign(id: number, windowDesign: Partial<InsertWindowDesign>): Promise<WindowDesign | undefined>;
  deleteWindowDesign(id: number): Promise<void>;
  
  // Component operations
  getComponentsByWindowDesign(windowDesignId: number): Promise<Component[]>;
  getComponent(id: number): Promise<Component | undefined>;
  createComponent(component: InsertComponent): Promise<Component>;
  updateComponent(id: number, component: Partial<InsertComponent>): Promise<Component | undefined>;
  deleteComponent(id: number): Promise<void>;
  
  // Glass specification operations
  getGlassSpecificationsByWindowDesign(windowDesignId: number): Promise<GlassSpecification[]>;
  getGlassSpecification(id: number): Promise<GlassSpecification | undefined>;
  createGlassSpecification(glassSpecification: InsertGlassSpecification): Promise<GlassSpecification>;
  updateGlassSpecification(id: number, glassSpecification: Partial<InsertGlassSpecification>): Promise<GlassSpecification | undefined>;
  deleteGlassSpecification(id: number): Promise<void>;
  
  // Window cutting list operations
  getWindowCuttingLists(userId?: number): Promise<WindowCuttingList[]>;
  getWindowCuttingList(id: number): Promise<WindowCuttingList | undefined>;
  createWindowCuttingList(windowCuttingList: InsertWindowCuttingList): Promise<WindowCuttingList>;
  updateWindowCuttingList(id: number, windowCuttingList: Partial<InsertWindowCuttingList>): Promise<WindowCuttingList | undefined>;
  deleteWindowCuttingList(id: number): Promise<void>;
  
  // Window project operations
  getWindowProjects(userId?: number): Promise<WindowProject[]>;
  getWindowProject(id: number): Promise<WindowProject | undefined>;
  createWindowProject(windowProject: InsertWindowProject): Promise<WindowProject>;
  updateWindowProject(id: number, windowProject: Partial<InsertWindowProject>): Promise<WindowProject | undefined>;
  deleteWindowProject(id: number): Promise<void>;
  copyWindowProject(id: number, userId: number): Promise<WindowProject>;
  
  // Project window item operations
  getProjectWindowItems(projectId: number): Promise<ProjectWindowItem[]>;
  getProjectWindowItemsByDesignId(windowDesignId: number): Promise<ProjectWindowItem[]>;
  getProjectWindowItem(id: number): Promise<ProjectWindowItem | undefined>;
  createProjectWindowItem(projectWindowItem: InsertProjectWindowItem): Promise<ProjectWindowItem>;
  updateProjectWindowItem(id: number, projectWindowItem: Partial<InsertProjectWindowItem>): Promise<ProjectWindowItem | undefined>;
  deleteProjectWindowItem(id: number): Promise<void>;
  reorderProjectWindowItems(projectId: number, itemIds: number[]): Promise<void>;
  
  // Accessories operations
  getAccessories(userId?: number, type?: string, subtype?: string): Promise<Accessory[]>;
  getAccessory(id: number): Promise<Accessory | undefined>;
  createAccessory(accessory: InsertAccessory): Promise<Accessory>;
  updateAccessory(id: number, accessory: Partial<InsertAccessory>): Promise<Accessory | undefined>;
  deleteAccessory(id: number): Promise<void>;
  
  // Component accessories operations
  getComponentAccessories(componentId: number): Promise<ComponentAccessory[]>;
  getComponentAccessory(id: number): Promise<ComponentAccessory | undefined>;
  createComponentAccessory(componentAccessory: InsertComponentAccessory): Promise<ComponentAccessory>;
  updateComponentAccessory(id: number, componentAccessory: Partial<InsertComponentAccessory>): Promise<ComponentAccessory | undefined>;
  deleteComponentAccessory(id: number): Promise<void>;
  getAccessoriesByComponent(componentId: number): Promise<(ComponentAccessory & Accessory)[]>;
  
  // Machining operations
  getMachiningOperationsByComponent(componentId: number): Promise<MachiningOperation[]>;
  getMachiningOperation(id: number): Promise<MachiningOperation | undefined>;
  createMachiningOperation(machiningOperation: InsertMachiningOperation): Promise<MachiningOperation>;
  updateMachiningOperation(id: number, machiningOperation: Partial<InsertMachiningOperation>): Promise<MachiningOperation | undefined>;
  deleteMachiningOperation(id: number): Promise<void>;
  
  // CNC tools operations
  getCncTools(userId?: number, type?: string): Promise<CncTool[]>;
  getCncTool(id: number): Promise<CncTool | undefined>;
  createCncTool(cncTool: InsertCncTool): Promise<CncTool>;
  updateCncTool(id: number, cncTool: Partial<InsertCncTool>): Promise<CncTool | undefined>;
  deleteCncTool(id: number): Promise<void>;
  
  // Invoice operations
  getInvoices(userId?: number): Promise<Invoice[]>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  updateInvoice(id: number, invoice: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  deleteInvoice(id: number): Promise<void>;
  getLatestInvoice(userId: number): Promise<Invoice | undefined>;
  
  // Session store
  sessionStore: session.Store;
}

// PostgreSQL database storage implementation
export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;
  
  constructor() {
    const PostgresSessionStore = connectPg(session);
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
  }
  
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }
  
  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }
  
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users);
  }
  
  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const [updated] = await db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();
    return updated;
  }
  
  async updateStripeCustomerId(userId: number, stripeCustomerId: string): Promise<User | undefined> {
    const [updated] = await db
      .update(users)
      .set({ stripeCustomerId })
      .where(eq(users.id, userId))
      .returning();
    return updated;
  }
  
  async updateUserStripeInfo(userId: number, stripeInfo: { stripeCustomerId: string, stripeSubscriptionId: string }): Promise<User | undefined> {
    const [updated] = await db
      .update(users)
      .set({
        stripeCustomerId: stripeInfo.stripeCustomerId,
        stripeSubscriptionId: stripeInfo.stripeSubscriptionId,
        subscriptionPlan: 'pro',
        maxQuantity: 999999,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      })
      .where(eq(users.id, userId))
      .returning();
    return updated;
  }
  
  async deleteUser(id: number): Promise<void> {
    // First fetch user's resources to identify related data to delete
    const userCutPieces = await this.getCutPieces(id);
    const userProjects = await this.getProjects(id);
    const userProfiles = await this.getProfiles(id);
    const userWindowDesigns = await this.getWindowDesigns(id);
    const userWindowProjects = await this.getWindowProjects(id);
    const userAccessories = await this.getAccessories(id);
    const userCncTools = await this.getCncTools(id);
    
    // Delete all dependent data first (in reverse order to avoid foreign key constraints)
    
    // Delete window project items
    for (const windowProject of userWindowProjects) {
      const projectItems = await this.getProjectWindowItems(windowProject.id);
      for (const item of projectItems) {
        await this.deleteProjectWindowItem(item.id);
      }
    }
    
    // Delete window projects
    for (const windowProject of userWindowProjects) {
      await this.deleteWindowProject(windowProject.id);
    }
    
    // Delete window cutting lists
    const userWindowCuttingLists = await this.getWindowCuttingLists(id);
    for (const list of userWindowCuttingLists) {
      await this.deleteWindowCuttingList(list.id);
    }
    
    // Delete component accessories and machining operations
    for (const windowDesign of userWindowDesigns) {
      const components = await this.getComponentsByWindowDesign(windowDesign.id);
      for (const component of components) {
        // Delete component accessories
        const compAccessories = await this.getComponentAccessories(component.id);
        for (const accessory of compAccessories) {
          await this.deleteComponentAccessory(accessory.id);
        }
        
        // Delete machining operations
        const operations = await this.getMachiningOperationsByComponent(component.id);
        for (const operation of operations) {
          await this.deleteMachiningOperation(operation.id);
        }
      }
      
      // Delete components and glass specs
      for (const component of await this.getComponentsByWindowDesign(windowDesign.id)) {
        await this.deleteComponent(component.id);
      }
      
      for (const glass of await this.getGlassSpecificationsByWindowDesign(windowDesign.id)) {
        await this.deleteGlassSpecification(glass.id);
      }
    }
    
    // Delete window designs
    for (const windowDesign of userWindowDesigns) {
      await this.deleteWindowDesign(windowDesign.id);
    }
    
    // Delete profiles
    for (const profile of userProfiles) {
      await this.deleteProfile(profile.id);
    }
    
    // Delete projects and cut pieces
    for (const project of userProjects) {
      await this.deleteProject(project.id);
    }
    
    for (const cutPiece of userCutPieces) {
      await this.deleteCutPiece(cutPiece.id);
    }
    
    // Delete accessories
    for (const accessory of userAccessories) {
      await this.deleteAccessory(accessory.id);
    }
    
    // Delete CNC tools
    for (const tool of userCncTools) {
      await this.deleteCncTool(tool.id);
    }
    
    // Finally, delete the user
    await db.delete(users).where(eq(users.id, id));
  }
  
  // Cut pieces operations
  async getCutPieces(userId?: number): Promise<CutPiece[]> {
    if (userId) {
      return await db.select().from(cutPieces).where(eq(cutPieces.userId, userId));
    }
    return await db.select().from(cutPieces);
  }
  
  async getCutPiece(id: number): Promise<CutPiece | undefined> {
    const [cutPiece] = await db.select().from(cutPieces).where(eq(cutPieces.id, id));
    return cutPiece;
  }
  
  async createCutPiece(insertCutPiece: InsertCutPiece): Promise<CutPiece> {
    const [cutPiece] = await db.insert(cutPieces).values(insertCutPiece).returning();
    return cutPiece;
  }
  
  async updateCutPiece(id: number, updateData: Partial<InsertCutPiece>): Promise<CutPiece | undefined> {
    const [updated] = await db
      .update(cutPieces)
      .set(updateData)
      .where(eq(cutPieces.id, id))
      .returning();
    return updated;
  }
  
  async deleteCutPiece(id: number): Promise<void> {
    await db.delete(cutPieces).where(eq(cutPieces.id, id));
  }
  
  // Project operations
  async getProjects(userId?: number): Promise<Project[]> {
    if (userId) {
      return await db.select().from(projects).where(eq(projects.userId, userId));
    }
    return await db.select().from(projects);
  }
  
  async getProject(id: number): Promise<Project | undefined> {
    const [project] = await db.select().from(projects).where(eq(projects.id, id));
    return project;
  }
  
  async createProject(insertProject: InsertProject): Promise<Project> {
    const [project] = await db.insert(projects).values(insertProject).returning();
    return project;
  }
  
  async updateProject(id: number, updateData: Partial<InsertProject>): Promise<Project | undefined> {
    const [updated] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.id, id))
      .returning();
    return updated;
  }
  
  async deleteProject(id: number): Promise<void> {
    await db.delete(projects).where(eq(projects.id, id));
  }

  // Profile operations
  async getProfiles(userId?: number, category?: string): Promise<Profile[]> {
    let conditions = [];
    
    if (userId) {
      conditions.push(eq(profiles.userId, userId));
    }
    
    if (category) {
      conditions.push(eq(profiles.category, category));
    }
    
    if (conditions.length > 0) {
      return await db
        .select()
        .from(profiles)
        .where(and(...conditions))
        .orderBy({ name: 'asc' });
    }
    
    return await db
      .select()
      .from(profiles)
      .orderBy({ name: 'asc' });
  }
  
  async getProfile(id: number): Promise<Profile | undefined> {
    const [profile] = await db.select().from(profiles).where(eq(profiles.id, id));
    return profile;
  }
  
  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    const [profile] = await db.insert(profiles).values(insertProfile).returning();
    return profile;
  }
  
  async updateProfile(id: number, updateData: Partial<InsertProfile>): Promise<Profile | undefined> {
    const [updated] = await db
      .update(profiles)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(profiles.id, id))
      .returning();
    return updated;
  }
  
  async deleteProfile(id: number): Promise<void> {
    await db.delete(profiles).where(eq(profiles.id, id));
  }
  
  // Window design operations
  async getWindowDesigns(userId?: number, category?: string): Promise<WindowDesign[]> {
    let conditions = [];
    
    if (userId) {
      conditions.push(eq(windowDesigns.userId, userId));
    }
    
    if (category) {
      conditions.push(eq(windowDesigns.category, category));
    }
    
    if (conditions.length > 0) {
      return await db
        .select()
        .from(windowDesigns)
        .where(and(...conditions))
        .orderBy({ name: 'asc' });
    }
    
    return await db
      .select()
      .from(windowDesigns)
      .orderBy({ name: 'asc' });
  }
  
  async getWindowDesign(id: number): Promise<WindowDesign | undefined> {
    const [windowDesign] = await db.select().from(windowDesigns).where(eq(windowDesigns.id, id));
    return windowDesign;
  }
  
  async createWindowDesign(insertWindowDesign: InsertWindowDesign): Promise<WindowDesign> {
    const [windowDesign] = await db.insert(windowDesigns).values(insertWindowDesign).returning();
    return windowDesign;
  }
  
  async updateWindowDesign(id: number, updateData: Partial<InsertWindowDesign>): Promise<WindowDesign | undefined> {
    const [updated] = await db
      .update(windowDesigns)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(windowDesigns.id, id))
      .returning();
    return updated;
  }
  
  async deleteWindowDesign(id: number): Promise<void> {
    // Step 1: Delete all components associated with this window design
    await db.delete(components).where(eq(components.windowDesignId, id));
    
    // Step 2: Delete all glass specifications associated with this window design
    await db.delete(glassSpecifications).where(eq(glassSpecifications.windowDesignId, id));
    
    // Step 3: Delete all project window items associated with this window design
    await db.delete(projectWindowItems).where(eq(projectWindowItems.windowDesignId, id));
    
    // Step 4: Delete all window cutting lists associated with this window design
    await db.delete(windowCuttingLists).where(eq(windowCuttingLists.windowDesignId, id));
    
    // Step 5: Finally delete the window design itself
    await db.delete(windowDesigns).where(eq(windowDesigns.id, id));
  }
  
  // Component operations
  async getComponentsByWindowDesign(windowDesignId: number): Promise<Component[]> {
    return await db
      .select()
      .from(components)
      .where(eq(components.windowDesignId, windowDesignId))
      .orderBy({ name: 'asc' });
  }
  
  async getComponent(id: number): Promise<Component | undefined> {
    const [component] = await db.select().from(components).where(eq(components.id, id));
    return component;
  }
  
  async createComponent(insertComponent: InsertComponent): Promise<Component> {
    const [component] = await db.insert(components).values(insertComponent).returning();
    return component;
  }
  
  async updateComponent(id: number, updateData: Partial<InsertComponent>): Promise<Component | undefined> {
    const [updated] = await db
      .update(components)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(components.id, id))
      .returning();
    return updated;
  }
  
  async deleteComponent(id: number): Promise<void> {
    // First delete related component accessories
    await db.delete(componentAccessories).where(eq(componentAccessories.componentId, id));
    
    // Delete related machining operations
    await db.delete(machiningOperations).where(eq(machiningOperations.componentId, id));
    
    // Then delete the component
    await db.delete(components).where(eq(components.id, id));
  }
  
  // Glass specification operations
  async getGlassSpecificationsByWindowDesign(windowDesignId: number): Promise<GlassSpecification[]> {
    return await db
      .select()
      .from(glassSpecifications)
      .where(eq(glassSpecifications.windowDesignId, windowDesignId))
      .orderBy({ name: 'asc' });
  }
  
  async getGlassSpecification(id: number): Promise<GlassSpecification | undefined> {
    const [glassSpec] = await db.select().from(glassSpecifications).where(eq(glassSpecifications.id, id));
    return glassSpec;
  }
  
  async createGlassSpecification(insertGlassSpec: InsertGlassSpecification): Promise<GlassSpecification> {
    const [glassSpec] = await db.insert(glassSpecifications).values(insertGlassSpec).returning();
    return glassSpec;
  }
  
  async updateGlassSpecification(id: number, updateData: Partial<InsertGlassSpecification>): Promise<GlassSpecification | undefined> {
    const [updated] = await db
      .update(glassSpecifications)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(glassSpecifications.id, id))
      .returning();
    return updated;
  }
  
  async deleteGlassSpecification(id: number): Promise<void> {
    await db.delete(glassSpecifications).where(eq(glassSpecifications.id, id));
  }
  
  // Window cutting list operations
  async getWindowCuttingLists(userId?: number): Promise<WindowCuttingList[]> {
    if (userId) {
      return await db
        .select()
        .from(windowCuttingLists)
        .where(eq(windowCuttingLists.userId, userId))
        .orderBy(desc(windowCuttingLists.createdAt));
    }
    return await db
      .select()
      .from(windowCuttingLists)
      .orderBy(desc(windowCuttingLists.createdAt));
  }
  
  async getWindowCuttingList(id: number): Promise<WindowCuttingList | undefined> {
    const [list] = await db.select().from(windowCuttingLists).where(eq(windowCuttingLists.id, id));
    return list;
  }
  
  async createWindowCuttingList(insertList: InsertWindowCuttingList): Promise<WindowCuttingList> {
    const [list] = await db.insert(windowCuttingLists).values(insertList).returning();
    return list;
  }
  
  async updateWindowCuttingList(id: number, updateData: Partial<InsertWindowCuttingList>): Promise<WindowCuttingList | undefined> {
    const [updated] = await db
      .update(windowCuttingLists)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(windowCuttingLists.id, id))
      .returning();
    return updated;
  }
  
  async deleteWindowCuttingList(id: number): Promise<void> {
    await db.delete(windowCuttingLists).where(eq(windowCuttingLists.id, id));
  }
  
  // Window project operations
  async getWindowProjects(userId?: number): Promise<WindowProject[]> {
    if (userId) {
      return await db
        .select()
        .from(windowProjects)
        .where(eq(windowProjects.userId, userId))
        .orderBy(desc(windowProjects.createdAt));
    }
    return await db
      .select()
      .from(windowProjects)
      .orderBy(desc(windowProjects.createdAt));
  }
  
  async getWindowProject(id: number): Promise<WindowProject | undefined> {
    const [project] = await db.select().from(windowProjects).where(eq(windowProjects.id, id));
    return project;
  }
  
  async createWindowProject(insertProject: InsertWindowProject): Promise<WindowProject> {
    const [project] = await db.insert(windowProjects).values(insertProject).returning();
    return project;
  }
  
  async updateWindowProject(id: number, updateData: Partial<InsertWindowProject>): Promise<WindowProject | undefined> {
    const [updated] = await db
      .update(windowProjects)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(windowProjects.id, id))
      .returning();
    return updated;
  }
  
  async deleteWindowProject(id: number): Promise<void> {
    // First delete all project window items
    await db.delete(projectWindowItems).where(eq(projectWindowItems.projectId, id));
    
    // Then delete the project
    await db.delete(windowProjects).where(eq(windowProjects.id, id));
  }
  
  async copyWindowProject(id: number, userId: number): Promise<WindowProject> {
    // 1. Get the original project
    const originalProject = await this.getWindowProject(id);
    if (!originalProject) {
      throw new Error("Project not found");
    }
    
    // 2. Create a new project with the same properties but a new name
    const newProjectData: InsertWindowProject = {
      name: `${originalProject.name} (Copy)`,
      description: originalProject.description,
      clientName: originalProject.clientName,
      clientContact: originalProject.clientContact,
      location: originalProject.location,
      dueDate: originalProject.dueDate,
      userId: userId,
      status: originalProject.status,
      notes: originalProject.notes
    };
    
    const newProject = await this.createWindowProject(newProjectData);
    
    // 3. Get all items from the original project
    const originalItems = await this.getProjectWindowItems(id);
    
    // 4. Create copies of all items in the new project
    for (const item of originalItems) {
      const newItemData: InsertProjectWindowItem = {
        projectId: newProject.id,
        windowDesignId: item.windowDesignId,
        windowCuttingListId: item.windowCuttingListId,
        width: item.width,
        height: item.height,
        quantity: item.quantity,
        windowType: item.windowType,
        position: item.position,
        notes: item.notes
      };
      
      await this.createProjectWindowItem(newItemData);
    }
    
    return newProject;
  }
  
  // Project window item operations
  async getProjectWindowItems(projectId: number): Promise<ProjectWindowItem[]> {
    return await db
      .select()
      .from(projectWindowItems)
      .where(eq(projectWindowItems.projectId, projectId))
      .orderBy({ orderIndex: 'asc' });
  }
  
  async getProjectWindowItemsByDesignId(windowDesignId: number): Promise<ProjectWindowItem[]> {
    return await db
      .select()
      .from(projectWindowItems)
      .where(eq(projectWindowItems.windowDesignId, windowDesignId));
  }
  
  async getProjectWindowItem(id: number): Promise<ProjectWindowItem | undefined> {
    const [item] = await db.select().from(projectWindowItems).where(eq(projectWindowItems.id, id));
    return item;
  }
  
  async createProjectWindowItem(insertItem: InsertProjectWindowItem): Promise<ProjectWindowItem> {
    // Get current max order index
    const items = await this.getProjectWindowItems(insertItem.projectId);
    const maxOrderIndex = items.length > 0 ? Math.max(...items.map(item => item.orderIndex || 0)) : -1;
    
    // Set order index to one more than current max
    const itemWithOrder = {
      ...insertItem,
      orderIndex: maxOrderIndex + 1
    };
    
    const [item] = await db.insert(projectWindowItems).values(itemWithOrder).returning();
    return item;
  }
  
  async updateProjectWindowItem(id: number, updateData: Partial<InsertProjectWindowItem>): Promise<ProjectWindowItem | undefined> {
    const [updated] = await db
      .update(projectWindowItems)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(projectWindowItems.id, id))
      .returning();
    return updated;
  }
  
  async deleteProjectWindowItem(id: number): Promise<void> {
    await db.delete(projectWindowItems).where(eq(projectWindowItems.id, id));
  }
  
  async reorderProjectWindowItems(projectId: number, itemIds: number[]): Promise<void> {
    for (let i = 0; i < itemIds.length; i++) {
      await db
        .update(projectWindowItems)
        .set({ orderIndex: i })
        .where(and(
          eq(projectWindowItems.id, itemIds[i]),
          eq(projectWindowItems.projectId, projectId)
        ));
    }
  }
  
  // Accessories operations
  async getAccessories(userId?: number, type?: string, subtype?: string): Promise<Accessory[]> {
    let conditions = [];
    
    if (userId) {
      conditions.push(eq(accessories.userId, userId));
    }
    
    if (type) {
      conditions.push(eq(accessories.type, type));
    }
    
    if (subtype) {
      conditions.push(eq(accessories.subtype, subtype));
    }
    
    if (conditions.length > 0) {
      return await db
        .select()
        .from(accessories)
        .where(and(...conditions))
        .orderBy({ name: 'asc' });
    }
    
    return await db
      .select()
      .from(accessories)
      .orderBy({ name: 'asc' });
  }
  
  async getAccessory(id: number): Promise<Accessory | undefined> {
    const [accessory] = await db.select().from(accessories).where(eq(accessories.id, id));
    return accessory;
  }
  
  async createAccessory(insertAccessory: InsertAccessory): Promise<Accessory> {
    const [accessory] = await db.insert(accessories).values(insertAccessory).returning();
    return accessory;
  }
  
  async updateAccessory(id: number, updateData: Partial<InsertAccessory>): Promise<Accessory | undefined> {
    const [updated] = await db
      .update(accessories)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(accessories.id, id))
      .returning();
    return updated;
  }
  
  async deleteAccessory(id: number): Promise<void> {
    // First delete related component accessories
    await db.delete(componentAccessories).where(eq(componentAccessories.accessoryId, id));
    
    // Then delete the accessory
    await db.delete(accessories).where(eq(accessories.id, id));
  }
  
  // Component accessories operations
  async getComponentAccessories(componentId: number): Promise<ComponentAccessory[]> {
    return await db
      .select()
      .from(componentAccessories)
      .where(eq(componentAccessories.componentId, componentId));
  }
  
  async getComponentAccessory(id: number): Promise<ComponentAccessory | undefined> {
    const [ca] = await db.select().from(componentAccessories).where(eq(componentAccessories.id, id));
    return ca;
  }
  
  async createComponentAccessory(insertCA: InsertComponentAccessory): Promise<ComponentAccessory> {
    const [ca] = await db.insert(componentAccessories).values(insertCA).returning();
    return ca;
  }
  
  async updateComponentAccessory(id: number, updateData: Partial<InsertComponentAccessory>): Promise<ComponentAccessory | undefined> {
    const [updated] = await db
      .update(componentAccessories)
      .set(updateData)
      .where(eq(componentAccessories.id, id))
      .returning();
    return updated;
  }
  
  async deleteComponentAccessory(id: number): Promise<void> {
    await db.delete(componentAccessories).where(eq(componentAccessories.id, id));
  }
  
  async getAccessoriesByComponent(componentId: number): Promise<(ComponentAccessory & Accessory)[]> {
    // Join componentAccessories with accessories
    const joined = await db
      .select({
        ...componentAccessories,
        ...accessories
      })
      .from(componentAccessories)
      .innerJoin(accessories, eq(componentAccessories.accessoryId, accessories.id))
      .where(eq(componentAccessories.componentId, componentId));
    
    return joined as (ComponentAccessory & Accessory)[];
  }
  
  // Machining operations
  async getMachiningOperationsByComponent(componentId: number): Promise<MachiningOperation[]> {
    return await db
      .select()
      .from(machiningOperations)
      .where(eq(machiningOperations.componentId, componentId))
      .orderBy({ positionFormula: 'asc' });
  }
  
  async getMachiningOperation(id: number): Promise<MachiningOperation | undefined> {
    const [operation] = await db.select().from(machiningOperations).where(eq(machiningOperations.id, id));
    return operation;
  }
  
  async createMachiningOperation(insertOperation: InsertMachiningOperation): Promise<MachiningOperation> {
    const [operation] = await db.insert(machiningOperations).values(insertOperation).returning();
    return operation;
  }
  
  async updateMachiningOperation(id: number, updateData: Partial<InsertMachiningOperation>): Promise<MachiningOperation | undefined> {
    const [updated] = await db
      .update(machiningOperations)
      .set(updateData)
      .where(eq(machiningOperations.id, id))
      .returning();
    return updated;
  }
  
  async deleteMachiningOperation(id: number): Promise<void> {
    await db.delete(machiningOperations).where(eq(machiningOperations.id, id));
  }
  
  // CNC tools operations
  async getCncTools(userId?: number, type?: string): Promise<CncTool[]> {
    let conditions = [];
    
    if (userId) {
      conditions.push(eq(cncTools.userId, userId));
    }
    
    if (type) {
      conditions.push(eq(cncTools.type, type));
    }
    
    if (conditions.length > 0) {
      return await db
        .select()
        .from(cncTools)
        .where(and(...conditions))
        .orderBy({ name: 'asc' });
    }
    
    return await db
      .select()
      .from(cncTools)
      .orderBy({ name: 'asc' });
  }
  
  async getCncTool(id: number): Promise<CncTool | undefined> {
    const [tool] = await db.select().from(cncTools).where(eq(cncTools.id, id));
    return tool;
  }
  
  async createCncTool(insertTool: InsertCncTool): Promise<CncTool> {
    const [tool] = await db.insert(cncTools).values(insertTool).returning();
    return tool;
  }
  
  async updateCncTool(id: number, updateData: Partial<InsertCncTool>): Promise<CncTool | undefined> {
    const [updated] = await db
      .update(cncTools)
      .set(updateData)
      .where(eq(cncTools.id, id))
      .returning();
    return updated;
  }
  
  async deleteCncTool(id: number): Promise<void> {
    // First update any machining operations using this tool to null
    await db
      .update(machiningOperations)
      .set({ toolId: null })
      .where(eq(machiningOperations.toolId, id));
    
    // Then delete the tool
    await db.delete(cncTools).where(eq(cncTools.id, id));
  }
  
  // Invoice operations
  async getInvoices(userId?: number): Promise<Invoice[]> {
    if (userId) {
      return await db
        .select()
        .from(invoices)
        .where(eq(invoices.userId, userId))
        .orderBy({ issuedDate: 'desc' });
    }
    return await db
      .select()
      .from(invoices)
      .orderBy({ issuedDate: 'desc' });
  }
  
  async getInvoice(id: number): Promise<Invoice | undefined> {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice;
  }
  
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    const [invoice] = await db.insert(invoices).values(insertInvoice).returning();
    return invoice;
  }
  
  async updateInvoice(id: number, updateData: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const [updated] = await db
      .update(invoices)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(invoices.id, id))
      .returning();
    return updated;
  }
  
  async deleteInvoice(id: number): Promise<void> {
    await db.delete(invoices).where(eq(invoices.id, id));
  }
  
  async getLatestInvoice(userId: number): Promise<Invoice | undefined> {
    const [invoice] = await db
      .select()
      .from(invoices)
      .where(eq(invoices.userId, userId))
      .orderBy({ issuedDate: 'desc' })
      .limit(1);
    return invoice;
  }
}

// In-memory storage implementation for development
const MemoryStore = createMemoryStore(session);

export class MemStorage implements IStorage {
  private users: User[] = [];
  private cutPieces: CutPiece[] = [];
  private projects: Project[] = [];
  private profiles: Profile[] = [];
  private windowDesigns: WindowDesign[] = [];
  private components: Component[] = [];
  private glassSpecifications: GlassSpecification[] = [];
  private windowCuttingLists: WindowCuttingList[] = [];
  private windowProjects: WindowProject[] = [];
  private projectWindowItems: ProjectWindowItem[] = [];
  private accessories: Accessory[] = [];
  private componentAccessories: ComponentAccessory[] = [];
  private machiningOperations: MachiningOperation[] = [];
  private cncTools: CncTool[] = [];
  private invoices: Invoice[] = [];
  sessionStore: session.Store;
  
  constructor() {
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000
    });
  }
  
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.find(user => user.id === id);
  }
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.users.find(user => user.username === username);
  }
  
  async createUser(insertUser: InsertUser): Promise<User> {
    const newId = this.users.length > 0 ? Math.max(...this.users.map(u => u.id)) + 1 : 1;
    const user: User = {
      id: newId,
      ...insertUser,
      role: insertUser.role || 'user',
      subscriptionPlan: insertUser.subscriptionPlan || 'free',
      maxQuantity: insertUser.maxQuantity || 50,
      subscriptionExpiry: null,
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      companyName: null,
      country: null,
      mobileNumber: null,
      accountStatus: 'active',
      statusNote: null,
      passwordChanged: true,
      email: insertUser.email || null,
      name: insertUser.name || null
    };
    
    this.users.push(user);
    return user;
  }
  
  async getAllUsers(): Promise<User[]> {
    return [...this.users];
  }
  
  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) return undefined;
    
    const updatedUser = { ...this.users[index], ...userData };
    this.users[index] = updatedUser;
    return updatedUser;
  }
  
  async updateStripeCustomerId(userId: number, stripeCustomerId: string): Promise<User | undefined> {
    return this.updateUser(userId, { stripeCustomerId });
  }
  
  async updateUserStripeInfo(userId: number, stripeInfo: { stripeCustomerId: string, stripeSubscriptionId: string }): Promise<User | undefined> {
    return this.updateUser(userId, {
      stripeCustomerId: stripeInfo.stripeCustomerId,
      stripeSubscriptionId: stripeInfo.stripeSubscriptionId,
      subscriptionPlan: 'pro',
      maxQuantity: 999999,
      subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  async deleteUser(id: number): Promise<void> {
    this.users = this.users.filter(user => user.id !== id);
    this.cutPieces = this.cutPieces.filter(cp => cp.userId !== id);
    this.projects = this.projects.filter(p => p.userId !== id);
    this.profiles = this.profiles.filter(p => p.userId !== id);
    this.windowDesigns = this.windowDesigns.filter(wd => wd.userId !== id);
    this.windowCuttingLists = this.windowCuttingLists.filter(wcl => wcl.userId !== id);
    this.windowProjects = this.windowProjects.filter(wp => wp.userId !== id);
    this.accessories = this.accessories.filter(a => a.userId !== id);
    this.cncTools = this.cncTools.filter(ct => ct.userId !== id);
    this.invoices = this.invoices.filter(i => i.userId !== id);
  }
  
  // Cut pieces operations
  async getCutPieces(userId?: number): Promise<CutPiece[]> {
    if (userId) {
      return this.cutPieces.filter(cp => cp.userId === userId);
    }
    return [...this.cutPieces];
  }
  
  async getCutPiece(id: number): Promise<CutPiece | undefined> {
    return this.cutPieces.find(cp => cp.id === id);
  }
  
  async createCutPiece(insertCutPiece: InsertCutPiece): Promise<CutPiece> {
    const newId = this.cutPieces.length > 0 ? Math.max(...this.cutPieces.map(cp => cp.id)) + 1 : 1;
    const cutPiece: CutPiece = {
      id: newId,
      ...insertCutPiece,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.cutPieces.push(cutPiece);
    return cutPiece;
  }
  
  async updateCutPiece(id: number, updateData: Partial<InsertCutPiece>): Promise<CutPiece | undefined> {
    const index = this.cutPieces.findIndex(cp => cp.id === id);
    if (index === -1) return undefined;
    
    const updatedCutPiece = { 
      ...this.cutPieces[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.cutPieces[index] = updatedCutPiece;
    return updatedCutPiece;
  }
  
  async deleteCutPiece(id: number): Promise<void> {
    this.cutPieces = this.cutPieces.filter(cp => cp.id !== id);
  }
  
  // Project operations
  async getProjects(userId?: number): Promise<Project[]> {
    if (userId) {
      return this.projects.filter(p => p.userId === userId);
    }
    return [...this.projects];
  }
  
  async getProject(id: number): Promise<Project | undefined> {
    return this.projects.find(p => p.id === id);
  }
  
  async createProject(insertProject: InsertProject): Promise<Project> {
    const newId = this.projects.length > 0 ? Math.max(...this.projects.map(p => p.id)) + 1 : 1;
    const project: Project = {
      id: newId,
      ...insertProject,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.projects.push(project);
    return project;
  }
  
  async updateProject(id: number, updateData: Partial<InsertProject>): Promise<Project | undefined> {
    const index = this.projects.findIndex(p => p.id === id);
    if (index === -1) return undefined;
    
    const updatedProject = { 
      ...this.projects[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.projects[index] = updatedProject;
    return updatedProject;
  }
  
  async deleteProject(id: number): Promise<void> {
    this.projects = this.projects.filter(p => p.id !== id);
  }
  
  // Profile operations
  async getProfiles(userId?: number, category?: string): Promise<Profile[]> {
    let result = [...this.profiles];
    
    if (userId) {
      result = result.filter(p => p.userId === userId);
    }
    
    if (category) {
      result = result.filter(p => p.category === category);
    }
    
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getProfile(id: number): Promise<Profile | undefined> {
    return this.profiles.find(p => p.id === id);
  }
  
  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    const newId = this.profiles.length > 0 ? Math.max(...this.profiles.map(p => p.id)) + 1 : 1;
    const profile: Profile = {
      id: newId,
      ...insertProfile,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: insertProfile.description || null,
      userId: insertProfile.userId || null,
      manufacturer: insertProfile.manufacturer || null,
      profileCode: insertProfile.profileCode || null,
      imageUrl: insertProfile.imageUrl || null,
      thumbnailUrl: insertProfile.thumbnailUrl || null,
      technicalDetails: insertProfile.technicalDetails || null
    };
    
    this.profiles.push(profile);
    return profile;
  }
  
  async updateProfile(id: number, updateData: Partial<InsertProfile>): Promise<Profile | undefined> {
    const index = this.profiles.findIndex(p => p.id === id);
    if (index === -1) return undefined;
    
    const updatedProfile = { 
      ...this.profiles[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.profiles[index] = updatedProfile;
    return updatedProfile;
  }
  
  async deleteProfile(id: number): Promise<void> {
    this.profiles = this.profiles.filter(p => p.id !== id);
  }
  
  // Window design operations
  async getWindowDesigns(userId?: number, category?: string): Promise<WindowDesign[]> {
    let result = [...this.windowDesigns];
    
    if (userId) {
      result = result.filter(wd => wd.userId === userId);
    }
    
    if (category) {
      result = result.filter(wd => wd.category === category);
    }
    
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getWindowDesign(id: number): Promise<WindowDesign | undefined> {
    return this.windowDesigns.find(wd => wd.id === id);
  }
  
  async createWindowDesign(insertWindowDesign: InsertWindowDesign): Promise<WindowDesign> {
    const newId = this.windowDesigns.length > 0 ? Math.max(...this.windowDesigns.map(wd => wd.id)) + 1 : 1;
    const windowDesign: WindowDesign = {
      id: newId,
      ...insertWindowDesign,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: insertWindowDesign.description || null,
      userId: insertWindowDesign.userId || null,
      width: insertWindowDesign.width || null,
      height: insertWindowDesign.height || null,
      imageUrl: insertWindowDesign.imageUrl || null,
      thumbnailUrl: insertWindowDesign.thumbnailUrl || null,
      customProperties: insertWindowDesign.customProperties || null,
      isPublic: insertWindowDesign.isPublic || false,
      defaultGlassType: insertWindowDesign.defaultGlassType || null,
      showDimensions: insertWindowDesign.showDimensions || false
    };
    
    this.windowDesigns.push(windowDesign);
    return windowDesign;
  }
  
  async updateWindowDesign(id: number, updateData: Partial<InsertWindowDesign>): Promise<WindowDesign | undefined> {
    const index = this.windowDesigns.findIndex(wd => wd.id === id);
    if (index === -1) return undefined;
    
    const updatedWindowDesign = { 
      ...this.windowDesigns[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.windowDesigns[index] = updatedWindowDesign;
    return updatedWindowDesign;
  }
  
  async deleteWindowDesign(id: number): Promise<void> {
    // First delete all components and glass specifications associated with this window design
    this.components = this.components.filter(c => c.windowDesignId !== id);
    this.glassSpecifications = this.glassSpecifications.filter(gs => gs.windowDesignId !== id);
    this.windowCuttingLists = this.windowCuttingLists.filter(wcl => wcl.windowDesignId !== id);
    this.projectWindowItems = this.projectWindowItems.filter(pwi => pwi.windowDesignId !== id);
    
    // Then delete the window design
    this.windowDesigns = this.windowDesigns.filter(wd => wd.id !== id);
  }
  
  // Component operations
  async getComponentsByWindowDesign(windowDesignId: number): Promise<Component[]> {
    return this.components
      .filter(c => c.windowDesignId === windowDesignId)
      .sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getComponent(id: number): Promise<Component | undefined> {
    return this.components.find(c => c.id === id);
  }
  
  async createComponent(insertComponent: InsertComponent): Promise<Component> {
    const newId = this.components.length > 0 ? Math.max(...this.components.map(c => c.id)) + 1 : 1;
    const component: Component = {
      id: newId,
      ...insertComponent,
      createdAt: new Date(),
      updatedAt: new Date(),
      notes: insertComponent.notes || null,
      position: insertComponent.position || null,
      customProperties: insertComponent.customProperties || null,
      isFixedHeight: insertComponent.isFixedHeight || false,
      fixedHeightValue: insertComponent.fixedHeightValue || null
    };
    
    this.components.push(component);
    return component;
  }
  
  async updateComponent(id: number, updateData: Partial<InsertComponent>): Promise<Component | undefined> {
    const index = this.components.findIndex(c => c.id === id);
    if (index === -1) return undefined;
    
    const updatedComponent = { 
      ...this.components[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.components[index] = updatedComponent;
    return updatedComponent;
  }
  
  async deleteComponent(id: number): Promise<void> {
    // First delete related component accessories and machining operations
    this.componentAccessories = this.componentAccessories.filter(ca => ca.componentId !== id);
    this.machiningOperations = this.machiningOperations.filter(mo => mo.componentId !== id);
    
    // Then delete the component
    this.components = this.components.filter(c => c.id !== id);
  }
  
  // Glass specification operations
  async getGlassSpecificationsByWindowDesign(windowDesignId: number): Promise<GlassSpecification[]> {
    return this.glassSpecifications
      .filter(gs => gs.windowDesignId === windowDesignId)
      .sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getGlassSpecification(id: number): Promise<GlassSpecification | undefined> {
    return this.glassSpecifications.find(gs => gs.id === id);
  }
  
  async createGlassSpecification(insertGlassSpec: InsertGlassSpecification): Promise<GlassSpecification> {
    const newId = this.glassSpecifications.length > 0 ? Math.max(...this.glassSpecifications.map(gs => gs.id)) + 1 : 1;
    const glassSpec: GlassSpecification = {
      id: newId,
      ...insertGlassSpec,
      createdAt: new Date(),
      updatedAt: new Date(),
      notes: insertGlassSpec.notes || null,
      position: insertGlassSpec.position || null,
      customProperties: insertGlassSpec.customProperties || null
    };
    
    this.glassSpecifications.push(glassSpec);
    return glassSpec;
  }
  
  async updateGlassSpecification(id: number, updateData: Partial<InsertGlassSpecification>): Promise<GlassSpecification | undefined> {
    const index = this.glassSpecifications.findIndex(gs => gs.id === id);
    if (index === -1) return undefined;
    
    const updatedGlassSpec = { 
      ...this.glassSpecifications[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.glassSpecifications[index] = updatedGlassSpec;
    return updatedGlassSpec;
  }
  
  async deleteGlassSpecification(id: number): Promise<void> {
    this.glassSpecifications = this.glassSpecifications.filter(gs => gs.id !== id);
  }
  
  // Window cutting list operations
  async getWindowCuttingLists(userId?: number): Promise<WindowCuttingList[]> {
    if (userId) {
      return this.windowCuttingLists
        .filter(wcl => wcl.userId === userId)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    
    return [...this.windowCuttingLists]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  async getWindowCuttingList(id: number): Promise<WindowCuttingList | undefined> {
    return this.windowCuttingLists.find(wcl => wcl.id === id);
  }
  
  async createWindowCuttingList(insertList: InsertWindowCuttingList): Promise<WindowCuttingList> {
    const newId = this.windowCuttingLists.length > 0 ? Math.max(...this.windowCuttingLists.map(wcl => wcl.id)) + 1 : 1;
    const list: WindowCuttingList = {
      id: newId,
      ...insertList,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.windowCuttingLists.push(list);
    return list;
  }
  
  async updateWindowCuttingList(id: number, updateData: Partial<InsertWindowCuttingList>): Promise<WindowCuttingList | undefined> {
    const index = this.windowCuttingLists.findIndex(wcl => wcl.id === id);
    if (index === -1) return undefined;
    
    const updatedList = { 
      ...this.windowCuttingLists[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.windowCuttingLists[index] = updatedList;
    return updatedList;
  }
  
  async deleteWindowCuttingList(id: number): Promise<void> {
    this.windowCuttingLists = this.windowCuttingLists.filter(wcl => wcl.id !== id);
  }
  
  // Window project operations
  async getWindowProjects(userId?: number): Promise<WindowProject[]> {
    if (userId) {
      return this.windowProjects
        .filter(wp => wp.userId === userId)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    
    return [...this.windowProjects]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  async getWindowProject(id: number): Promise<WindowProject | undefined> {
    return this.windowProjects.find(wp => wp.id === id);
  }
  
  async createWindowProject(insertProject: InsertWindowProject): Promise<WindowProject> {
    const newId = this.windowProjects.length > 0 ? Math.max(...this.windowProjects.map(wp => wp.id)) + 1 : 1;
    const project: WindowProject = {
      id: newId,
      ...insertProject,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.windowProjects.push(project);
    return project;
  }
  
  async updateWindowProject(id: number, updateData: Partial<InsertWindowProject>): Promise<WindowProject | undefined> {
    const index = this.windowProjects.findIndex(wp => wp.id === id);
    if (index === -1) return undefined;
    
    const updatedProject = { 
      ...this.windowProjects[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.windowProjects[index] = updatedProject;
    return updatedProject;
  }
  
  async deleteWindowProject(id: number): Promise<void> {
    // First delete all project window items
    this.projectWindowItems = this.projectWindowItems.filter(pwi => pwi.projectId !== id);
    
    // Then delete the project
    this.windowProjects = this.windowProjects.filter(wp => wp.id !== id);
  }
  
  async copyWindowProject(id: number, userId: number): Promise<WindowProject> {
    // 1. Get the original project
    const originalProject = await this.getWindowProject(id);
    if (!originalProject) {
      throw new Error("Project not found");
    }
    
    // 2. Create a new project with the same properties but a new name
    const newProjectData: InsertWindowProject = {
      name: `${originalProject.name} (Copy)`,
      description: originalProject.description,
      clientName: originalProject.clientName,
      clientContact: originalProject.clientContact,
      location: originalProject.location,
      dueDate: originalProject.dueDate,
      userId: userId,
      status: originalProject.status,
      notes: originalProject.notes
    };
    
    const newProject = await this.createWindowProject(newProjectData);
    
    // 3. Get all items from the original project
    const originalItems = await this.getProjectWindowItems(id);
    
    // 4. Create copies of all items in the new project
    for (const item of originalItems) {
      const newItemData: InsertProjectWindowItem = {
        projectId: newProject.id,
        windowDesignId: item.windowDesignId,
        windowCuttingListId: item.windowCuttingListId,
        width: item.width,
        height: item.height,
        quantity: item.quantity,
        windowType: item.windowType,
        position: item.position,
        notes: item.notes
      };
      
      await this.createProjectWindowItem(newItemData);
    }
    
    return newProject;
  }
  
  // Project window item operations
  async getProjectWindowItems(projectId: number): Promise<ProjectWindowItem[]> {
    return this.projectWindowItems
      .filter(pwi => pwi.projectId === projectId)
      .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0));
  }
  
  async getProjectWindowItemsByDesignId(windowDesignId: number): Promise<ProjectWindowItem[]> {
    return this.projectWindowItems.filter(pwi => pwi.windowDesignId === windowDesignId);
  }
  
  async getProjectWindowItem(id: number): Promise<ProjectWindowItem | undefined> {
    return this.projectWindowItems.find(pwi => pwi.id === id);
  }
  
  async createProjectWindowItem(insertItem: InsertProjectWindowItem): Promise<ProjectWindowItem> {
    const newId = this.projectWindowItems.length > 0 ? Math.max(...this.projectWindowItems.map(pwi => pwi.id)) + 1 : 1;
    
    // Get current max order index
    const items = await this.getProjectWindowItems(insertItem.projectId);
    const maxOrderIndex = items.length > 0 ? Math.max(...items.map(item => item.orderIndex || 0)) : -1;
    
    const item: ProjectWindowItem = {
      id: newId,
      ...insertItem,
      orderIndex: maxOrderIndex + 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.projectWindowItems.push(item);
    return item;
  }
  
  async updateProjectWindowItem(id: number, updateData: Partial<InsertProjectWindowItem>): Promise<ProjectWindowItem | undefined> {
    const index = this.projectWindowItems.findIndex(pwi => pwi.id === id);
    if (index === -1) return undefined;
    
    const updatedItem = { 
      ...this.projectWindowItems[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.projectWindowItems[index] = updatedItem;
    return updatedItem;
  }
  
  async deleteProjectWindowItem(id: number): Promise<void> {
    this.projectWindowItems = this.projectWindowItems.filter(pwi => pwi.id !== id);
  }
  
  async reorderProjectWindowItems(projectId: number, itemIds: number[]): Promise<void> {
    for (let i = 0; i < itemIds.length; i++) {
      const index = this.projectWindowItems.findIndex(
        pwi => pwi.id === itemIds[i] && pwi.projectId === projectId
      );
      
      if (index !== -1) {
        this.projectWindowItems[index] = {
          ...this.projectWindowItems[index],
          orderIndex: i
        };
      }
    }
  }
  
  // Accessories operations
  async getAccessories(userId?: number, type?: string, subtype?: string): Promise<Accessory[]> {
    let result = [...this.accessories];
    
    if (userId) {
      result = result.filter(a => a.userId === userId);
    }
    
    if (type) {
      result = result.filter(a => a.type === type);
    }
    
    if (subtype) {
      result = result.filter(a => a.subtype === subtype);
    }
    
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getAccessory(id: number): Promise<Accessory | undefined> {
    return this.accessories.find(a => a.id === id);
  }
  
  async createAccessory(insertAccessory: InsertAccessory): Promise<Accessory> {
    const newId = this.accessories.length > 0 ? Math.max(...this.accessories.map(a => a.id)) + 1 : 1;
    const accessory: Accessory = {
      id: newId,
      ...insertAccessory,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: insertAccessory.description || null,
      userId: insertAccessory.userId || null,
      imageUrl: insertAccessory.imageUrl || null,
      thumbnailUrl: insertAccessory.thumbnailUrl || null,
      price: insertAccessory.price || 0,
      manufacturer: insertAccessory.manufacturer || null,
      techSpecs: insertAccessory.techSpecs || null
    };
    
    this.accessories.push(accessory);
    return accessory;
  }
  
  async updateAccessory(id: number, updateData: Partial<InsertAccessory>): Promise<Accessory | undefined> {
    const index = this.accessories.findIndex(a => a.id === id);
    if (index === -1) return undefined;
    
    const updatedAccessory = { 
      ...this.accessories[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.accessories[index] = updatedAccessory;
    return updatedAccessory;
  }
  
  async deleteAccessory(id: number): Promise<void> {
    // First delete related component accessories
    this.componentAccessories = this.componentAccessories.filter(ca => ca.accessoryId !== id);
    
    // Then delete the accessory
    this.accessories = this.accessories.filter(a => a.id !== id);
  }
  
  // Component accessories operations
  async getComponentAccessories(componentId: number): Promise<ComponentAccessory[]> {
    return this.componentAccessories.filter(ca => ca.componentId === componentId);
  }
  
  async getComponentAccessory(id: number): Promise<ComponentAccessory | undefined> {
    return this.componentAccessories.find(ca => ca.id === id);
  }
  
  async createComponentAccessory(insertCA: InsertComponentAccessory): Promise<ComponentAccessory> {
    const newId = this.componentAccessories.length > 0 ? Math.max(...this.componentAccessories.map(ca => ca.id)) + 1 : 1;
    const componentAccessory: ComponentAccessory = {
      id: newId,
      ...insertCA,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.componentAccessories.push(componentAccessory);
    return componentAccessory;
  }
  
  async updateComponentAccessory(id: number, updateData: Partial<InsertComponentAccessory>): Promise<ComponentAccessory | undefined> {
    const index = this.componentAccessories.findIndex(ca => ca.id === id);
    if (index === -1) return undefined;
    
    const updatedCA = { 
      ...this.componentAccessories[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.componentAccessories[index] = updatedCA;
    return updatedCA;
  }
  
  async deleteComponentAccessory(id: number): Promise<void> {
    this.componentAccessories = this.componentAccessories.filter(ca => ca.id !== id);
  }
  
  async getAccessoriesByComponent(componentId: number): Promise<(ComponentAccessory & Accessory)[]> {
    const result: (ComponentAccessory & Accessory)[] = [];
    
    const compAccessories = this.componentAccessories.filter(ca => ca.componentId === componentId);
    
    for (const ca of compAccessories) {
      const accessory = this.accessories.find(a => a.id === ca.accessoryId);
      if (accessory) {
        result.push({
          ...ca,
          ...accessory
        });
      }
    }
    
    return result;
  }
  
  // Machining operations
  async getMachiningOperationsByComponent(componentId: number): Promise<MachiningOperation[]> {
    return this.machiningOperations
      .filter(mo => mo.componentId === componentId)
      .sort((a, b) => a.positionFormula.localeCompare(b.positionFormula));
  }
  
  async getMachiningOperation(id: number): Promise<MachiningOperation | undefined> {
    return this.machiningOperations.find(mo => mo.id === id);
  }
  
  async createMachiningOperation(insertOperation: InsertMachiningOperation): Promise<MachiningOperation> {
    const newId = this.machiningOperations.length > 0 ? Math.max(...this.machiningOperations.map(mo => mo.id)) + 1 : 1;
    const operation: MachiningOperation = {
      id: newId,
      ...insertOperation,
      length: insertOperation.length || null,
      userId: insertOperation.userId || null,
      width: insertOperation.width || null,
      depth: insertOperation.depth || null,
      angle: insertOperation.angle || null,
      radius: insertOperation.radius || null,
      toolId: insertOperation.toolId || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.machiningOperations.push(operation);
    return operation;
  }
  
  async updateMachiningOperation(id: number, updateData: Partial<InsertMachiningOperation>): Promise<MachiningOperation | undefined> {
    const index = this.machiningOperations.findIndex(mo => mo.id === id);
    if (index === -1) return undefined;
    
    const updatedOperation = { 
      ...this.machiningOperations[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.machiningOperations[index] = updatedOperation;
    return updatedOperation;
  }
  
  async deleteMachiningOperation(id: number): Promise<void> {
    this.machiningOperations = this.machiningOperations.filter(mo => mo.id !== id);
  }
  
  // CNC tools operations
  async getCncTools(userId?: number, type?: string): Promise<CncTool[]> {
    let result = [...this.cncTools];
    
    if (userId) {
      result = result.filter(ct => ct.userId === userId);
    }
    
    if (type) {
      result = result.filter(ct => ct.type === type);
    }
    
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }
  
  async getCncTool(id: number): Promise<CncTool | undefined> {
    return this.cncTools.find(ct => ct.id === id);
  }
  
  async createCncTool(insertTool: InsertCncTool): Promise<CncTool> {
    const newId = this.cncTools.length > 0 ? Math.max(...this.cncTools.map(ct => ct.id)) + 1 : 1;
    const tool: CncTool = {
      id: newId,
      ...insertTool,
      length: insertTool.length || null,
      description: insertTool.description || null,
      userId: insertTool.userId || null,
      imageUrl: insertTool.imageUrl || null,
      parameters: insertTool.parameters || null,
      materialType: insertTool.materialType || null,
      speedRPM: insertTool.speedRPM || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.cncTools.push(tool);
    return tool;
  }
  
  async updateCncTool(id: number, updateData: Partial<InsertCncTool>): Promise<CncTool | undefined> {
    const index = this.cncTools.findIndex(ct => ct.id === id);
    if (index === -1) return undefined;
    
    const updatedTool = { 
      ...this.cncTools[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.cncTools[index] = updatedTool;
    return updatedTool;
  }
  
  async deleteCncTool(id: number): Promise<void> {
    // First update any machining operations using this tool to null
    for (let i = 0; i < this.machiningOperations.length; i++) {
      if (this.machiningOperations[i].toolId === id) {
        this.machiningOperations[i] = {
          ...this.machiningOperations[i],
          toolId: null
        };
      }
    }
    
    // Then delete the tool
    this.cncTools = this.cncTools.filter(ct => ct.id !== id);
  }
  
  // Invoice operations
  async getInvoices(userId?: number): Promise<Invoice[]> {
    if (userId) {
      return this.invoices
        .filter(i => i.userId === userId)
        .sort((a, b) => new Date(b.issuedDate).getTime() - new Date(a.issuedDate).getTime());
    }
    
    return [...this.invoices]
      .sort((a, b) => new Date(b.issuedDate).getTime() - new Date(a.issuedDate).getTime());
  }
  
  async getInvoice(id: number): Promise<Invoice | undefined> {
    return this.invoices.find(i => i.id === id);
  }
  
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    const newId = this.invoices.length > 0 ? Math.max(...this.invoices.map(i => i.id)) + 1 : 1;
    const invoice: Invoice = {
      id: newId,
      ...insertInvoice,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.invoices.push(invoice);
    return invoice;
  }
  
  async updateInvoice(id: number, updateData: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const index = this.invoices.findIndex(i => i.id === id);
    if (index === -1) return undefined;
    
    const updatedInvoice = { 
      ...this.invoices[index], 
      ...updateData,
      updatedAt: new Date()
    };
    
    this.invoices[index] = updatedInvoice;
    return updatedInvoice;
  }
  
  async deleteInvoice(id: number): Promise<void> {
    this.invoices = this.invoices.filter(i => i.id !== id);
  }
  
  async getLatestInvoice(userId: number): Promise<Invoice | undefined> {
    return this.invoices
      .filter(i => i.userId === userId)
      .sort((a, b) => new Date(b.issuedDate).getTime() - new Date(a.issuedDate).getTime())[0];
  }
}

// Use database storage if DATABASE_URL is set, otherwise use in-memory storage
// Create the appropriate storage instance based on environment and explicitly type as IStorage
// This ensures TypeScript knows all methods from the interface are available
export const storage: IStorage = process.env.DATABASE_URL 
  ? new DatabaseStorage() 
  : new MemStorage();