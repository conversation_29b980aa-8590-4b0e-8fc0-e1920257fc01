import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { 
  CrownIcon, 
  UserCogIcon, 
  AlertCircleIcon, 
  CheckCircleIcon, 
  BanIcon, 
  ClockIcon,
  Trash2Icon,
  UserXIcon,
  KeyIcon,
  ShieldIcon,
  CopyIcon
} from "lucide-react";
import { useLocation, Link } from "wouter";
import { apiRequest, queryClient } from "@/lib/queryClient";

interface User {
  id: number;
  username: string;
  name: string | null;
  email: string | null;
  companyName: string | null;
  mobileNumber: string | null;
  role: string;
  subscriptionPlan: string;
  maxQuantity: number;
  subscriptionExpiry: string | null;
  accountStatus?: string;
  statusNote?: string;
  paymentIssueDate?: string;
}

export default function AdminPage() {
  const { user: currentUser } = useAuth();
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [temporaryPassword, setTemporaryPassword] = useState<string | null>(null);
  const [newPlan, setNewPlan] = useState("free");
  const [newMaxQuantity, setNewMaxQuantity] = useState("50");
  const [newAccountStatus, setNewAccountStatus] = useState("active");
  const [statusNote, setStatusNote] = useState("");
  
  // Redirect if not admin using useEffect
  useEffect(() => {
    if (currentUser && currentUser.role !== 'admin') {
      setLocation("/");
    }
  }, [currentUser, setLocation]);
  
  // Return early if not admin or if redirecting
  if (currentUser && currentUser.role !== 'admin') {
    return null;
  }
  
  const { data: users, isLoading, error } = useQuery<User[]>({
    queryKey: ["/api/admin/users"],
    retry: false,
  });
  
  const updateSubscriptionMutation = useMutation({
    mutationFn: async ({ 
      userId, 
      plan, 
      quantity, 
      accountStatus, 
      statusNote 
    }: { 
      userId: number, 
      plan: string, 
      quantity: number,
      accountStatus: string,
      statusNote?: string 
    }) => {
      const response = await apiRequest("PUT", `/api/admin/users/${userId}/subscription`, {
        subscriptionPlan: plan,
        maxQuantity: quantity,
        accountStatus,
        statusNote
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update user subscription");
      }
      
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Subscription Updated",
        description: `Successfully updated subscription for ${selectedUser?.username}`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsUpdateDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  });
  
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("DELETE", `/api/admin/users/${userId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete user");
      }
      
      return await response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "User Deleted",
        description: `Successfully deleted user ${data.username} and all associated data`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsDeleteDialogOpen(false);
      setSelectedUser(null);
    },
    onError: (error) => {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  });
  
  const resetPasswordMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("POST", `/api/admin/users/${userId}/reset-password`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to reset password");
      }
      
      return await response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Password Reset",
        description: `Successfully reset password for ${selectedUser?.username}`,
      });
      setTemporaryPassword(data.temporaryPassword);
    },
    onError: (error) => {
      toast({
        title: "Password Reset Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
      setIsResetPasswordDialogOpen(false);
    }
  });
  
  const handleUpdateSubscription = (user: User) => {
    setSelectedUser(user);
    setNewPlan(user.subscriptionPlan);
    setNewMaxQuantity(user.maxQuantity.toString());
    setNewAccountStatus(user.accountStatus || 'active');
    setStatusNote(user.statusNote || '');
    setIsUpdateDialogOpen(true);
  };
  
  const handleSubmitSubscriptionUpdate = () => {
    if (!selectedUser) return;
    
    updateSubscriptionMutation.mutate({
      userId: selectedUser.id,
      plan: newPlan,
      quantity: parseInt(newMaxQuantity) || 50,
      accountStatus: newAccountStatus,
      statusNote: statusNote || undefined
    });
  };
  
  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };
  
  const confirmDeleteUser = () => {
    if (!selectedUser) return;
    deleteUserMutation.mutate(selectedUser.id);
  };
  
  const handleResetPassword = (user: User) => {
    setSelectedUser(user);
    setTemporaryPassword(null);
    setIsResetPasswordDialogOpen(true);
  };
  
  const confirmResetPassword = () => {
    if (!selectedUser) return;
    resetPasswordMutation.mutate(selectedUser.id);
  };
  
  if (!currentUser) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Admin Access Required</CardTitle>
            <CardDescription>Please log in with admin credentials.</CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setLocation("/auth")} className="w-full">
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Admin Panel</CardTitle>
            <CardDescription>Loading user data...</CardDescription>
          </CardHeader>
          <CardContent className="animate-pulse">
            <div className="h-64 bg-neutral-100 rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Admin Panel</CardTitle>
            <CardDescription className="text-red-500">
              Error loading user data. You may not have admin permissions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] })}>
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl flex items-center">
                <UserCogIcon className="mr-2 h-6 w-6" />
                Admin Panel
              </CardTitle>
              <CardDescription>
                Manage users and subscription plans
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                className="flex items-center gap-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700" 
                asChild
              >
                <Link href="/ai-window-analysis">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"/><path d="M3 15v4h16a2 2 0 0 0 0-4H3z"/></svg>
                  AI Window Analysis
                </Link>
              </Button>
              <Badge variant="outline" className="text-primary">
                {users?.length || 0} Users
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Username</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Mobile</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users && users.map((user) => (
                <TableRow key={user.id} className={user.accountStatus === 'suspended' ? 'bg-red-50' : ''}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.name || '-'}</TableCell>
                  <TableCell>{user.email || '-'}</TableCell>
                  <TableCell>{user.companyName || '-'}</TableCell>
                  <TableCell>{user.mobileNumber || '-'}</TableCell>
                  <TableCell>
                    <Badge variant={user.role === 'admin' ? 'default' : 'outline'}>
                      {user.role.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.subscriptionPlan !== 'free' ? 'default' : 'outline'}
                      className={
                        user.subscriptionPlan === 'pro' 
                          ? 'bg-amber-500' 
                          : user.subscriptionPlan === 'platinum'
                            ? 'bg-gradient-to-r from-indigo-500 to-purple-600'
                            : undefined
                      }
                    >
                      {user.subscriptionPlan === 'pro' ? (
                        <div className="flex items-center">
                          <CrownIcon className="w-3 h-3 mr-1" />
                          <span>PRO</span>
                        </div>
                      ) : user.subscriptionPlan === 'platinum' ? (
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>
                          <span>PLATINUM</span>
                        </div>
                      ) : (
                        <span>FREE</span>
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.accountStatus === 'suspended' ? (
                      <Badge variant="destructive" className="flex items-center gap-1">
                        <BanIcon className="w-3 h-3" />
                        Suspended
                      </Badge>
                    ) : user.accountStatus === 'pending_payment' ? (
                      <Badge variant="outline" className="text-amber-600 border-amber-600 flex items-center gap-1">
                        <ClockIcon className="w-3 h-3" />
                        Payment Due
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-green-600 border-green-600 flex items-center gap-1">
                        <CheckCircleIcon className="w-3 h-3" />
                        Active
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {user.subscriptionExpiry 
                      ? new Date(user.subscriptionExpiry).toLocaleDateString() 
                      : '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateSubscription(user)}
                        className={user.accountStatus === 'suspended' ? 'border-red-300' : ''}
                      >
                        {user.accountStatus === 'suspended' ? (
                          <>
                            <BanIcon className="w-3 h-3 mr-1 text-red-500" />
                            Manage Access
                          </>
                        ) : (
                          'Update Plan'
                        )}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResetPassword(user)}
                        className="border-blue-200 hover:border-blue-300 hover:bg-blue-50"
                      >
                        <KeyIcon className="w-3 h-3 mr-1 text-blue-500" />
                        Reset Password
                      </Button>
                      
                      {/* Only show delete button for non-admin users */}
                      {user.role !== 'admin' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteUser(user)}
                          className="border-red-200 hover:border-red-300 hover:bg-red-50"
                        >
                          <Trash2Icon className="w-3 h-3 mr-1 text-red-500" />
                          Delete
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              
              {(!users || users.length === 0) && (
                <TableRow>
                  <TableCell colSpan={11} className="text-center py-8 text-muted-foreground">
                    No users found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Subscription Plan</DialogTitle>
            <DialogDescription>
              {selectedUser 
                ? `Change subscription details for ${selectedUser.username}`
                : 'Select a user first'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="subscription-plan">Subscription Plan</Label>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={newPlan === 'free' ? 'default' : 'outline'}
                  className="flex-1"
                  onClick={() => setNewPlan('free')}
                >
                  Free
                </Button>
                <Button
                  type="button"
                  variant={newPlan === 'pro' ? 'default' : 'outline'}
                  className={`flex-1 ${newPlan === 'pro' ? 'bg-amber-500 hover:bg-amber-600' : ''}`}
                  onClick={() => setNewPlan('pro')}
                >
                  <CrownIcon className="w-4 h-4 mr-2" />
                  Pro
                </Button>
                <Button
                  type="button"
                  variant={newPlan === 'platinum' ? 'default' : 'outline'}
                  className={`flex-1 ${newPlan === 'platinum' ? 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700' : ''}`}
                  onClick={() => setNewPlan('platinum')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>
                  Platinum
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="account-status">Account Status</Label>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={newAccountStatus === 'active' ? 'default' : 'outline'}
                  className={`flex-1 ${newAccountStatus === 'active' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                  onClick={() => setNewAccountStatus('active')}
                >
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Active
                </Button>
                <Button
                  type="button"
                  variant={newAccountStatus === 'pending_payment' ? 'default' : 'outline'}
                  className={`flex-1 ${newAccountStatus === 'pending_payment' ? 'bg-amber-500 hover:bg-amber-600' : ''}`}
                  onClick={() => setNewAccountStatus('pending_payment')}
                >
                  <ClockIcon className="w-4 h-4 mr-2" />
                  Payment Due
                </Button>
                <Button
                  type="button"
                  variant={newAccountStatus === 'suspended' ? 'default' : 'outline'}
                  className={`flex-1 ${newAccountStatus === 'suspended' ? 'bg-red-600 hover:bg-red-700' : ''}`}
                  onClick={() => setNewAccountStatus('suspended')}
                >
                  <BanIcon className="w-4 h-4 mr-2" />
                  Suspend
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status-note">Note (visible to admins only)</Label>
              <Textarea
                id="status-note"
                placeholder="Add a note about this user's account status or payment issue"
                value={statusNote}
                onChange={(e) => setStatusNote(e.target.value)}
                rows={3}
              />
            </div>
            
            {newPlan === 'free' && (
              <div className="space-y-2">
                <Label htmlFor="max-quantity">Max Quantity Limit</Label>
                <Input
                  id="max-quantity"
                  type="number"
                  value={newMaxQuantity}
                  onChange={(e) => setNewMaxQuantity(e.target.value)}
                  min="1"
                  max="1000"
                />
              </div>
            )}
            
            {newPlan === 'pro' && (
              <div className="bg-neutral-50 p-3 rounded border text-sm space-y-2">
                <div className="flex items-center text-amber-600">
                  <CrownIcon className="w-4 h-4 mr-2" />
                  <span className="font-medium">Pro Plan Features:</span>
                </div>
                <ul className="space-y-1 pl-6">
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                    Unlimited cut piece quantities
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                    Pro plan valid for 1 year
                  </li>
                </ul>
              </div>
            )}
            
            {newPlan === 'platinum' && (
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-3 rounded border border-purple-200 text-sm space-y-2">
                <div className="flex items-center text-purple-600">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>
                  <span className="font-medium">Platinum Plan Features:</span>
                </div>
                <ul className="space-y-1 pl-6">
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                    All Pro plan features included
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                    AI Window Analysis capabilities
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                    Priority support and features
                  </li>
                </ul>
              </div>
            )}
            
            {newAccountStatus === 'suspended' && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md text-sm space-y-2">
                <div className="flex items-center text-red-600 font-medium">
                  <AlertCircleIcon className="w-4 h-4 mr-2" />
                  Warning: User Access Will Be Suspended
                </div>
                <p className="text-gray-700">
                  This will prevent the user from accessing Pro features. Use this option if the user failed 
                  to make a payment or violated platform policies.
                </p>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsUpdateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmitSubscriptionUpdate}
              disabled={updateSubscriptionMutation.isPending}
              className={
                newPlan === 'pro' 
                  ? 'bg-amber-500 hover:bg-amber-600' 
                  : newPlan === 'platinum'
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700' 
                    : undefined
              }
            >
              {updateSubscriptionMutation.isPending ? (
                <>Updating...</>
              ) : (
                <>
                  {newPlan === 'pro' && <CrownIcon className="w-4 h-4 mr-2" />}
                  {newPlan === 'platinum' && (
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>
                  )}
                  Update Plan
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <UserXIcon className="h-5 w-5 mr-2" />
              Delete User Account
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedUser && (
                <div className="space-y-4">
                  <p>
                    You are about to permanently delete the account for <span className="font-semibold">{selectedUser.username}</span>. 
                    This action cannot be undone.
                  </p>
                  
                  <div className="bg-amber-50 p-3 border border-amber-200 rounded text-sm">
                    <div className="flex items-center text-amber-600 font-medium mb-1">
                      <AlertCircleIcon className="w-4 h-4 mr-2" />
                      Warning: All user data will be permanently deleted
                    </div>
                    <ul className="space-y-1 pl-6 text-gray-700">
                      <li>• All user profiles, accessories, and settings</li>
                      <li>• All window designs and components</li>
                      <li>• All optimization history and projects</li>
                      <li>• All machine profiles and configurations</li>
                    </ul>
                  </div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              disabled={deleteUserMutation.isPending}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={(e) => {
                e.preventDefault(); // Prevent the dialog from closing automatically
                confirmDeleteUser();
              }}
              disabled={deleteUserMutation.isPending}
            >
              {deleteUserMutation.isPending ? (
                <>Deleting...</>
              ) : (
                <>
                  <Trash2Icon className="w-4 h-4 mr-2" />
                  Delete User
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <AlertDialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-blue-600">
              <KeyIcon className="h-5 w-5 mr-2" />
              Reset User Password
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedUser && (
                <div className="space-y-4">
                  <p>
                    You are about to reset the password for <span className="font-semibold">{selectedUser.username}</span>.
                    A temporary password will be generated.
                  </p>
                  
                  <div className="bg-blue-50 p-3 border border-blue-200 rounded text-sm">
                    <div className="flex items-center text-blue-600 font-medium mb-1">
                      <ShieldIcon className="w-4 h-4 mr-2" />
                      Important Information
                    </div>
                    <ul className="space-y-1 pl-6 text-gray-700">
                      <li>• The user will be required to change their password on next login</li>
                      <li>• An email notification will be sent to the user if they have an email address</li>
                      <li>• The temporary password will be shown only once after reset</li>
                    </ul>
                  </div>
                  
                  {temporaryPassword && (
                    <div className="mt-4 border rounded-md p-4 bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <Label className="font-medium text-gray-700">Temporary Password</Label>
                        <Button 
                          type="button" 
                          variant="ghost" 
                          size="sm"
                          className="h-8 px-2 text-gray-500"
                          onClick={() => {
                            navigator.clipboard.writeText(temporaryPassword);
                            toast({
                              title: "Copied!",
                              description: "Password copied to clipboard",
                              duration: 2000,
                            });
                          }}
                        >
                          <CopyIcon className="h-4 w-4 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="flex items-center p-2 bg-white border rounded">
                        <code className="font-mono text-sm flex-1 text-blue-600">{temporaryPassword}</code>
                      </div>
                      <p className="mt-2 text-xs text-gray-500">
                        Please share this temporary password with the user securely.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              disabled={resetPasswordMutation.isPending}
              onClick={() => {
                if (temporaryPassword) {
                  queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
                  setTemporaryPassword(null);
                }
              }}
            >
              {temporaryPassword ? "Done" : "Cancel"}
            </AlertDialogCancel>
            {!temporaryPassword && (
              <AlertDialogAction 
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={(e) => {
                  e.preventDefault(); // Prevent the dialog from closing automatically
                  confirmResetPassword();
                }}
                disabled={resetPasswordMutation.isPending}
              >
                {resetPasswordMutation.isPending ? (
                  <>Resetting...</>
                ) : (
                  <>
                    <KeyIcon className="w-4 h-4 mr-2" />
                    Reset Password
                  </>
                )}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}