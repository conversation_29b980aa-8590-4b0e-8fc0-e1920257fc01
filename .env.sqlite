# Local SQLite Database Configuration (No installation required)
DATABASE_URL=file:./local_database.db
PGDATABASE=aluminum_optimizer
PGHOST=localhost
PGPORT=5432
PGUSER=admin
PGPASSWORD=admin123

# Backup - NeonDB Configuration (commented out)
# DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
# PGDATABASE=neondb
# PGHOST=ep-quiet-cake-a6kq63uf.us-west-2.aws.neon.tech
# PGPORT=5432
# PGUSER=neondb_owner
# PGPASSWORD=npg_ynP9Ot1pNkRA

# Stripe Configuration (placeholder values for development)
STRIPE_SECRET_KEY=sk_test_placeholder
STRIPE_PUBLISHABLE_KEY=pk_test_placeholder

# SendGrid Configuration (optional)
SENDGRID_API_KEY=placeholder

# OpenAI Configuration (optional)
OPENAI_API_KEY=placeholder

# Session Secret
SESSION_SECRET=your-session-secret-key-here