import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";
import { CrownIcon } from "lucide-react";
import { AccountStatusAlert } from "@/components/AccountStatusAlert";

// ProtectedRoute component - redirects to /auth if user isn't authenticated
export function ProtectedRoute({
  path,
  component: Component,
  requirePro = false,
  requirePlatinum = false,
}: {
  path: string;
  component: React.ComponentType;
  requirePro?: boolean;
  requirePlatinum?: boolean;
}) {
  const { user, isLoading } = useAuth();

  // Show loading while checking authentication status
  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  // Redirect to auth page if not authenticated
  if (!user) {
    return (
      <Route path={path}>
        <Redirect to="/auth" />
      </Route>
    );
  }
  
  // Check if user needs to change their temporary password
  if (user.passwordChanged === false && path !== "/change-password") {
    return (
      <Route path={path}>
        <Redirect to="/change-password" />
      </Route>
    );
  }

  // Check if Pro user is suspended - deny access completely
  if (requirePro && user.subscriptionPlan === 'pro' && user.accountStatus === 'suspended') {
    return (
      <Route path={path}>
        <div className="container max-w-3xl mx-auto mt-20 px-4">
          <AccountStatusAlert 
            status="suspended" 
            message={user.statusNote || undefined} 
          />
          <div className="flex justify-center mt-8">
            <Button variant="outline" asChild>
              <Link href="/">Return to Home</Link>
            </Button>
          </div>
        </div>
      </Route>
    );
  }
  
  // Check if pro subscription is required but user doesn't have it
  if (requirePro && user.subscriptionPlan !== 'pro' && user.role !== 'admin') {
    return (
      <Route path={path}>
        <div className="container max-w-3xl mx-auto mt-20 px-4">
          <Alert className="bg-amber-50 border-amber-200">
            <CrownIcon className="h-5 w-5 text-amber-600" />
            <AlertTitle className="text-amber-800 text-lg font-semibold">
              Pro Subscription Required
            </AlertTitle>
            <AlertDescription className="text-amber-700 mt-2">
              <p className="mb-4">
                This feature is only available with a Pro subscription. Upgrade your account to access the window calculator, project management, profile manager, and accessories manager features.
              </p>
              <div className="flex space-x-4 mt-4">
                <Button asChild>
                  <Link href="/subscription">Upgrade to Pro</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/">Return to Home</Link>
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </Route>
    );
  }
  
  // Check if platinum subscription is required but user doesn't have it
  if (requirePlatinum && user.subscriptionPlan !== 'platinum' && user.role !== 'admin') {
    return (
      <Route path={path}>
        <div className="container max-w-3xl mx-auto mt-20 px-4">
          <Alert className="bg-purple-50 border-purple-200">
            <CrownIcon className="h-5 w-5 text-purple-600" />
            <AlertTitle className="text-purple-800 text-lg font-semibold">
              Platinum Subscription Required
            </AlertTitle>
            <AlertDescription className="text-purple-700 mt-2">
              <p className="mb-4">
                This feature is only available with a Platinum subscription. Upgrade your account to access advanced AI window analysis and other platinum features.
              </p>
              <div className="flex space-x-4 mt-4">
                <Button className="bg-purple-600 hover:bg-purple-700" asChild>
                  <Link href="/subscription">Upgrade to Platinum</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/">Return to Home</Link>
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </Route>
    );
  }

  // Render the protected component if authenticated and has required subscription
  // Include payment warning alert if account has payment issues
  return (
    <Route path={path}>
      <div>
        {/* Show payment warning banner for Pro users with pending_payment status */}
        {user.subscriptionPlan === 'pro' && user.accountStatus === 'pending_payment' && (
          <div className="container mx-auto max-w-7xl px-4 pt-4">
            <AccountStatusAlert 
              status="pending_payment" 
              message={user.statusNote || undefined}
            />
          </div>
        )}
        <Component />
      </div>
    </Route>
  );
}