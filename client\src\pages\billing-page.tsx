import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import { Link, useLocation } from "wouter";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  FileText, 
  Download, 
  CreditCard, 
  Calendar, 
  Clock, 
  Check, 
  X, 
  Loader2, 
  Receipt
} from "lucide-react";
import { useTranslation } from "react-i18next";

interface Invoice {
  id: number;
  userId: number;
  invoiceNumber: string;
  amount: string;
  description: string;
  subscriptionPlan: string;
  subscriptionPeriod: string;
  issuedDate: string;
  dueDate: string | null;
  paidDate: string | null;
  paymentMethod: string;
  status: string;
  items?: Array<{
    description: string;
    amount: number;
    type: string;
    period: string;
  }>;
  createdAt: string;
  updatedAt: string;
  stripeInvoiceId: string | null;
}

export default function BillingPage() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("invoices");

  // Fetch invoices
  const { data: invoices, isLoading } = useQuery({
    queryKey: ["/api/billing/invoices"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/billing/invoices");
      if (!response.ok) {
        throw new Error("Failed to fetch invoices");
      }
      return response.json() as Promise<Invoice[]>;
    },
    enabled: !!user
  });

  // Download invoice PDF
  const downloadInvoiceMutation = useMutation({
    mutationFn: async (invoiceId: number) => {
      const response = await apiRequest("GET", `/api/billing/invoices/${invoiceId}/pdf`);
      if (!response.ok) {
        throw new Error("Failed to download invoice");
      }
      return response.json() as Promise<{ pdfDataUri: string, fileName: string }>;
    },
    onSuccess: (data) => {
      try {
        // Convert the data URI to a Blob
        const byteString = atob(data.pdfDataUri.split(',')[1]);
        const mimeString = data.pdfDataUri.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        
        const blob = new Blob([ab], { type: mimeString });
        const url = URL.createObjectURL(blob);
        
        // Create an anchor element
        const link = document.createElement('a');
        
        // Set link properties
        link.href = url;
        link.download = data.fileName || `Window-Craft-Pro-Invoice-${Date.now()}.pdf`;
        link.style.display = 'none';
        
        // Append to the document
        document.body.appendChild(link);
        
        // Trigger click event
        link.click();
        
        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }, 100);
        
        toast({
          title: t("Success"),
          description: t("Invoice downloaded successfully"),
        });
      } catch (err) {
        console.error("Error downloading PDF:", err);
        
        // Fallback to opening in new tab
        try {
          // Create another download link, but simpler this time
          const link = document.createElement('a');
          link.href = data.pdfDataUri;
          link.download = data.fileName || `Window-Craft-Pro-Invoice-${Date.now()}.pdf`;
          link.target = '_blank';
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          
          setTimeout(() => {
            document.body.removeChild(link);
          }, 100);
        } catch (innerErr) {
          console.error("Error in fallback:", innerErr);
          
          // Last resort - try to open in a new window
          try {
            const newWindow = window.open();
            if (newWindow) {
              newWindow.document.write(`
                <html>
                  <head>
                    <title>Window Craft Pro Invoice</title>
                  </head>
                  <body style="margin:0;padding:0;">
                    <embed width="100%" height="100%" src="${data.pdfDataUri}" type="application/pdf" />
                    <p style="text-align:center;margin-top:10px;">
                      If the PDF doesn't appear, please right-click on this page and select "Save As" to download the invoice.
                    </p>
                  </body>
                </html>
              `);
            } else {
              window.location.href = data.pdfDataUri;
            }
          } catch (finalErr) {
            console.error("Final fallback error:", finalErr);
            window.location.href = data.pdfDataUri;
          }
        }
        
        toast({
          title: t("Note"),
          description: t("Invoice opened in new tab. You may need to save it manually."),
        });
      }
    },
    onError: (error) => {
      toast({
        title: t("Error"),
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return <Badge variant="default" className="bg-green-500"><Check className="h-3 w-3 mr-1" />{t("Paid")}</Badge>;
      case "unpaid":
        return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />{t("Unpaid")}</Badge>;
      case "pending":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />{t("Pending")}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Handle invoice download
  const handleDownloadInvoice = (invoiceId: number) => {
    downloadInvoiceMutation.mutate(invoiceId);
  };

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("Billing & Invoices")}</h1>
          <p className="text-muted-foreground mt-1">
            {t("Manage your subscription and view past invoices")}
          </p>
        </div>
        <Button variant="default" className="mt-4 md:mt-0" asChild>
          <Link href="/subscription">
            <CreditCard className="mr-2 h-4 w-4" />
            {t("Manage Subscription")}
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="invoices" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 md:w-[400px]">
          <TabsTrigger value="invoices">
            <FileText className="h-4 w-4 mr-2" />
            {t("Invoices")}
          </TabsTrigger>
          <TabsTrigger value="subscription">
            <Calendar className="h-4 w-4 mr-2" />
            {t("Subscription Details")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="invoices" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("Invoice History")}</CardTitle>
              <CardDescription>
                {t("View and download your past invoices")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : invoices && invoices.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("Invoice")}</TableHead>
                      <TableHead>{t("Date")}</TableHead>
                      <TableHead>{t("Amount")}</TableHead>
                      <TableHead>{t("Status")}</TableHead>
                      <TableHead className="text-right">{t("Actions")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{invoice.invoiceNumber}</span>
                            <span className="text-sm text-muted-foreground">{invoice.description}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{formatDate(invoice.issuedDate)}</span>
                            {invoice.dueDate && (
                              <span className="text-sm text-muted-foreground">
                                {t("Due")}: {formatDate(invoice.dueDate)}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>${invoice.amount}</TableCell>
                        <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadInvoice(invoice.id)}
                            disabled={downloadInvoiceMutation.isPending}
                          >
                            {downloadInvoiceMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Download className="h-4 w-4 mr-1" />
                            )}
                            {t("Download")}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <Receipt className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">{t("No invoices yet")}</h3>
                  <p className="text-muted-foreground mt-1 max-w-md">
                    {t("Your invoice history will appear here once you've made payments.")}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscription" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("Current Subscription")}</CardTitle>
              <CardDescription>
                {t("Details about your current subscription plan")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-lg capitalize">{user.subscriptionPlan} {t("Plan")}</p>
                    <p className="text-muted-foreground">
                      {user.subscriptionPlan === "free" ? t("Limited to 50 items") : t("Unlimited items")}
                    </p>
                  </div>
                  <Badge variant="outline" className="capitalize ml-auto">
                    {user.subscriptionPlan}
                  </Badge>
                </div>

                <Separator />

                <div className="grid gap-2">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t("Status")}</span>
                    <span className="font-medium">
                      {user.accountStatus === "active" ? (
                        <Badge variant="default" className="bg-green-500">
                          <Check className="h-3 w-3 mr-1" />
                          {t("Active")}
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          <X className="h-3 w-3 mr-1" />
                          {t("Inactive")}
                        </Badge>
                      )}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t("Renewal Date")}</span>
                    <span className="font-medium">
                      {user.subscriptionExpiry ? formatDate(user.subscriptionExpiry) : t("N/A")}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t("Maximum Items")}</span>
                    <span className="font-medium">
                      {user.subscriptionPlan === "free" ? "50" : t("Unlimited")}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col items-start gap-2">
              <div className="text-muted-foreground text-sm mb-2">
                {t("Need to change your plan?")}
              </div>
              <div className="flex space-x-2">
                <Button asChild>
                  <Link href="/subscription">
                    <CreditCard className="mr-2 h-4 w-4" />
                    {t("Manage Subscription")}
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/contact">
                    {t("Contact Support")}
                  </Link>
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}