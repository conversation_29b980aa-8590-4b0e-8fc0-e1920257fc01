version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: aluminum_optimizer_db
    environment:
      POSTGRES_DB: aluminum_optimizer
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d aluminum_optimizer"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
