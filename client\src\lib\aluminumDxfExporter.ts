/**
 * Aluminum Profile DXF Exporter with Machining Operations
 * 
 * This module provides functionality to export DXF files for aluminum profiles
 * with machining operations like grooves, holes, etc. for CNC machines.
 */

import Drawing from 'dxf-writer';

// Import types from the schema
import type { Component, MachiningOperation } from '@shared/schema';

// Color constants that match the AutoCAD Color Index (ACI)
const ACI_COLORS = {
  RED: 1,
  YELLOW: 2,
  GREEN: 3,
  CYAN: 4,
  BLUE: 5,
  MAGENTA: 6,
  WHITE: 7
};

// Interface for DXF export options
interface AluminumDxfExportOptions {
  scale?: number; // Scale factor (default: 1mm = 1 unit)
  drawLabels?: boolean; // Include description labels
  drawDimensions?: boolean; // Include dimensions
  drawCrossSections?: boolean; // Draw cross sections
  drawMachiningOperations?: boolean; // Draw machining operations
  viewType?: 'top' | 'front' | 'side' | '3d'; // View type
  showMeasurements?: boolean; // Show measurements
}

// Interface for calculated machining operation
interface CalculatedMachiningOperation extends MachiningOperation {
  calculatedPosition: number; // Position calculated from formula
}

// Interface for component with machining operations
interface ComponentWithMachiningOperations extends Component {
  machiningOperations?: CalculatedMachiningOperation[];
  calculatedWidth: number;
  calculatedHeight: number;
}

/**
 * Generates a DXF file for an aluminum profile component with machining operations
 * 
 * @param component The component with calculated dimensions and machining operations
 * @param options Export options for DXF generation
 * @returns The DXF file content as a string
 */
export function generateAluminumProfileDXF(
  component: ComponentWithMachiningOperations,
  options: AluminumDxfExportOptions = {}
): string {
  // Set default options
  const {
    scale = 1,
    drawLabels = true,
    drawDimensions = true,
    drawCrossSections = true,
    drawMachiningOperations = true,
    viewType = 'top',
    showMeasurements = true
  } = options;

  // Create a new drawing
  const dxf = new Drawing();

  // Add layers for different elements
  dxf.addLayer('Profile', ACI_COLORS.WHITE, 'CONTINUOUS');
  dxf.addLayer('Dimensions', ACI_COLORS.GREEN, 'CONTINUOUS');
  dxf.addLayer('Labels', ACI_COLORS.CYAN, 'CONTINUOUS');
  dxf.addLayer('CrossSection', ACI_COLORS.YELLOW, 'CONTINUOUS');
  dxf.addLayer('MachiningOperations', ACI_COLORS.RED, 'CONTINUOUS');
  dxf.addLayer('Hidden', ACI_COLORS.BLUE, 'DASHED');

  // Set the current layer to Profile
  dxf.setActiveLayer('Profile');

  // Get component dimensions
  const { calculatedWidth, calculatedHeight } = component;
  const length = calculatedWidth; // Use width as length for aluminum profiles
  
  // Draw profile outline - a simple rectangle
  const drawProfileRect = () => {
    // Draw a rectangle representing the aluminum profile
    dxf.drawRect(0, 0, length, 30); // 30mm standard height for profile cross section
  };

  // Draw top view (length of the profile)
  if (viewType === 'top') {
    drawProfileRect();
  } 
  // Draw side views (cross sections)
  else if (viewType === 'front' || viewType === 'side') {
    dxf.setActiveLayer('CrossSection');
    // Just draw a simple rectangle as cross section
    // In a more advanced implementation, we could fetch profile details
    dxf.drawRect(0, 0, 50, 30);
  }

  // Draw machining operations if available
  if (drawMachiningOperations && component.machiningOperations?.length) {
    dxf.setActiveLayer('MachiningOperations');
    
    component.machiningOperations.forEach(operation => {
      // Calculate position based on the formula (this should be pre-calculated)
      const position = operation.calculatedPosition;
      
      switch (operation.operationType) {
        case 'hole':
          // Draw a hole (circle)
          if (operation.diameter) {
            dxf.drawCircle(position, 15, operation.diameter / 2); // Center of the profile
          }
          break;
          
        case 'groove':
          // Draw a groove (rectangle)
          if (operation.width && operation.depth) {
            const grooveLength = operation.length || 20;
            
            if (operation.edgeReference === 'top') {
              dxf.drawRect(position, 0, position + grooveLength, operation.depth);
            } else if (operation.edgeReference === 'bottom') {
              dxf.drawRect(position, 30 - operation.depth, position + grooveLength, 30);
            }
          }
          break;
          
        case 'slot':
          // Draw a slot (rounded rectangle)
          if (operation.width && operation.length) {
            const centerY = operation.edgeReference === 'top' ? operation.width / 2 : 30 - operation.width / 2;
            
            // Draw the slot as two lines and two half circles
            const radius = operation.width / 2;
            const startX = position;
            const endX = position + operation.length;
            
            // Top line
            dxf.drawLine(startX, centerY - radius, endX, centerY - radius);
            // Bottom line
            dxf.drawLine(startX, centerY + radius, endX, centerY + radius);
            
            // Left half circle
            dxf.drawArc(startX, centerY, radius, 90, 270);
            // Right half circle
            dxf.drawArc(endX, centerY, radius, 270, 90);
          }
          break;
          
        case 'notch':
          // Draw a notch (cut out from the profile)
          if (operation.width && operation.depth) {
            if (operation.edgeReference === 'top') {
              dxf.drawRect(position, 0, position + operation.width, operation.depth);
            } else if (operation.edgeReference === 'bottom') {
              dxf.drawRect(position, 30 - operation.depth, position + operation.width, 30);
            } else if (operation.edgeReference === 'end') {
              dxf.drawRect(length - operation.depth, 0, length, operation.width);
            } else if (operation.edgeReference === 'start') {
              dxf.drawRect(0, 0, operation.depth, operation.width);
            }
          }
          break;
          
        case 'miter':
          // Draw a miter cut (angled cut)
          if (operation.angle) {
            const angle = operation.angle * Math.PI / 180; // Convert to radians
            
            if (operation.edgeReference === 'end') {
              // Draw an angled line at the end
              const startY = 0;
              const endY = 30;
              const startX = length - Math.tan(angle) * 30;
              const endX = length;
              
              dxf.drawLine(startX, startY, endX, endY);
            } else if (operation.edgeReference === 'start') {
              // Draw an angled line at the start
              const startY = 0;
              const endY = 30;
              const startX = 0;
              const endX = Math.tan(angle) * 30;
              
              dxf.drawLine(startX, startY, endX, endY);
            }
          }
          break;
      }
      
      // Add labels for the operation if requested
      if (drawLabels) {
        dxf.setActiveLayer('Labels');
        dxf.drawText(position, 40, 3, 0, `${operation.name}`, 'left');
      }
    });
  }
  
  // Add dimensions if requested
  if (drawDimensions) {
    dxf.setActiveLayer('Dimensions');
    
    // Overall length dimension
    dxf.drawText(length / 2, -10, 3, 0, `${length}mm`, 'center');
    
    // Add dimension lines
    dxf.drawLine(0, -5, 0, -8);
    dxf.drawLine(length, -5, length, -8);
    dxf.drawLine(0, -7, length, -7);
  }
  
  // Return the DXF content
  return dxf.toDxfString();
}

/**
 * Creates and triggers a download of a DXF file
 * 
 * @param dxfContent The DXF file content
 * @param filename The name of the file to download
 */
export function downloadDxfFile(dxfContent: string, filename: string): void {
  const blob = new Blob([dxfContent], { type: 'application/dxf' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  document.body.appendChild(link);
  link.click();
  
  // Clean up
  setTimeout(() => {
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, 100);
}

/**
 * Calculate position based on formula and component dimensions
 * 
 * @param formula Formula to calculate position (e.g., "width/2")
 * @param component Component with dimensions
 * @returns Calculated position
 */
export function calculateOperationPosition(
  formula: string, 
  component: ComponentWithMachiningOperations
): number {
  try {
    // Create context with component dimensions
    const context = {
      width: component.calculatedWidth,
      height: component.calculatedHeight,
      length: component.calculatedWidth // Use width as length for profiles
    };
    
    // Replace variables in formula
    let preparedFormula = formula
      .replace(/width/g, context.width.toString())
      .replace(/height/g, context.height.toString())
      .replace(/length/g, context.length.toString());
    
    // Evaluate formula
    // eslint-disable-next-line no-eval
    return eval(preparedFormula);
  } catch (error) {
    console.error('Error calculating operation position:', error);
    return 0;
  }
}

/**
 * Process machining operations for a component
 * 
 * @param component Component with machining operations
 * @returns Component with calculated machining operations
 */
export function processComponentWithMachiningOperations(
  component: Component & { machiningOperations?: MachiningOperation[] }
): ComponentWithMachiningOperations {
  // Cast to the correct type
  const processedComponent = component as ComponentWithMachiningOperations;
  
  // Calculate positions for machining operations
  if (processedComponent.machiningOperations?.length) {
    processedComponent.machiningOperations = processedComponent.machiningOperations.map(op => {
      return {
        ...op,
        calculatedPosition: calculateOperationPosition(op.positionFormula, processedComponent)
      };
    });
  }
  
  return processedComponent;
}