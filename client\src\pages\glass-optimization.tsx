import { useState } from "react";
import GlassStockForm from "@/components/GlassStockForm";
import GlassCutListManager from "@/components/GlassCutListManager";
import GlassOptimizationResults from "@/components/GlassOptimizationResults";
import UserMenu from "@/components/UserMenu";
import SubscriptionInfo from "@/components/SubscriptionInfo";
import { GlassPiece, GlassStockSettings, GlassOptimizationResult } from "@/lib/types";
import { optimizeGlassCutting } from "@/lib/glassOptimizationAlgorithm";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { Button } from "@/components/ui/button";

export default function GlassOptimizationPage() {
  const { user } = useAuth();
  const [glassStockSettings, setGlassStockSettings] = useState<GlassStockSettings>({
    sheetWidth: 3210,
    sheetHeight: 2250,
    kerf: 3,
    edgeTrim: 10
  });
  
  const [glassCutList, setGlassCutList] = useState<GlassPiece[]>([
    { id: 1, width: 1000, height: 800, quantity: 5, description: "Window pane", glassType: "Clear", canRotate: true },
    { id: 2, width: 600, height: 1200, quantity: 3, description: "Door panel", glassType: "Tinted", canRotate: true },
    { id: 3, width: 500, height: 500, quantity: 8, description: "Small frame", glassType: "Clear", canRotate: true }
  ]);
  
  const [optimizationResult, setOptimizationResult] = useState<GlassOptimizationResult | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);

  const { toast } = useToast();
  
  const handleOptimize = () => {
    setIsOptimizing(true);
    
    // Check total quantity for free plan users
    if (user && user.subscriptionPlan === 'free') {
      const totalQuantity = glassCutList.reduce((sum, piece) => sum + piece.quantity, 0);
      if (totalQuantity > user.maxQuantity) {
        toast({
          title: "Free Plan Limit Exceeded",
          description: `Your free plan allows up to ${user.maxQuantity} pieces. You're trying to optimize ${totalQuantity} pieces. Please upgrade to Pro plan or reduce your quantity.`,
          variant: "destructive",
        });
        setIsOptimizing(false);
        return;
      }
    }
    
    // Small delay to allow UI to update
    setTimeout(() => {
      try {
        const result = optimizeGlassCutting(glassCutList, glassStockSettings);
        setOptimizationResult(result);
      } catch (error) {
        console.error("Glass optimization failed:", error);
        toast({
          title: "Optimization Error",
          description: error instanceof Error ? error.message : "Unknown error occurred during optimization",
          variant: "destructive",
        });
      } finally {
        setIsOptimizing(false);
      }
    }, 100);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      <header className="relative pb-6">
        <UserMenu />
        
        <div className="text-center">
          <h1 className="text-3xl font-bold text-primary">Glass Cutting Optimizer</h1>
          <p className="text-neutral-600 mt-2">
            Optimize your glass sheet cutting layouts to minimize waste and costs
          </p>
        </div>
        
        <div className="flex justify-center mt-4 space-x-4">
          <Link href="/">
            <Button variant="outline">Switch to Aluminum Profile Optimizer</Button>
          </Link>
        </div>
      </header>
      
      <main className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <GlassStockForm 
              glassStockSettings={glassStockSettings} 
              onUpdateSettings={setGlassStockSettings} 
            />
          </div>
          <div className="lg:col-span-1">
            <SubscriptionInfo />
          </div>
        </div>
        
        <GlassCutListManager 
          glassCutList={glassCutList} 
          onUpdateGlassCutList={setGlassCutList} 
          onOptimize={handleOptimize}
          isOptimizing={isOptimizing}
        />
        
        {optimizationResult && (
          <GlassOptimizationResults 
            result={optimizationResult} 
            glassStockSettings={glassStockSettings}
            onReoptimize={handleOptimize}
          />
        )}
      </main>
      
      <footer className="mt-8 text-center text-sm text-neutral-600">
        <p>© {new Date().getFullYear()} Glass Cutting Optimizer. All rights reserved.</p>
      </footer>
    </div>
  );
}