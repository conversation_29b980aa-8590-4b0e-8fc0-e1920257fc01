import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { GlassOptimizationResult, GlassStockSettings, GlassTypeResult, GlassSheetPlan } from "@/lib/types";
import { RefreshCw, Download, Printer, FileDown } from "lucide-react";
import { useState } from "react";
import jsPDF from "jspdf";
import { generateAllGlassCuttingDxfs, downloadAllDxfFiles } from "@/lib/dxfExporter";

interface GlassOptimizationResultsProps {
  result: GlassOptimizationResult;
  glassStockSettings: GlassStockSettings;
  onReoptimize: () => void;
}

// Function to generate a color for a glass type
function getGlassColor(glassType: string, opacity: number = 1): string {
  // Simple hash function to generate a consistent color for the same glass type
  let hash = 0;
  for (let i = 0; i < glassType.length; i++) {
    hash = glassType.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Generate HSL color with high saturation and medium lightness
  const h = Math.abs(hash) % 360;
  const s = 70; // High saturation
  const l = 60; // Medium lightness to ensure readability
  
  return `hsla(${h}, ${s}%, ${l}%, ${opacity})`;
}

export default function GlassOptimizationResults({ 
  result,
  glassStockSettings,
  onReoptimize
}: GlassOptimizationResultsProps) {
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  const [isExportingDxf, setIsExportingDxf] = useState(false);
  
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };
  
  const downloadPdf = async () => {
    setIsGeneratingPdf(true);
    
    try {
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 10;
      
      // ===== FIRST PAGE: SUMMARY INFORMATION =====
      
      // Add title
      doc.setFontSize(18);
      doc.text("Glass Cutting Optimization Results", margin, 20);
      
      // Add date
      doc.setFontSize(10);
      doc.text(`Generated on: ${new Date().toLocaleString()}`, margin, 30);
      
      // Add glass stock settings
      doc.setFontSize(12);
      doc.text("Glass Stock Settings:", margin, 40);
      doc.setFontSize(10);
      doc.text(`Sheet Width: ${glassStockSettings.sheetWidth}mm`, margin + 5, 45);
      doc.text(`Sheet Height: ${glassStockSettings.sheetHeight}mm`, margin + 5, 50);
      doc.text(`Kerf Width: ${glassStockSettings.kerf}mm`, margin + 5, 55);
      doc.text(`Edge Trim: ${glassStockSettings.edgeTrim}mm`, margin + 5, 60);
      
      // Add summary
      let y = 70;
      doc.setFontSize(12);
      doc.text("Optimization Summary:", margin, y);
      y += 8;
      
      // Calculate total material usage and waste
      const totalArea = result.glassTypeResults.reduce((sum, type) => 
        sum + (type.sheetsCount * glassStockSettings.sheetWidth * glassStockSettings.sheetHeight), 0);
      
      const totalWasteArea = result.glassTypeResults.reduce((sum, type) => 
        sum + type.totalWasteArea, 0);
      
      const utilizationPercent = totalArea > 0 
        ? ((totalArea - totalWasteArea) / totalArea) * 100 
        : 0;
      
      const wastePercent = totalArea > 0 
        ? (totalWasteArea / totalArea) * 100 
        : 0;
      
      doc.setFontSize(10);
      doc.text(`Total Sheets Required: ${result.totalSheets}`, margin + 5, y);
      y += 5;
      doc.text(`Total Material Utilization: ${utilizationPercent.toFixed(1)}%`, margin + 5, y);
      y += 5;
      doc.text(`Total Material Waste: ${wastePercent.toFixed(1)}% (${totalWasteArea.toFixed(0)}mm²)`, margin + 5, y);
      y += 10;
      
      // Glass type results
      result.glassTypeResults.forEach(glassType => {
        if (y > pageHeight - 30) {
          doc.addPage();
          y = 20;
        }
        
        doc.setFontSize(11);
        doc.text(`${glassType.glassType} Glass:`, margin + 5, y);
        y += 5;
        
        doc.setFontSize(10);
        doc.text(`Sheets: ${glassType.sheetsCount}`, margin + 10, y);
        y += 5;
        doc.text(`Material Usage: ${formatPercentage(glassType.materialUsagePercent)}`, margin + 10, y);
        y += 5;
        doc.text(`Total Waste Area: ${glassType.totalWasteArea.toFixed(0)}mm²`, margin + 10, y);
        y += 10;
      });
      
      // ===== NEXT PAGES: CUTTING PLANS =====
      
      // Always start cutting plans on a new page
      doc.addPage();
      y = 20;
      
      // Add sheet plans for each glass type
      result.glassTypeResults.forEach(glassType => {
        glassType.sheetPlans.forEach((plan, planIndex) => {
          // If not enough space for the entire cutting plan visualization, start new page
          if (y > pageHeight - 120) {
            doc.addPage();
            y = 20;
          }
          
          doc.setFontSize(12);
          doc.text(`${glassType.glassType} Glass - Sheet ${planIndex + 1}:`, margin, y);
          y += 10;
          
          // Draw the sheet
          const sheetWidth = Math.min(pageWidth - (margin * 2), 180);  // Scale to fit page width
          const sheetHeight = (sheetWidth / glassStockSettings.sheetWidth) * glassStockSettings.sheetHeight;
          
          // Draw sheet border
          doc.setDrawColor(0);
          doc.setFillColor(240, 240, 240);
          doc.rect(margin, y, sheetWidth, sheetHeight, 'FD');
          
          // Draw each cut piece
          plan.cuts.forEach((cut, cutIndex) => {
            // Calculate position and size
            const scale = sheetWidth / glassStockSettings.sheetWidth;
            const x = margin + (cut.x * scale);
            const y_pos = y + (cut.y * scale);
            const width = cut.width * scale;
            const height = cut.height * scale;
            
            // Draw the piece
            const hue = (cutIndex * 30) % 360; // Different color for each piece
            doc.setFillColor(hue, 80, 80);
            doc.setDrawColor(0);
            doc.rect(x, y_pos, width, height, 'FD');
            
            // Add text if piece is large enough
            if (width > 30 && height > 10) {
              doc.setTextColor(255, 255, 255);
              doc.setFontSize(8);
              doc.text(`${cut.width}x${cut.height}`, x + 2, y_pos + 8);
            }
          });
          
          y += sheetHeight + 15;
          
          // Add cut list details for this sheet
          doc.setTextColor(0);
          doc.setFontSize(10);
          doc.text("Cut Pieces:", margin, y);
          y += 7;
          
          // Create a table for cut pieces
          const tableWidth = pageWidth - (margin * 2);
          const cellPadding = 3;
          const colWidths = [10, 30, 30, tableWidth - 130, 30];
          
          // Draw table headers
          doc.setFillColor(240, 240, 240);
          doc.rect(margin, y, tableWidth, 7, 'F');
          doc.setTextColor(0);
          doc.text("#", margin + cellPadding, y + 5);
          doc.text("Width", margin + colWidths[0] + cellPadding, y + 5);
          doc.text("Height", margin + colWidths[0] + colWidths[1] + cellPadding, y + 5);
          doc.text("Description", margin + colWidths[0] + colWidths[1] + colWidths[2] + cellPadding, y + 5);
          doc.text("Rotated", margin + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + cellPadding, y + 5);
          y += 7;
          
          // Draw table rows
          plan.cuts.forEach((cut, cutIndex) => {
            // Check if we need a new page for the table
            if (y > pageHeight - 10) {
              doc.addPage();
              y = 20;
              
              // Redraw the table header
              doc.setFillColor(240, 240, 240);
              doc.rect(margin, y, tableWidth, 7, 'F');
              doc.setTextColor(0);
              doc.text("#", margin + cellPadding, y + 5);
              doc.text("Width", margin + colWidths[0] + cellPadding, y + 5);
              doc.text("Height", margin + colWidths[0] + colWidths[1] + cellPadding, y + 5);
              doc.text("Description", margin + colWidths[0] + colWidths[1] + colWidths[2] + cellPadding, y + 5);
              doc.text("Rotated", margin + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + cellPadding, y + 5);
              y += 7;
            }
            
            doc.rect(margin, y, tableWidth, 7);
            doc.text(`${cutIndex + 1}`, margin + cellPadding, y + 5);
            doc.text(`${cut.width}mm`, margin + colWidths[0] + cellPadding, y + 5);
            doc.text(`${cut.height}mm`, margin + colWidths[0] + colWidths[1] + cellPadding, y + 5);
            doc.text(cut.description || 'No description', margin + colWidths[0] + colWidths[1] + colWidths[2] + cellPadding, y + 5);
            doc.text(cut.rotated ? 'Yes' : 'No', margin + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + cellPadding, y + 5);
            y += 7;
          });
          
          // Add more space between sheets
          y += 15;
          
          // Start each new sheet on a fresh page
          if (planIndex < glassType.sheetPlans.length - 1) {
            doc.addPage();
            y = 20;
          }
        });
        
        // Start each new glass type on a fresh page
        if (glassType !== result.glassTypeResults[result.glassTypeResults.length - 1]) {
          doc.addPage();
          y = 20;
        }
      });
      
      doc.save('glass-cutting-optimization-results.pdf');
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGeneratingPdf(false);
    }
  };
  
  const printCuttingPlan = () => {
    setIsPrinting(true);
    
    setTimeout(() => {
      try {
        window.print();
      } catch (error) {
        console.error('Error printing:', error);
      } finally {
        setIsPrinting(false);
      }
    }, 100);
  };
  
  // Export cutting plans as DXF files for CNC machines
  const exportDxfFiles = () => {
    setIsExportingDxf(true);
    
    setTimeout(() => {
      try {
        // Export each glass type separately
        result.glassTypeResults.forEach(glassType => {
          const dxfContents = generateAllGlassCuttingDxfs(glassType, glassStockSettings);
          downloadAllDxfFiles(dxfContents, `glass-cutting-${glassType.glassType.toLowerCase()}`);
        });
      } catch (error) {
        console.error('Error exporting DXF files:', error);
      } finally {
        setIsExportingDxf(false);
      }
    }, 100);
  };
  
  // Render a single glass type result card
  const renderGlassTypeResultCard = (glassTypeResult: GlassTypeResult) => {
    const bgColor = getGlassColor(glassTypeResult.glassType, 0.1);
    const textColor = getGlassColor(glassTypeResult.glassType, 1);
    
    return (
      <div 
        key={glassTypeResult.glassType}
        className="p-4 rounded-lg border mb-4"
        style={{ backgroundColor: bgColor, borderColor: textColor }}
      >
        <h3 className="text-lg font-bold mb-3" style={{ color: textColor }}>
          {glassTypeResult.glassType} Glass
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Sheets</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {glassTypeResult.sheetsCount}
            </p>
          </div>
          
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Material Usage</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {formatPercentage(glassTypeResult.materialUsagePercent)}
            </p>
          </div>
          
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Waste Area</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {glassTypeResult.totalWasteArea.toFixed(0)} mm²
            </p>
          </div>
        </div>
      </div>
    );
  };
  
  // Render cutting plans for a glass type
  const renderGlassSheetPlans = (glassTypeResult: GlassTypeResult) => {
    const bgColor = getGlassColor(glassTypeResult.glassType, 0.1);
    const textColor = getGlassColor(glassTypeResult.glassType, 1);
    
    return (
      <div key={`plans-${glassTypeResult.glassType}`} className="space-y-6 mb-6">
        <h3 
          className="text-lg font-medium p-2 rounded border-l-4"
          style={{ 
            backgroundColor: bgColor, 
            borderLeftColor: textColor,
            color: textColor
          }}
        >
          {glassTypeResult.glassType} Glass Cutting Plans
        </h3>
        
        {glassTypeResult.sheetPlans.length === 0 ? (
          <p className="text-neutral-600 italic">No {glassTypeResult.glassType} glass pieces to optimize.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {glassTypeResult.sheetPlans.map((plan, planIndex) => (
              <div key={planIndex} className="border rounded-lg p-4">
                <p className="font-medium text-neutral-900 mb-2">Sheet #{planIndex + 1}</p>
                
                {/* Sheet visualization */}
                <div className="glass-sheet-visualization relative border border-neutral-300 mb-4" style={{
                  width: '100%',
                  height: '300px',
                  position: 'relative',
                  backgroundColor: '#f5f5f5'
                }}>
                  {/* Cut Pieces */}
                  {plan.cuts.map((cut, cutIndex) => {
                    // Calculate position and size as percentage of sheet dimensions
                    const xPercent = (cut.x / glassStockSettings.sheetWidth) * 100;
                    const yPercent = (cut.y / glassStockSettings.sheetHeight) * 100;
                    const widthPercent = (cut.width / glassStockSettings.sheetWidth) * 100;
                    const heightPercent = (cut.height / glassStockSettings.sheetHeight) * 100;
                    
                    // Generate a unique color for each piece
                    const pieceColor = `hsl(${(cutIndex * 30) % 360}, 80%, 60%)`;
                    
                    return (
                      <div 
                        key={cutIndex}
                        className="absolute border border-neutral-700 flex items-center justify-center overflow-hidden"
                        style={{ 
                          left: `${xPercent}%`, 
                          top: `${yPercent}%`,
                          width: `${widthPercent}%`,
                          height: `${heightPercent}%`,
                          backgroundColor: pieceColor
                        }}
                      >
                        <span className="text-white text-xs font-medium p-1">
                          {cut.width}x{cut.height}
                        </span>
                      </div>
                    );
                  })}
                </div>
                
                <div className="text-sm text-neutral-600 mb-2">
                  <span className="font-medium">Material Usage:</span> 
                  {formatPercentage(plan.materialUsagePercent)} 
                  ({glassStockSettings.sheetWidth * glassStockSettings.sheetHeight - plan.wasteArea}mm² used, {plan.wasteArea}mm² waste)
                </div>
                
                {/* Cut list table */}
                <table className="min-w-full border-collapse text-sm">
                  <thead>
                    <tr className="bg-neutral-100">
                      <th className="px-2 py-1 border border-neutral-300 text-left">#</th>
                      <th className="px-2 py-1 border border-neutral-300 text-left">Width</th>
                      <th className="px-2 py-1 border border-neutral-300 text-left">Height</th>
                      <th className="px-2 py-1 border border-neutral-300 text-left">Description</th>
                      <th className="px-2 py-1 border border-neutral-300 text-left">Rotated</th>
                    </tr>
                  </thead>
                  <tbody>
                    {plan.cuts.map((cut, cutIndex) => (
                      <tr key={cutIndex}>
                        <td className="px-2 py-1 border border-neutral-300">{cutIndex + 1}</td>
                        <td className="px-2 py-1 border border-neutral-300">{cut.width}mm</td>
                        <td className="px-2 py-1 border border-neutral-300">{cut.height}mm</td>
                        <td className="px-2 py-1 border border-neutral-300">{cut.description || 'No description'}</td>
                        <td className="px-2 py-1 border border-neutral-300">{cut.rotated ? 'Yes' : 'No'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Glass Optimization Results</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Results Summary */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-primary bg-opacity-10 p-4 rounded-lg text-center">
              <h3 className="text-primary text-sm font-bold mb-1">Total Sheets</h3>
              <p className="text-primary text-3xl font-semibold">{result.totalSheets}</p>
            </div>
            
            {/* Calculate and show overall material utilization */}
            {(() => {
              // Calculate total used material and total waste across all glass types
              const totalArea = result.glassTypeResults.reduce((sum, type) => 
                sum + (type.sheetsCount * glassStockSettings.sheetWidth * glassStockSettings.sheetHeight), 0);
              
              const totalWasteArea = result.glassTypeResults.reduce((sum, type) => 
                sum + type.totalWasteArea, 0);
              
              const utilizationPercent = totalArea > 0 
                ? ((totalArea - totalWasteArea) / totalArea) * 100 
                : 0;
              
              const wastePercent = totalArea > 0 
                ? (totalWasteArea / totalArea) * 100 
                : 0;
              
              return (
                <>
                  <div className="bg-green-100 p-4 rounded-lg text-center">
                    <h3 className="text-green-700 text-sm font-bold mb-1">Total Material Utilization</h3>
                    <p className="text-green-700 text-3xl font-semibold">{utilizationPercent.toFixed(1)}%</p>
                  </div>
                  
                  <div className="bg-red-100 p-4 rounded-lg text-center">
                    <h3 className="text-red-700 text-sm font-bold mb-1">Total Material Waste</h3>
                    <p className="text-red-700 text-3xl font-semibold">{wastePercent.toFixed(1)}%</p>
                    <p className="text-red-600 text-sm">{totalWasteArea.toFixed(0)}mm² total</p>
                  </div>
                </>
              );
            })()}
          </div>
          
          {/* Glass Type Results Summary Cards */}
          <div className="space-y-4">
            {result.glassTypeResults.map(glassTypeResult => renderGlassTypeResultCard(glassTypeResult))}
          </div>
        </div>
        
        {/* Sheet Plans Visualization for each glass type */}
        {result.glassTypeResults.map(glassTypeResult => renderGlassSheetPlans(glassTypeResult))}
        
        <div className="flex flex-wrap gap-4 mt-6">
          <Button
            onClick={downloadPdf}
            disabled={isGeneratingPdf}
            className="bg-neutral-600 text-white hover:bg-neutral-700 flex items-center"
          >
            <Download className="h-5 w-5 mr-2" />
            {isGeneratingPdf ? 'Generating PDF...' : 'Download PDF'}
          </Button>
          
          <Button
            onClick={printCuttingPlan}
            disabled={isPrinting}
            className="bg-neutral-600 text-white hover:bg-neutral-700 flex items-center"
          >
            <Printer className="h-5 w-5 mr-2" />
            {isPrinting ? 'Preparing...' : 'Print Cutting Plan'}
          </Button>
          
          <Button
            onClick={exportDxfFiles}
            disabled={isExportingDxf}
            className="bg-blue-600 text-white hover:bg-blue-700 flex items-center"
          >
            <FileDown className="h-5 w-5 mr-2" />
            {isExportingDxf ? 'Exporting...' : 'Export DXF for CNC'}
          </Button>
          
          <Button
            onClick={onReoptimize}
            variant="outline"
            className="flex items-center"
          >
            <RefreshCw className="h-5 w-5 mr-2" />
            Re-Optimize
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}