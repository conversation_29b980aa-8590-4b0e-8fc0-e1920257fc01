import dotenv from 'dotenv';
import { Pool as NeonPool, neonConfig } from '@neondatabase/serverless';
import Database from 'better-sqlite3';
import ws from 'ws';

dotenv.config();

// Configure Neon
neonConfig.webSocketConstructor = ws;

// Neon database connection
const neonPool = new NeonPool({
  connectionString: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
});

// SQLite database connection
const sqlite = new Database('./local_database.db');

// List of tables to migrate (in dependency order)
const tables = [
  'users',
  'profiles',
  'window_designs',
  'window_design_components',
  'window_design_glass_specifications',
  'window_projects',
  'project_window_items',
  'accessories',
  'invoices',
  'subscription_plans'
];

async function exportTableData(tableName) {
  const client = await neonPool.connect();
  try {
    console.log(`Exporting data from table: ${tableName}`);
    const result = await client.query(`SELECT * FROM ${tableName}`);
    console.log(`Found ${result.rows.length} rows in ${tableName}`);
    return result.rows;
  } catch (error) {
    console.error(`Error exporting ${tableName}:`, error.message);
    return [];
  } finally {
    client.release();
  }
}

function importTableData(tableName, data) {
  if (data.length === 0) {
    console.log(`No data to import for ${tableName}`);
    return;
  }

  try {
    console.log(`Importing ${data.length} rows to ${tableName}`);
    
    // Get column names from first row
    const columns = Object.keys(data[0]);
    const columnList = columns.join(', ');
    const placeholders = columns.map(() => '?').join(', ');
    
    // Prepare insert statement
    const insertStmt = sqlite.prepare(
      `INSERT OR IGNORE INTO ${tableName} (${columnList}) VALUES (${placeholders})`
    );
    
    // Insert data in a transaction for better performance
    const insertMany = sqlite.transaction((rows) => {
      for (const row of rows) {
        const values = columns.map(col => row[col]);
        insertStmt.run(values);
      }
    });
    
    insertMany(data);
    console.log(`Successfully imported data to ${tableName}`);
    
  } catch (error) {
    console.error(`Error importing to ${tableName}:`, error.message);
  }
}

function checkTableExists(tableName) {
  try {
    const result = sqlite.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `).get(tableName);
    return !!result;
  } catch (error) {
    return false;
  }
}

async function migrateData() {
  console.log('Starting data migration from Neon to SQLite...');
  
  try {
    // Test Neon connection
    console.log('Testing Neon connection...');
    const neonClient = await neonPool.connect();
    await neonClient.query('SELECT 1');
    neonClient.release();
    console.log('✅ Neon connection successful');
    
    // Test SQLite connection
    console.log('Testing SQLite connection...');
    sqlite.prepare('SELECT 1').get();
    console.log('✅ SQLite connection successful');
    
    // Migrate each table
    for (const tableName of tables) {
      console.log(`\n--- Processing table: ${tableName} ---`);
      
      // Check if table exists in SQLite database
      const exists = checkTableExists(tableName);
      if (!exists) {
        console.log(`⚠️  Table ${tableName} does not exist in SQLite database. Skipping...`);
        continue;
      }
      
      // Export data from Neon
      const data = await exportTableData(tableName);
      
      // Import data to SQLite
      importTableData(tableName, data);
    }
    
    console.log('\n🎉 Data migration completed successfully!');
    console.log('📊 Your data is now available in the local SQLite database');
    console.log('🚀 You can now run: npm run dev');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await neonPool.end();
    sqlite.close();
  }
}

// Run migration
migrateData();
