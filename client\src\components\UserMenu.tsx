import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Link, useLocation } from "wouter";
import { LogOut, User, Save, Shield, CrownIcon, Phone, Grid, Settings, Receipt, CreditCard } from "lucide-react";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";

export default function UserMenu() {
  const { toast } = useToast();
  const { user, logoutMutation } = useAuth();
  const [, setLocation] = useLocation();
  const { t } = useTranslation();
  
  // If not logged in, show login button and contact link
  if (!user) {
    return (
      <div className="absolute top-0 right-0 flex items-center p-4">
        <LanguageSwitcher />
        <Link href="/contact" className="mr-3 ml-2">
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>{t('navigation.contact')}</span>
          </Button>
        </Link>
        <Link href="/auth">
          <Button variant="outline" size="sm">
            {t('navigation.login')} / {t('user.registerButton')}
          </Button>
        </Link>
      </div>
    );
  }
  
  // Get user initials for avatar
  const getUserInitials = () => {
    // Check if name exists and is not null
    if (user.name && typeof user.name === 'string') {
      const names = user.name.split(' ');
      if (names.length >= 2) {
        return `${names[0][0]}${names[1][0]}`.toUpperCase();
      }
      return user.name[0].toUpperCase();
    }
    return user.username[0].toUpperCase();
  };
  
  const handleProfileClick = () => {
    setLocation('/profile');
  };
  
  const handleSavedProjectsClick = () => {
    toast({
      title: "Saved Projects",
      description: "View your saved cutting projects",
    });
  };
  
  const handleSignOutClick = () => {
    logoutMutation.mutate();
  };
  
  return (
    <div className="absolute top-0 right-0 flex items-center p-4">
      <LanguageSwitcher />
      <div className="flex items-center ml-2 mr-2 hidden md:flex">
        <span className="text-sm mr-2">
          {(user.name && typeof user.name === 'string') ? user.name : user.username}
        </span>
        {user.subscriptionPlan === 'pro' && (
          <span className="bg-amber-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
            <CrownIcon className="w-3 h-3 mr-1" />
            {t('subscription.pro')}
          </span>
        )}
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="p-0 focus:outline-none focus:ring-0"
          >
            <Avatar>
              <AvatarFallback className="bg-primary text-white">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={handleProfileClick} className="cursor-pointer flex items-center gap-2">
            <User className="h-4 w-4" />
            <span>{t('navigation.profile')}</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleSavedProjectsClick} className="cursor-pointer flex items-center gap-2">
            <Save className="h-4 w-4" />
            <span>{t('common.save')}</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => setLocation('/window-calculator')} 
            className="cursor-pointer flex items-center gap-2"
          >
            <Grid className="h-4 w-4" />
            <span>Window Calculator</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => setLocation('/profiles')} 
            className="cursor-pointer flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            <span>Profile Manager</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => setLocation('/contact')} 
            className="cursor-pointer flex items-center gap-2"
          >
            <Phone className="h-4 w-4" />
            <span>{t('navigation.contact')}</span>
          </DropdownMenuItem>
          
          {user.subscriptionPlan !== 'free' && (
            <DropdownMenuItem 
              onClick={() => setLocation('/billing')} 
              className="cursor-pointer flex items-center gap-2"
            >
              <Receipt className="h-4 w-4" />
              <span>Billing & Invoices</span>
            </DropdownMenuItem>
          )}
          
          {user.role === 'admin' && (
            <DropdownMenuItem 
              onClick={() => setLocation('/admin')} 
              className="cursor-pointer flex items-center gap-2"
            >
              <Shield className="h-4 w-4" />
              <span>{t('navigation.admin')}</span>
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleSignOutClick} 
            className="cursor-pointer text-red-500 focus:text-red-500 flex items-center gap-2"
            disabled={logoutMutation.isPending}
          >
            <LogOut className="h-4 w-4" />
            <span>{logoutMutation.isPending ? t('common.loading') : t('navigation.logout')}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
