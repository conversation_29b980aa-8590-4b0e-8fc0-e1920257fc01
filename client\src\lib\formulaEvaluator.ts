/**
 * Formula Evaluator for Window Cutting List Calculator
 * 
 * This module provides utilities for evaluating mathematical formulas with variable substitution.
 * It supports basic arithmetic operations, parentheses, and custom variables.
 */

/**
 * Variables passed to the formula evaluator
 */
export interface FormulaVariables {
  [key: string]: number;
}

/**
 * Error thrown when formula evaluation fails
 */
export class FormulaEvaluationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'FormulaEvaluationError';
  }
}

/**
 * Evaluates a mathematical formula string with variable substitution
 * 
 * @param formula - The formula string to evaluate (e.g., "width/2 + 10")
 * @param variables - Object containing variable values (e.g., { width: 1000 })
 * @returns The result of the formula evaluation
 * @throws {FormulaEvaluationError} If the formula cannot be evaluated
 */
export function evaluateFormula(formula: string, variables: FormulaVariables): number {
  if (!formula || formula.trim() === '') {
    throw new FormulaEvaluationError('Empty formula');
  }

  try {
    // Replace all variables with their values
    let processedFormula = formula.trim();
    
    // Sort variable names by length (descending) to prevent partial replacements
    // (e.g., replacing 'width' before 'totalWidth' would cause issues)
    const variableNames = Object.keys(variables).sort((a, b) => b.length - a.length);
    
    for (const varName of variableNames) {
      // Use regex with word boundaries to ensure we replace complete variable names
      const varRegex = new RegExp(`\\b${varName}\\b`, 'g');
      processedFormula = processedFormula.replace(varRegex, variables[varName].toString());
    }
    
    // Handle some common mathematical functions
    processedFormula = processedFormula
      .replace(/Math\.round/g, 'Math.round')
      .replace(/Math\.floor/g, 'Math.floor')
      .replace(/Math\.ceil/g, 'Math.ceil')
      .replace(/Math\.abs/g, 'Math.abs')
      .replace(/round/g, 'Math.round')
      .replace(/floor/g, 'Math.floor')
      .replace(/ceil/g, 'Math.ceil')
      .replace(/abs/g, 'Math.abs');
    
    // Evaluate the formula using the Function constructor
    // eslint-disable-next-line no-new-func
    const result = new Function('Math', `return ${processedFormula}`)(Math);
    
    // Check if the result is a valid number
    if (typeof result !== 'number' || isNaN(result) || !isFinite(result)) {
      throw new FormulaEvaluationError(`Invalid result: ${result}`);
    }
    
    // Return result as an integer (for backward compatibility, this is optional)
    // The calling functions now handle rounding themselves
    return result;
  } catch (error) {
    if (error instanceof FormulaEvaluationError) {
      throw error;
    }
    throw new FormulaEvaluationError(`Error evaluating formula "${formula}": ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Validates a formula by attempting to evaluate it with sample variables
 * 
 * @param formula - The formula to validate
 * @returns An object with validation result and error message if applicable
 */
export function validateFormula(formula: string): { isValid: boolean; error?: string } {
  if (!formula || formula.trim() === '') {
    return { isValid: false, error: 'Formula cannot be empty' };
  }
  
  // Sample variables for validation
  const sampleVariables: FormulaVariables = {
    width: 1000,
    height: 800,
    quantity: 1
  };
  
  try {
    evaluateFormula(formula, sampleVariables);
    return { isValid: true };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Generates a cutting list for a window design based on input dimensions
 * 
 * @param components - Array of component objects with formulas
 * @param width - Window width
 * @param height - Window height
 * @param quantity - Number of windows
 * @returns Array of calculated components with dimensions
 */
export function generateCuttingList(
  components: Array<{
    id: number;
    name: string;
    profileId: number;
    widthFormula?: string | null;
    widthQuantity?: string;
    heightFormula?: string | null;
    heightQuantity?: string;
    isFixedHeight?: boolean;
    fixedHeightValue?: number | null;
    quantityFormula: string;
    componentType: string;
    leftCutDegree?: number;
    rightCutDegree?: number;
  }>,
  width: number,
  height: number,
  quantity: number = 1
) {
  const variables: FormulaVariables = { width, height, quantity };
  
  return components.map(component => {
    try {
      // Calculate dimensions based on formulas - widthFormula is now optional
      const calculatedWidth = component.widthFormula 
        ? Math.round(evaluateFormula(component.widthFormula, variables))
        : 0;
      
      // Height calculation: use fixed height if enabled, otherwise use formula if provided
      let calculatedHeight = null;
      if (component.isFixedHeight && component.fixedHeightValue) {
        try {
          // Try to evaluate the fixed height value as a formula
          calculatedHeight = Math.round(evaluateFormula(component.fixedHeightValue, variables));
        } catch (e) {
          // If it's not a valid formula, try to parse it as a number
          const numValue = parseFloat(component.fixedHeightValue);
          if (!isNaN(numValue)) {
            calculatedHeight = Math.round(numValue);
          } else {
            // If all fails, default to 0 with error
            calculatedHeight = 0;
            throw new Error(`Invalid fixed height value: ${component.fixedHeightValue}`);
          }
        }
      } else if (component.heightFormula) {
        // Use the height formula
        calculatedHeight = Math.round(evaluateFormula(component.heightFormula, variables));
      }
      
      // Calculate quantity for width pieces
      const widthQuantity = component.widthQuantity 
        ? Math.round(evaluateFormula(component.widthQuantity, variables))
        : 0;
      
      // Calculate quantity for height pieces
      const heightQuantity = component.heightQuantity 
        ? Math.round(evaluateFormula(component.heightQuantity, variables))
        : 0;
      
      // Legacy quantity formula - keep for backward compatibility
      const calculatedQuantity = Math.round(evaluateFormula(component.quantityFormula, variables));
      
      return {
        ...component,
        calculatedWidth,
        calculatedHeight,
        calculatedWidthQuantity: widthQuantity,
        calculatedHeightQuantity: heightQuantity,
        calculatedQuantity,
        totalQuantity: Math.round(calculatedQuantity * quantity), // Total quantity needed based on window quantity
        error: null
      };
    } catch (error) {
      // Return component with error information
      return {
        ...component,
        calculatedWidth: 0,
        calculatedHeight: 0,
        calculatedWidthQuantity: 0,
        calculatedHeightQuantity: 0,
        calculatedQuantity: 0,
        totalQuantity: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
}

/**
 * Generates a glass cutting list for a window design based on input dimensions
 * 
 * @param glassSpecs - Array of glass specification objects with formulas
 * @param width - Window width
 * @param height - Window height
 * @param quantity - Number of windows
 * @returns Array of calculated glass items with dimensions
 */
export function generateGlassCuttingList(
  glassSpecs: Array<{
    id: number;
    name: string;
    widthFormula: string;
    heightFormula: string;
    isFixedHeight?: boolean;
    fixedHeightValue?: string;
    quantityFormula: string;
    glassType: string;
    thickness: number;
  }>,
  width: number,
  height: number,
  quantity: number = 1
) {
  const variables: FormulaVariables = { width, height, quantity };
  
  return glassSpecs.map(spec => {
    try {
      // Calculate dimensions based on formulas
      const calculatedWidth = Math.round(evaluateFormula(spec.widthFormula, variables));
      
      // Height calculation: use fixed height if enabled, otherwise use formula
      let calculatedHeight;
      if (spec.isFixedHeight && spec.fixedHeightValue) {
        try {
          // Try to evaluate the fixed height value as a formula
          calculatedHeight = Math.round(evaluateFormula(spec.fixedHeightValue, variables));
        } catch (e) {
          // If it's not a valid formula, try to parse it as a number
          const numValue = parseFloat(spec.fixedHeightValue);
          if (!isNaN(numValue)) {
            calculatedHeight = Math.round(numValue);
          } else {
            // If all fails, default to 0 with error
            calculatedHeight = 0;
            throw new Error(`Invalid fixed height value: ${spec.fixedHeightValue}`);
          }
        }
      } else {
        // Use the regular height formula
        calculatedHeight = Math.round(evaluateFormula(spec.heightFormula, variables));
      }
      
      const calculatedQuantity = Math.round(evaluateFormula(spec.quantityFormula, variables));
      
      // Calculate area in square meters
      const areaM2 = (calculatedWidth * calculatedHeight) / 1000000; // Convert from mm² to m²
      
      return {
        ...spec,
        calculatedWidth,
        calculatedHeight,
        calculatedQuantity,
        totalQuantity: Math.round(calculatedQuantity * quantity),
        areaM2,
        totalAreaM2: areaM2 * calculatedQuantity * quantity,
        error: null
      };
    } catch (error) {
      return {
        ...spec,
        calculatedWidth: 0,
        calculatedHeight: 0,
        calculatedQuantity: 0,
        totalQuantity: 0,
        areaM2: 0,
        totalAreaM2: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });
}