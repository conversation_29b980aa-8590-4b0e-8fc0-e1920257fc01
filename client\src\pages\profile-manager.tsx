import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Profile } from '@shared/schema';

// Icons
import { Pencil, Trash2, Plus, ArrowLeft } from 'lucide-react';

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Profile Categories
const PROFILE_CATEGORIES = [
  'Frame',
  'Transom',
  'Mullion',
  'Sash',
  'Glazing Bead',
  'Thermal Break',
  'Reinforcement',
  'Other',
];

// Form schema
const profileFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  category: z.string().min(1, { message: 'Category is required' }),
  description: z.string().optional(),
  manufacturer: z.string().optional(),
  profileCode: z.string().optional(),
  imageUrl: z.string().optional().nullable(),
  technicalDetails: z.any().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

export default function ProfileManagerPage() {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Reset form when dialog is closed
  useEffect(() => {
    if (!isEditDialogOpen) {
      form.reset({
        name: '',
        category: '',
        description: '',
        manufacturer: '',
        profileCode: '',
        imageUrl: '',
        technicalDetails: '',
      });
    }
  }, [isEditDialogOpen]);

  // Form setup
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: '',
      category: '',
      description: '',
      manufacturer: '',
      profileCode: '',
      imageUrl: '',
      technicalDetails: '',
    },
  });

  // Query to fetch profiles
  const { data: profiles = [], isLoading } = useQuery<Profile[]>({
    queryKey: ['/api/profiles'],
  });

  // Create profile mutation
  const createProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      const res = await fetch('/api/profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || 'Failed to create profile');
      }
      
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/profiles'] });
      setIsEditDialogOpen(false);
      
      toast({
        title: t('Profile Created'),
        description: t('The profile has been created successfully.'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('Creation Failed'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues & { id: number }) => {
      const { id, ...profileData } = data;
      
      const res = await fetch(`/api/profiles/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData),
      });
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || 'Failed to update profile');
      }
      
      return await res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/profiles'] });
      setIsEditDialogOpen(false);
      
      if (selectedProfile?.id === data.id) {
        setSelectedProfile(data);
      }
      
      toast({
        title: t('Profile Updated'),
        description: t('The profile has been updated successfully.'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('Update Failed'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Delete profile mutation
  const deleteProfileMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await fetch(`/api/profiles/${id}`, {
        method: 'DELETE',
      });
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || 'Failed to delete profile');
      }
      
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ['/api/profiles'] });
      setIsDeleteDialogOpen(false);
      
      if (selectedProfile?.id === id) {
        setSelectedProfile(null);
      }
      
      toast({
        title: t('Profile Deleted'),
        description: t('The profile has been deleted successfully.'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('Deletion Failed'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (values: ProfileFormValues) => {
    if (selectedProfile) {
      updateProfileMutation.mutate({
        ...values,
        id: selectedProfile.id,
      });
    } else {
      createProfileMutation.mutate(values);
    }
  };

  const handleEditProfile = (profile: Profile) => {
    form.reset({
      name: profile.name,
      category: profile.category,
      description: profile.description || '',
      manufacturer: profile.manufacturer || '',
      profileCode: profile.profileCode || '',
      imageUrl: profile.imageUrl || '',
      technicalDetails: profile.technicalDetails || '',
    });
    
    setSelectedProfile(profile);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProfile = () => {
    if (selectedProfile) {
      deleteProfileMutation.mutate(selectedProfile.id);
    }
  };

  // Filter profiles by category
  const filteredProfiles = activeCategory
    ? profiles.filter((profile: Profile) => profile.category === activeCategory)
    : profiles;

  return (
    <div className="container py-6 mx-auto">
      {/* Back Button */}
      <Button 
        variant="outline" 
        size="sm" 
        className="mb-6"
        onClick={() => setLocation('/')}
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        {t('Back to Home')}
      </Button>
      
      <div className="flex flex-col space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('Profile Management')}</h1>
          <p className="text-muted-foreground">
            {t('Create and manage aluminum profiles for your cutting plans')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('Categories')}</CardTitle>
                <CardDescription>
                  {t('Filter profiles by category')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button
                    variant={activeCategory === null ? 'default' : 'outline'}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setActiveCategory(null)}
                  >
                    {t('All Categories')}
                  </Button>
                  {PROFILE_CATEGORIES.map((category) => (
                    <Button
                      key={category}
                      variant={activeCategory === category ? 'default' : 'outline'}
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => setActiveCategory(category)}
                    >
                      {t(category)}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('Profiles')}</CardTitle>
                <CardDescription>
                  {activeCategory 
                    ? t('{{category}} profiles', { category: t(activeCategory) })
                    : t('All profiles')
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-20">
                      <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full" />
                    </div>
                  ) : filteredProfiles.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>{t('No profiles found')}</p>
                      <p className="text-sm">{t('Click Add below to create a new profile')}</p>
                    </div>
                  ) : (
                    filteredProfiles.map((profile: Profile) => (
                      <div
                        key={profile.id}
                        className={`
                          p-3 rounded-md cursor-pointer
                          ${selectedProfile?.id === profile.id 
                            ? 'bg-primary/10 border border-primary/20' 
                            : 'hover:bg-muted/50 border border-transparent'}
                        `}
                        onClick={() => setSelectedProfile(profile)}
                      >
                        <div className="font-medium">{profile.name}</div>
                        <div className="text-sm text-muted-foreground">{t(profile.category)}</div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full" 
                  onClick={() => {
                    setSelectedProfile(null);
                    setIsEditDialogOpen(true);
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {t('Add')}
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          {/* Right Content Area */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>{t('Profile Details')}</CardTitle>
              <CardDescription>
                {selectedProfile 
                  ? t('Viewing details for {{name}}', { name: selectedProfile.name })
                  : t('Select a profile from the list to view details')
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedProfile ? (
                <div className="text-center py-12 text-muted-foreground">
                  {t('Please select a profile from the list to view details')}
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex flex-col md:flex-row gap-6">
                    {/* Profile Image */}
                    <div className="md:w-1/3">
                      {selectedProfile.imageUrl ? (
                        <div className="w-full aspect-square relative rounded-md overflow-hidden border">
                          <img 
                            src={selectedProfile.imageUrl} 
                            alt={selectedProfile.name}
                            className="w-full h-full object-contain p-2"
                            onError={(e) => {
                              e.currentTarget.src = "/profile-images/sample-frame.png";
                            }}
                          />
                        </div>
                      ) : (
                        <div className="w-full aspect-square bg-muted flex items-center justify-center rounded-md">
                          <span className="text-muted-foreground">{t('No image')}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Profile Information */}
                    <div className="md:w-2/3 space-y-4">
                      <div>
                        <h2 className="text-2xl font-semibold">{selectedProfile.name}</h2>
                        <p className="text-muted-foreground">{selectedProfile.category}</p>
                      </div>
                      
                      {selectedProfile.description && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">{t('Description')}</h3>
                          <p>{selectedProfile.description}</p>
                        </div>
                      )}
                      
                      {selectedProfile.manufacturer && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">{t('Manufacturer')}</h3>
                          <p>{selectedProfile.manufacturer}</p>
                        </div>
                      )}
                      
                      {selectedProfile.profileCode && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">{t('Profile Code')}</h3>
                          <p>{selectedProfile.profileCode}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Technical Details */}
                  {selectedProfile.technicalDetails && (
                    <div>
                      <h3 className="text-md font-semibold mb-2">{t('Technical Details')}</h3>
                      <div className="bg-muted p-4 rounded-md">
                        <pre className="whitespace-pre-wrap text-sm">
                          {JSON.stringify(selectedProfile.technicalDetails, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
            {selectedProfile && (
              <CardFooter className="flex justify-end space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => handleEditProfile(selectedProfile)}
                >
                  <Pencil className="w-4 h-4 mr-2" />
                  {t('Edit')}
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={() => setIsDeleteDialogOpen(true)}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t('Delete')}
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      {/* Edit Profile Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedProfile ? t('Edit Profile') : t('Add New Profile')}
            </DialogTitle>
            <DialogDescription>
              {selectedProfile 
                ? t('Edit the details for this profile') 
                : t('Create a new profile with the form below')
              }
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('Profile name')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Category')}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('Select a category')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {PROFILE_CATEGORIES.map(category => (
                            <SelectItem key={category} value={category}>
                              {t(category)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Description')}</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder={t('Profile description')} 
                        className="resize-none" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      {t('Brief description of the profile')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="manufacturer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Manufacturer')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('Manufacturer name')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="profileCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Profile Code')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('Profile code or reference')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => {
                    // State for the preview image
                    const [imagePreview, setImagePreview] = useState<string | null>(null);
                    
                    // Initialize preview from field value
                    useEffect(() => {
                      if (field.value) {
                        setImagePreview(field.value);
                      }
                    }, [field.value]);
                    
                    return (
                      <FormItem>
                        <FormLabel>{t('Profile Image')}</FormLabel>
                        {/* Create a wrapper div instead of using FormControl directly for the complex UI */}
                        <div>
                          {imagePreview ? (
                            <div className="space-y-2">
                              <div className="relative w-full h-40 bg-muted rounded-md overflow-hidden">
                                {/* Image preview with direct reference */}
                                <img 
                                  src={imagePreview}
                                  alt="Profile" 
                                  className="w-full h-full object-contain" 
                                  onError={() => {
                                    // Fallback if image fails to load
                                    setImagePreview("/profile-images/sample-frame.png");
                                  }}
                                />
                                
                                {/* Remove Button */}
                                <div className="absolute bottom-0 right-0 p-2">
                                  <Button 
                                    type="button" 
                                    variant="destructive" 
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      field.onChange('');
                                      setImagePreview(null);
                                    }}
                                  >
                                    {t('Remove')}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div 
                              className="border-2 border-dashed border-muted-foreground/25 rounded-md p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors h-40 flex flex-col items-center justify-center"
                              onClick={() => {
                                // Trigger file input click
                                const fileInput = document.getElementById('profile-image-upload');
                                if (fileInput) fileInput.click();
                              }}
                              onDragOver={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                e.currentTarget.classList.add('border-primary');
                              }}
                              onDragLeave={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                e.currentTarget.classList.remove('border-primary');
                              }}
                              onDrop={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                e.currentTarget.classList.remove('border-primary');
                                
                                const file = e.dataTransfer.files?.[0];
                                if (file && file.type.startsWith('image/')) {
                                  // Use FileReader for image preview
                                  const reader = new FileReader();
                                  reader.onload = (e) => {
                                    if (e.target?.result) {
                                      const imageDataUrl = e.target.result as string;
                                      setImagePreview(imageDataUrl);
                                      field.onChange(imageDataUrl); // Save actual image data
                                    }
                                  };
                                  reader.readAsDataURL(file);
                                  
                                  // Show feedback
                                  toast({
                                    title: t('Image selected'),
                                    description: t('File upload will be fully implemented in the next phase'),
                                  });
                                }
                              }}
                            >
                              <p className="text-sm text-muted-foreground mb-2">{t('Drag & drop your image here or click to select')}</p>
                              <p className="text-xs text-muted-foreground">{t('Image upload will be implemented in the next phase')}</p>
                            </div>
                          )}
                          <Input 
                            id="profile-image-upload"
                            type="file" 
                            className="hidden" 
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                // Use FileReader for preview
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  if (e.target?.result) {
                                    const imageDataUrl = e.target.result as string;
                                    setImagePreview(imageDataUrl);
                                    field.onChange(imageDataUrl); // Save actual image data
                                  }
                                };
                                reader.readAsDataURL(file);
                                
                                // Show feedback
                                toast({
                                  title: t('Image selected'),
                                  description: t('File upload will be fully implemented in the next phase'),
                                });
                              }
                            }} 
                          />
                          {/* Use FormControl with the Input for the actual field binding */}
                          {/* Using a hidden div instead of Input to avoid type issues */}
                          <FormControl>
                            <div className="hidden">
                              <input
                                type="hidden" 
                                value={field.value || ''}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                              />
                            </div>
                          </FormControl>
                        </div>
                        <FormDescription>
                          {t('Image upload functionality coming soon')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
              
              <FormField
                control={form.control}
                name="technicalDetails"
                render={({ field }) => {
                  // Ensure the value is a string
                  const stringValue = typeof field.value === 'string' 
                    ? field.value 
                    : field.value ? JSON.stringify(field.value, null, 2) : '';
                  
                  return (
                    <FormItem>
                      <FormLabel>{t('Technical Details')}</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder={t('Technical specifications, dimensions, etc.')} 
                          className="resize-none min-h-[120px]"
                          value={stringValue}
                          onChange={(e) => {
                            try {
                              // Try to parse as JSON if it starts with { or [
                              const trimmedValue = e.target.value.trim();
                              if ((trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) && 
                                  (trimmedValue.endsWith('}') || trimmedValue.endsWith(']'))) {
                                const jsonValue = JSON.parse(trimmedValue);
                                field.onChange(jsonValue);
                              } else {
                                field.onChange(e.target.value);
                              }
                            } catch (error) {
                              // If JSON parsing fails, use as string
                              field.onChange(e.target.value);
                            }
                          }}
                          onBlur={field.onBlur}
                          name={field.name}
                          ref={field.ref}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('Enter as text or valid JSON')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  {t('Cancel')}
                </Button>
                <Button type="submit" disabled={createProfileMutation.isPending || updateProfileMutation.isPending}>
                  {(createProfileMutation.isPending || updateProfileMutation.isPending) ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      {t('Saving...')}
                    </div>
                  ) : (
                    selectedProfile ? t('Update Profile') : t('Create Profile')
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('Confirm Deletion')}</DialogTitle>
            <DialogDescription>
              <p>{t('Are you sure you want to delete this profile?')}</p>
              <p>{t('This action cannot be undone.')}</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {t('Cancel')}
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteProfile}
              disabled={deleteProfileMutation.isPending}
            >
              {deleteProfileMutation.isPending ? (
                <div className="flex items-center">
                  <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                  {t('Deleting...')}
                </div>
              ) : t('Delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}