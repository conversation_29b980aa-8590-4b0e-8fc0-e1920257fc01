import { execSync } from 'child_process';
import { existsSync } from 'fs';

console.log('🚀 Setting up local PostgreSQL database for Windows...\n');

// Check if PostgreSQL is already installed
function checkPostgreSQL() {
  try {
    execSync('psql --version', { stdio: 'ignore' });
    console.log('✅ PostgreSQL is already installed');
    return true;
  } catch (error) {
    console.log('❌ PostgreSQL is not installed');
    return false;
  }
}

// Check if we can connect to PostgreSQL
async function testConnection() {
  try {
    const { Pool } = await import('pg');
    const pool = new Pool({
      host: 'localhost',
      port: 5432,
      database: 'postgres', // Connect to default database first
      user: 'postgres',
      password: 'admin123'
    });
    
    await pool.query('SELECT 1');
    await pool.end();
    console.log('✅ PostgreSQL connection successful');
    return true;
  } catch (error) {
    console.log('❌ Cannot connect to PostgreSQL:', error.message);
    return false;
  }
}

// Create database and user
async function setupDatabase() {
  try {
    const { Pool } = await import('pg');
    
    // Connect to default postgres database
    const adminPool = new Pool({
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'admin123'
    });
    
    console.log('📋 Creating database and user...');
    
    // Create user if not exists
    try {
      await adminPool.query(`CREATE USER admin WITH PASSWORD 'admin123'`);
      console.log('✅ Created user: admin');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  User admin already exists');
      } else {
        throw error;
      }
    }
    
    // Create database if not exists
    try {
      await adminPool.query(`CREATE DATABASE aluminum_optimizer OWNER admin`);
      console.log('✅ Created database: aluminum_optimizer');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  Database aluminum_optimizer already exists');
      } else {
        throw error;
      }
    }
    
    // Grant privileges
    await adminPool.query(`GRANT ALL PRIVILEGES ON DATABASE aluminum_optimizer TO admin`);
    console.log('✅ Granted privileges to admin user');
    
    await adminPool.end();
    
    // Test connection to new database
    const testPool = new Pool({
      host: 'localhost',
      port: 5432,
      database: 'aluminum_optimizer',
      user: 'admin',
      password: 'admin123'
    });
    
    await testPool.query('SELECT 1');
    await testPool.end();
    console.log('✅ Database setup completed successfully');
    
    return true;
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔍 Checking PostgreSQL installation...');
  
  if (!checkPostgreSQL()) {
    console.log('\n📥 PostgreSQL is not installed. Please install it first:');
    console.log('1. Download PostgreSQL from: https://www.postgresql.org/download/windows/');
    console.log('2. During installation, set the password for "postgres" user to: admin123');
    console.log('3. Make sure PostgreSQL service is running');
    console.log('4. Run this script again after installation');
    process.exit(1);
  }
  
  console.log('🔗 Testing PostgreSQL connection...');
  if (!(await testConnection())) {
    console.log('\n❌ Cannot connect to PostgreSQL. Please check:');
    console.log('1. PostgreSQL service is running');
    console.log('2. Default password for "postgres" user is "admin123"');
    console.log('3. PostgreSQL is listening on port 5432');
    console.log('\nTo reset postgres password:');
    console.log('1. Open Command Prompt as Administrator');
    console.log('2. Run: net stop postgresql-x64-15 (or your version)');
    console.log('3. Run: net start postgresql-x64-15');
    console.log('4. Run: psql -U postgres');
    console.log('5. Run: ALTER USER postgres PASSWORD \'admin123\';');
    process.exit(1);
  }
  
  console.log('🏗️  Setting up database...');
  if (!(await setupDatabase())) {
    process.exit(1);
  }
  
  console.log('📋 Running database migrations...');
  try {
    execSync('npm run db:push', { stdio: 'inherit' });
    console.log('✅ Database schema created successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
  
  console.log('\n🎯 Local PostgreSQL database is ready!');
  console.log('📊 Database URL: postgresql://admin:admin123@localhost:5432/aluminum_optimizer');
  console.log('🔧 You can connect using pgAdmin or any PostgreSQL client');
  
  console.log('\n📝 Next steps:');
  console.log('1. Run: node migrate-data.js (to migrate data from Neon)');
  console.log('2. Run: npm run dev (to start the application)');
}

main().catch(console.error);
