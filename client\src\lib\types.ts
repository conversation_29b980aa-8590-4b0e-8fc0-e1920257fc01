// Common Settings
export interface BaseStockSettings {
  kerf: number;
}

// Stock material settings for linear cutting (profiles)
export interface StockSettings extends BaseStockSettings {
  stockLength: number;
  endTrim: number;
}

// Stock settings for 2D glass cutting
export interface GlassStockSettings extends BaseStockSettings {
  sheetWidth: number;
  sheetHeight: number;
  edgeTrim: number;  // Trim around edges of sheet
}

// Cut piece with ID for tracking in UI
export interface CutPiece {
  id: number;
  length: number;
  quantity: number;
  description: string;
  profileType: string; // Now a string for custom profile names
}

// Individual cut used in optimization
export interface CutItem {
  length: number;
  description: string;
  profileType: string;
}

// Cutting plan for a single stock piece
export interface CuttingPlan {
  stockPieceId: number;
  cuts: CutItem[];
  waste: number;
  materialUsagePercent: number;
  profileType: string; // Added profile type to each cutting plan
}

// Result for a specific profile type
export interface ProfileResult {
  stockPiecesCount: number;
  materialUsagePercent: number;
  totalWaste: number;
  totalCuts: number;
  cuttingPlans: CuttingPlan[];
  profileType: string;
}

// Complete optimization result
export interface OptimizationResult {
  profileResults: ProfileResult[]; // Now an array of profile results for all unique profiles
  totalStockPieces: number;
}

// Glass cutting types (2D cutting)
export interface GlassPiece {
  id: number;
  width: number;
  height: number;
  quantity: number;
  description: string;
  glassType: string; // Type of glass (clear, tinted, etc.)
  canRotate: boolean; // Whether the piece can be rotated 90° for better fit
}

// Individual glass cut placed on a sheet
export interface GlassCutItem {
  width: number;
  height: number;
  description: string;
  glassType: string;
  x: number; // X position on the sheet
  y: number; // Y position on the sheet
  rotated: boolean; // Whether this piece was rotated from original orientation
}

// Glass cutting plan for a single sheet
export interface GlassSheetPlan {
  sheetId: number;
  cuts: GlassCutItem[];
  wasteArea: number; // Unused area in square units
  materialUsagePercent: number;
  glassType: string;
}

// Result for a specific glass type
export interface GlassTypeResult {
  sheetsCount: number;
  materialUsagePercent: number;
  totalWasteArea: number;
  sheetPlans: GlassSheetPlan[];
  glassType: string;
}

// Complete glass optimization result
export interface GlassOptimizationResult {
  glassTypeResults: GlassTypeResult[];
  totalSheets: number;
}

// Saved project
export interface Project {
  id: string;
  name: string;
  stockSettings: StockSettings;
  cutList: CutPiece[];
  optimizationResult?: OptimizationResult;
  // Glass related fields
  glassStockSettings?: GlassStockSettings;
  glassCutList?: GlassPiece[];
  glassOptimizationResult?: GlassOptimizationResult;
  createdAt: string;
  updatedAt: string;
}
