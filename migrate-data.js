import dotenv from 'dotenv';
import { Pool as PgPool } from 'pg';
import { Pool as NeonPool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';

dotenv.config();

// Configure Neon
neonConfig.webSocketConstructor = ws;

// Neon database connection
const neonPool = new NeonPool({
  connectionString: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
});

// Local PostgreSQL connection
const localPool = new PgPool({
  connectionString: "postgresql://admin:admin123@localhost:5432/aluminum_optimizer"
});

// List of tables to migrate (in dependency order)
const tables = [
  'users',
  'profiles',
  'window_designs',
  'window_design_components',
  'window_design_glass_specifications',
  'window_projects',
  'project_window_items',
  'accessories',
  'invoices',
  'subscription_plans'
];

async function getTableSchema(tableName) {
  const client = await neonPool.connect();
  try {
    // Get table structure
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = $1 
      ORDER BY ordinal_position
    `, [tableName]);
    
    return result.rows;
  } finally {
    client.release();
  }
}

async function exportTableData(tableName) {
  const client = await neonPool.connect();
  try {
    console.log(`Exporting data from table: ${tableName}`);
    const result = await client.query(`SELECT * FROM ${tableName}`);
    console.log(`Found ${result.rows.length} rows in ${tableName}`);
    return result.rows;
  } catch (error) {
    console.error(`Error exporting ${tableName}:`, error.message);
    return [];
  } finally {
    client.release();
  }
}

async function importTableData(tableName, data, schema) {
  if (data.length === 0) {
    console.log(`No data to import for ${tableName}`);
    return;
  }

  const client = await localPool.connect();
  try {
    console.log(`Importing ${data.length} rows to ${tableName}`);
    
    // Get column names
    const columns = schema.map(col => col.column_name);
    const columnList = columns.join(', ');
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');
    
    // Insert data row by row to handle any conflicts
    for (const row of data) {
      try {
        const values = columns.map(col => row[col]);
        await client.query(
          `INSERT INTO ${tableName} (${columnList}) VALUES (${placeholders}) ON CONFLICT DO NOTHING`,
          values
        );
      } catch (error) {
        console.warn(`Warning: Could not insert row in ${tableName}:`, error.message);
        // Continue with next row
      }
    }
    
    console.log(`Successfully imported data to ${tableName}`);
  } catch (error) {
    console.error(`Error importing to ${tableName}:`, error.message);
  } finally {
    client.release();
  }
}

async function checkTableExists(tableName) {
  const client = await localPool.connect();
  try {
    const result = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = $1
      )
    `, [tableName]);
    return result.rows[0].exists;
  } finally {
    client.release();
  }
}

async function migrateData() {
  console.log('Starting data migration from Neon to local PostgreSQL...');
  
  try {
    // Test connections
    console.log('Testing Neon connection...');
    const neonClient = await neonPool.connect();
    await neonClient.query('SELECT 1');
    neonClient.release();
    console.log('✅ Neon connection successful');
    
    console.log('Testing local PostgreSQL connection...');
    const localClient = await localPool.connect();
    await localClient.query('SELECT 1');
    localClient.release();
    console.log('✅ Local PostgreSQL connection successful');
    
    // Migrate each table
    for (const tableName of tables) {
      console.log(`\n--- Processing table: ${tableName} ---`);
      
      // Check if table exists in local database
      const exists = await checkTableExists(tableName);
      if (!exists) {
        console.log(`⚠️  Table ${tableName} does not exist in local database. Skipping...`);
        continue;
      }
      
      // Get table schema
      const schema = await getTableSchema(tableName);
      
      // Export data from Neon
      const data = await exportTableData(tableName);
      
      // Import data to local database
      await importTableData(tableName, data, schema);
    }
    
    console.log('\n🎉 Data migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await neonPool.end();
    await localPool.end();
  }
}

// Run migration
migrateData();
