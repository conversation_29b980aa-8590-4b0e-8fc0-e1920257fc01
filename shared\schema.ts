import { pgTable, text, serial, integer, boolean, jsonb, real, timestamp, numeric } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Base user schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  name: text("name"),
  email: text("email"),
  companyName: text("company_name"), // Company name field
  country: text("country"), // Country field
  mobileNumber: text("mobile_number"), // Mobile number field
  role: text("role").default("user").notNull(), // user, admin
  accountStatus: text("account_status").default("active").notNull(), // active, suspended, pending_payment
  subscriptionPlan: text("subscription_plan").default("free").notNull(), // free, pro
  subscriptionExpiry: text("subscription_expiry"), // ISO date string
  maxQuantity: integer("max_quantity").default(50).notNull(), // 50 for free, unlimited for pro
  stripeCustomerId: text("stripe_customer_id"), // Stripe customer ID for payment processing
  stripeSubscriptionId: text("stripe_subscription_id"), // Stripe subscription ID
  paymentIssueDate: text("payment_issue_date"), // Date when payment issue was recorded
  statusNote: text("status_note"), // Admin note about account status
  passwordChanged: boolean("password_changed").default(true), // Indicates if user has changed their temporary password
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  name: true,
  email: true,
  companyName: true,
  country: true,
  mobileNumber: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Cut piece schema
export const cutPieces = pgTable("cut_pieces", {
  id: serial("id").primaryKey(),
  length: real("length").notNull(),
  quantity: integer("quantity").notNull().default(1),
  description: text("description"),
  userId: integer("user_id").references(() => users.id),
});

export const insertCutPieceSchema = createInsertSchema(cutPieces).pick({
  length: true,
  quantity: true,
  description: true,
  userId: true,
});

export type InsertCutPiece = z.infer<typeof insertCutPieceSchema>;
export type CutPiece = typeof cutPieces.$inferSelect;

// Project settings schema
export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  stockLength: real("stock_length").notNull(),
  kerf: real("kerf").notNull(),
  endTrim: real("end_trim").notNull(),
  userId: integer("user_id").references(() => users.id),
  cutPieces: jsonb("cut_pieces"),
  optimizationResults: jsonb("optimization_results"),
});

export const insertProjectSchema = createInsertSchema(projects).pick({
  name: true,
  stockLength: true,
  kerf: true,
  endTrim: true,
  userId: true,
  cutPieces: true,
  optimizationResults: true,
});

export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Project = typeof projects.$inferSelect;

// Optimization result schema with cutting plans
export const optimizationResults = pgTable("optimization_results", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").references(() => projects.id),
  stockPiecesCount: integer("stock_pieces_count").notNull(),
  materialUsagePercent: real("material_usage_percent").notNull(),
  totalWaste: real("total_waste").notNull(),
  totalCuts: integer("total_cuts").notNull(),
  cuttingPlans: jsonb("cutting_plans").notNull(),
  createdAt: text("created_at").notNull(),
});

export const insertOptimizationResultSchema = createInsertSchema(optimizationResults).pick({
  projectId: true,
  stockPiecesCount: true,
  materialUsagePercent: true,
  totalWaste: true,
  totalCuts: true,
  cuttingPlans: true,
  createdAt: true,
});

export type InsertOptimizationResult = z.infer<typeof insertOptimizationResultSchema>;
export type OptimizationResult = typeof optimizationResults.$inferSelect;

// Profile schema - stores information about aluminum profiles
export const profiles = pgTable("profiles", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category").notNull(), // frame, panel, glazing bead, etc.
  manufacturer: text("manufacturer"),
  profileCode: text("profile_code"), // New field for profile code
  imageUrl: text("image_url"),
  thumbnailUrl: text("thumbnail_url"),
  technicalDetails: jsonb("technical_details"), // width, height, thickness, material, etc.
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
});

export const insertProfileSchema = createInsertSchema(profiles).pick({
  name: true,
  description: true,
  category: true,
  manufacturer: true,
  profileCode: true,
  imageUrl: true,
  thumbnailUrl: true,
  technicalDetails: true,
  userId: true,
});

export type InsertProfile = z.infer<typeof insertProfileSchema>;
export type Profile = typeof profiles.$inferSelect;

// Window design schema - templates for window designs
export const windowDesigns = pgTable("window_designs", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category").notNull(), // sliding, casement, fixed, etc.
  imageUrl: text("image_url"),
  thumbnailUrl: text("thumbnail_url"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
  isPublic: boolean("is_public").default(false).notNull(),
});

export const insertWindowDesignSchema = createInsertSchema(windowDesigns).pick({
  name: true,
  description: true,
  category: true,
  imageUrl: true,
  thumbnailUrl: true,
  userId: true,
  isPublic: true,
});

export type InsertWindowDesign = z.infer<typeof insertWindowDesignSchema>;
export type WindowDesign = typeof windowDesigns.$inferSelect;

// Component schema - individual parts of a window design
export const components = pgTable("components", {
  id: serial("id").primaryKey(),
  windowDesignId: integer("window_design_id").references(() => windowDesigns.id).notNull(),
  profileId: integer("profile_id").references(() => profiles.id).notNull(),
  name: text("name").notNull(),
  componentType: text("component_type").notNull(), // frame, panel, mullion, glazing bead, etc.
  widthFormula: text("width_formula"), // e.g., "window_width - 10" - optional now
  widthQuantity: text("width_quantity").default("1").notNull(), // quantity for width profiles
  heightFormula: text("height_formula"), // e.g., "window_height - 10"
  heightQuantity: text("height_quantity").default("1").notNull(), // quantity for height profiles
  isFixedHeight: boolean("is_fixed_height").default(false), // Whether this component maintains fixed height regardless of window height
  fixedHeightValue: text("fixed_height_value"), // Fixed height formula or value for components that don't adjust with window height
  quantityFormula: text("quantity_formula").default("1").notNull(), // e.g., "2" or "Math.ceil(window_width/600)" - for backwards compatibility
  leftCutDegree: integer("left_cut_degree").default(90), // Cutting angle for left side (90 or 45 degrees)
  rightCutDegree: integer("right_cut_degree").default(90), // Cutting angle for right side (90 or 45 degrees)
  notes: text("notes"),
  position: jsonb("position"), // {x, y} coordinates for visual display
  customProperties: jsonb("custom_properties"), // Any additional properties specific to the component
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertComponentSchema = createInsertSchema(components).pick({
  windowDesignId: true,
  profileId: true,
  name: true,
  componentType: true,
  widthFormula: true,
  widthQuantity: true,
  heightFormula: true,
  heightQuantity: true,
  isFixedHeight: true,
  fixedHeightValue: true,
  quantityFormula: true,
  leftCutDegree: true,
  rightCutDegree: true,
  notes: true,
  position: true,
  customProperties: true,
});

export type InsertComponent = z.infer<typeof insertComponentSchema>;
export type Component = typeof components.$inferSelect;

// Glass specifications schema - for window designs
export const glassSpecifications = pgTable("glass_specifications", {
  id: serial("id").primaryKey(),
  windowDesignId: integer("window_design_id").references(() => windowDesigns.id).notNull(),
  name: text("name").notNull(),
  glassType: text("glass_type").notNull(), // clear, tinted, low-e, etc.
  thickness: real("thickness").notNull(),
  widthFormula: text("width_formula").notNull(), // e.g., "window_width - 20"
  heightFormula: text("height_formula").notNull(), // e.g., "window_height - 20"
  isFixedHeight: boolean("is_fixed_height").default(false), // Whether glass maintains fixed height regardless of window height
  fixedHeightValue: text("fixed_height_value"), // Fixed height formula or value for glass that doesn't adjust with window height
  quantityFormula: text("quantity_formula").default("1").notNull(),
  notes: text("notes"),
  position: jsonb("position"), // {x, y} coordinates for visual display
  customProperties: jsonb("custom_properties"), // Any additional properties
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertGlassSpecificationSchema = createInsertSchema(glassSpecifications).pick({
  windowDesignId: true,
  name: true,
  glassType: true,
  thickness: true,
  widthFormula: true,
  heightFormula: true,
  isFixedHeight: true,
  fixedHeightValue: true, 
  quantityFormula: true,
  notes: true,
  position: true,
  customProperties: true,
});

export type InsertGlassSpecification = z.infer<typeof insertGlassSpecificationSchema>;
export type GlassSpecification = typeof glassSpecifications.$inferSelect;

// Window cutting list schema - generated cutting lists
export const windowCuttingLists = pgTable("window_cutting_lists", {
  id: serial("id").primaryKey(),
  windowDesignId: integer("window_design_id").references(() => windowDesigns.id).notNull(),
  name: text("name").notNull(),
  width: real("width").notNull(),
  height: real("height").notNull(),
  quantity: integer("quantity").default(1).notNull(),
  components: jsonb("components").notNull(), // Calculated components with actual dimensions
  glassItems: jsonb("glass_items").notNull(), // Calculated glass with actual dimensions
  createdAt: timestamp("created_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
  notes: text("notes"),
});

export const insertWindowCuttingListSchema = createInsertSchema(windowCuttingLists).pick({
  windowDesignId: true,
  name: true,
  width: true,
  height: true,
  quantity: true,
  components: true,
  glassItems: true,
  userId: true,
  notes: true,
});

export type InsertWindowCuttingList = z.infer<typeof insertWindowCuttingListSchema>;
export type WindowCuttingList = typeof windowCuttingLists.$inferSelect;

// Window Projects schema - collections of window designs for a project
export const windowProjects = pgTable("window_projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  clientName: text("client_name"),
  clientContact: text("client_contact"),
  location: text("location"),
  dueDate: text("due_date"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
  status: text("status").default("draft").notNull(), // draft, in-progress, completed
  notes: text("notes"),
});

export const insertWindowProjectSchema = createInsertSchema(windowProjects).pick({
  name: true,
  description: true,
  clientName: true,
  clientContact: true,
  location: true,
  dueDate: true,
  userId: true,
  status: true,
  notes: true,
});

export type InsertWindowProject = z.infer<typeof insertWindowProjectSchema>;
export type WindowProject = typeof windowProjects.$inferSelect;

// Project Window Items schema - links designs to projects with specific dimensions
export const projectWindowItems = pgTable("project_window_items", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").references(() => windowProjects.id).notNull(),
  windowDesignId: integer("window_design_id").references(() => windowDesigns.id).notNull(),
  windowCuttingListId: integer("window_cutting_list_id").references(() => windowCuttingLists.id),
  width: real("width").notNull(),
  height: real("height").notNull(),
  quantity: integer("quantity").default(1).notNull(),
  windowType: text("window_type"), // Type of window (e.g., Casement, Sliding, Fixed)
  position: integer("position").default(0).notNull(), // Order within the project
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertProjectWindowItemSchema = createInsertSchema(projectWindowItems).pick({
  projectId: true,
  windowDesignId: true,
  windowCuttingListId: true,
  width: true,
  height: true,
  quantity: true,
  windowType: true,
  position: true,
  notes: true,
});

export type InsertProjectWindowItem = z.infer<typeof insertProjectWindowItemSchema>;
export type ProjectWindowItem = typeof projectWindowItems.$inferSelect;

// Accessories schema - for hardware, seals, fasteners, etc.
export const accessories = pgTable("accessories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // EPDM, roller, screen mesh, screws, foam, silicone, etc.
  subtype: text("subtype"), // For frame, for glazing bead, for panel, etc.
  description: text("description"),
  manufacturer: text("manufacturer"),
  imageUrl: text("image_url"),
  thumbnailUrl: text("thumbnail_url"),
  technicalDetails: jsonb("technical_details"), // size, material, color, etc.
  stock: integer("stock"), // Inventory count if needed
  price: real("price"), // Unit price if needed
  unit: text("unit").default("piece").notNull(), // piece, meter, kg, box, etc.
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
});

export const insertAccessorySchema = createInsertSchema(accessories).pick({
  name: true,
  type: true,
  subtype: true,
  description: true,
  manufacturer: true,
  imageUrl: true,
  thumbnailUrl: true,
  technicalDetails: true,
  stock: true,
  price: true,
  unit: true,
  userId: true,
});

export type InsertAccessory = z.infer<typeof insertAccessorySchema>;
export type Accessory = typeof accessories.$inferSelect;

// Component accessories - links accessories to specific components
export const componentAccessories = pgTable("component_accessories", {
  id: serial("id").primaryKey(),
  componentId: integer("component_id").references(() => components.id).notNull(),
  accessoryId: integer("accessory_id").references(() => accessories.id).notNull(),
  quantity: real("quantity").default(1).notNull(),
  quantityFormula: text("quantity_formula"), // Formula to calculate the quantity based on window dimensions
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertComponentAccessorySchema = createInsertSchema(componentAccessories).pick({
  componentId: true,
  accessoryId: true,
  quantity: true,
  quantityFormula: true,
  notes: true,
});

export type InsertComponentAccessory = z.infer<typeof insertComponentAccessorySchema>;
export type ComponentAccessory = typeof componentAccessories.$inferSelect;

// Machining operations schema - for defining CNC operations
export const machiningOperations = pgTable("machining_operations", {
  id: serial("id").primaryKey(),
  componentId: integer("component_id").references(() => components.id).notNull(),
  operationType: text("operation_type").notNull(), // groove, hole, slot, pocket, notch, miter, etc.
  name: text("name").notNull(), // Name of the operation (e.g., "Lock cylinder hole")
  positionFormula: text("position_formula").notNull(), // Formula to calculate position from component start (e.g., "width/2")
  edgeReference: text("edge_reference").notNull(), // Which edge to reference: top, bottom, left, right, front, back
  width: real("width"), // Width of groove/slot/hole
  depth: real("depth"), // Depth of groove/slot/hole
  length: real("length"), // Length of groove/slot
  diameter: real("diameter"), // Diameter of hole
  angle: real("angle"), // Angle for miter cuts
  offsetX: real("offset_x").default(0).notNull(), // X offset from position
  offsetY: real("offset_y").default(0).notNull(), // Y offset from position
  parameters: jsonb("parameters"), // Additional parameters specific to the operation
  toolId: integer("tool_id"), // Reference to a specific tool if needed
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
});

export const insertMachiningOperationSchema = createInsertSchema(machiningOperations).pick({
  componentId: true,
  operationType: true,
  name: true,
  positionFormula: true,
  edgeReference: true,
  width: true,
  depth: true,
  length: true,
  diameter: true,
  angle: true,
  offsetX: true,
  offsetY: true,
  parameters: true,
  toolId: true,
  userId: true,
});

export type InsertMachiningOperation = z.infer<typeof insertMachiningOperationSchema>;
export type MachiningOperation = typeof machiningOperations.$inferSelect;

// CNC Tools schema - for defining tools used in machining operations
export const cncTools = pgTable("cnc_tools", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // drill bit, router bit, saw blade, etc.
  diameter: real("diameter").notNull(),
  length: real("length"),
  materialType: text("material_type"), // HSS, carbide, diamond, etc.
  speedRPM: integer("speed_rpm"), // Rotational speed
  feedRate: real("feed_rate"), // Feed rate in mm/min
  description: text("description"),
  imageUrl: text("image_url"),
  parameters: jsonb("parameters"), // Additional tool parameters
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id),
});

export const insertCncToolSchema = createInsertSchema(cncTools).pick({
  name: true,
  type: true,
  diameter: true,
  length: true,
  materialType: true,
  speedRPM: true,
  feedRate: true,
  description: true,
  imageUrl: true,
  parameters: true,
  userId: true,
});

export type InsertCncTool = z.infer<typeof insertCncToolSchema>;
export type CncTool = typeof cncTools.$inferSelect;

// Invoices schema - for storing user payment history
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  amount: numeric("amount").notNull(),
  currency: text("currency").default("USD").notNull(),
  status: text("status").default("paid").notNull(), // paid, pending, failed
  description: text("description").notNull(),
  subscriptionPlan: text("subscription_plan").notNull(), // free, pro, platinum
  subscriptionPeriod: text("subscription_period").notNull(), // monthly, annual
  issuedDate: timestamp("issued_date").defaultNow().notNull(),
  dueDate: timestamp("due_date"),
  paidDate: timestamp("paid_date"),
  paymentMethod: text("payment_method").default("credit_card").notNull(), // credit_card, bank_transfer, paypal
  billingAddress: jsonb("billing_address"), // Billing address object
  items: jsonb("items"), // Line items
  stripeInvoiceId: text("stripe_invoice_id"), // Reference to Stripe invoice if applicable
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertInvoiceSchema = createInsertSchema(invoices).pick({
  userId: true,
  invoiceNumber: true,
  amount: true,
  currency: true,
  status: true,
  description: true,
  subscriptionPlan: true,
  subscriptionPeriod: true,
  issuedDate: true,
  dueDate: true,
  paidDate: true,
  paymentMethod: true,
  billingAddress: true,
  items: true,
  stripeInvoiceId: true,
});

export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type Invoice = typeof invoices.$inferSelect;
