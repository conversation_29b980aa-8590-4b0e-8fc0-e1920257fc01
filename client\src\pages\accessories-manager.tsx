import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Link } from "wouter";
import { insertAccessorySchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, X, Pencil, Trash2, ImagePlus } from "lucide-react";

// AccessoryType represents the types of accessories
const AccessoryType = {
  SEAL: "seal",
  ROLLER: "roller",
  HANDLE: "handle",
  LOCK: "lock",
  BRUSH: "brush",
  GASKET: "gasket",
  HARDWARE: "hardware",
  FASTENER: "fastener",
  OTHER: "other"
} as const;

// Validation schema for the accessory form
const accessoryFormSchema = insertAccessorySchema.extend({
  // Additional client-side validations can be added here
  name: z.string().min(2, "Name must be at least 2 characters"),
  type: z.string().min(1, "Type is required"),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  stock: z.coerce.number().int().min(0, "Stock must be a positive integer")
});

type AccessoryFormValues = z.infer<typeof accessoryFormSchema>;

const AccessoriesManager: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<string>("all");
  const [selectedAccessory, setSelectedAccessory] = useState<number | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Define the query for accessories
  const { data: accessories, isLoading, error } = useQuery({
    queryKey: ["/api/accessories", activeTab !== "all" ? activeTab : undefined],
    queryFn: async () => {
      const url = activeTab !== "all"
        ? `/api/accessories?type=${activeTab}`
        : "/api/accessories";
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch accessories");
      }
      return response.json();
    }
  });

  // Form setup for creating/editing accessories
  const form = useForm<AccessoryFormValues>({
    resolver: zodResolver(accessoryFormSchema),
    defaultValues: {
      name: "",
      type: AccessoryType.SEAL,
      subtype: "",
      description: "",
      manufacturer: "",
      technicalDetails: "",
      price: 0,
      stock: 0,
      unit: "pcs",
      imageUrl: "",
      thumbnailUrl: ""
    }
  });

  // Define mutations for creating, updating, and deleting accessories
  const createAccessoryMutation = useMutation({
    mutationFn: async (data: AccessoryFormValues) => {
      const response = await apiRequest("POST", "/api/accessories", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/accessories"] });
      toast({
        title: "Success",
        description: "Accessory created successfully",
      });
      setShowCreateDialog(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to create accessory: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const updateAccessoryMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<AccessoryFormValues> }) => {
      const response = await apiRequest("PUT", `/api/accessories/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/accessories"] });
      toast({
        title: "Success",
        description: "Accessory updated successfully",
      });
      setShowEditDialog(false);
      setSelectedAccessory(null);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update accessory: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const deleteAccessoryMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/accessories/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/accessories"] });
      toast({
        title: "Success",
        description: "Accessory deleted successfully",
      });
      setShowDeleteDialog(false);
      setSelectedAccessory(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete accessory: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  // Form handlers
  const onCreateSubmit = (data: AccessoryFormValues) => {
    createAccessoryMutation.mutate(data);
  };

  const onEditSubmit = (data: AccessoryFormValues) => {
    if (selectedAccessory) {
      updateAccessoryMutation.mutate({ id: selectedAccessory, data });
    }
  };

  const onDeleteConfirm = () => {
    if (selectedAccessory) {
      deleteAccessoryMutation.mutate(selectedAccessory);
    }
  };

  const handleEdit = (accessory: any) => {
    setSelectedAccessory(accessory.id);
    // Populate the form with the selected accessory's data
    form.reset({
      name: accessory.name,
      type: accessory.type,
      subtype: accessory.subtype || "",
      description: accessory.description || "",
      manufacturer: accessory.manufacturer || "",
      technicalDetails: accessory.technicalDetails || "",
      price: accessory.price,
      stock: accessory.stock,
      unit: accessory.unit,
      imageUrl: accessory.imageUrl || "",
      thumbnailUrl: accessory.thumbnailUrl || ""
    });
    setShowEditDialog(true);
  };

  const handleDelete = (accessory: any) => {
    setSelectedAccessory(accessory.id);
    setShowDeleteDialog(true);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">Failed to load accessories. Please try again later.</span>
        </div>
        <Button className="mt-4" onClick={() => queryClient.invalidateQueries({ queryKey: ["/api/accessories"] })}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Accessories Management</h1>
          <p className="text-gray-500">Manage accessories for window components</p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/">Back to Home</Link>
          </Button>
          <Button onClick={() => {
            form.reset();
            setShowCreateDialog(true);
          }}>
            <Plus className="mr-2 h-4 w-4" /> Add New Accessory
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <div className="mb-6">
          <h2 className="text-lg font-medium mb-2">Filter by Type</h2>
          <TabsList className="grid grid-cols-5 md:flex md:flex-wrap md:space-x-2">
            <TabsTrigger value="all">All Types</TabsTrigger>
            <TabsTrigger value={AccessoryType.SEAL}>Seals</TabsTrigger>
            <TabsTrigger value={AccessoryType.ROLLER}>Rollers</TabsTrigger>
            <TabsTrigger value={AccessoryType.HANDLE}>Handles</TabsTrigger>
            <TabsTrigger value={AccessoryType.LOCK}>Locks</TabsTrigger>
            <TabsTrigger value={AccessoryType.BRUSH}>Brushes</TabsTrigger>
            <TabsTrigger value={AccessoryType.GASKET}>Gaskets</TabsTrigger>
            <TabsTrigger value={AccessoryType.HARDWARE}>Hardware</TabsTrigger>
            <TabsTrigger value={AccessoryType.FASTENER}>Fasteners</TabsTrigger>
            <TabsTrigger value={AccessoryType.OTHER}>Other</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="mt-0">
          <AccessoriesTable 
            accessories={accessories} 
            onEdit={handleEdit} 
            onDelete={handleDelete} 
          />
        </TabsContent>

        {Object.values(AccessoryType).map(type => (
          <TabsContent key={type} value={type} className="mt-0">
            <AccessoriesTable 
              accessories={accessories?.filter((a: any) => a.type === type)} 
              onEdit={handleEdit} 
              onDelete={handleDelete} 
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create Accessory Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Accessory</DialogTitle>
            <DialogDescription>
              Create a new accessory item. Fill in the details below.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onCreateSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Accessory name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(AccessoryType).map(([key, value]) => (
                            <SelectItem key={value} value={value}>
                              {key.charAt(0) + key.slice(1).toLowerCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="subtype"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subtype</FormLabel>
                      <FormControl>
                        <Input placeholder="Subtype (optional)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="manufacturer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Manufacturer</FormLabel>
                      <FormControl>
                        <Input placeholder="Manufacturer (optional)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="stock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock</FormLabel>
                      <FormControl>
                        <Input type="number" step="1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="unit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit</FormLabel>
                      <FormControl>
                        <Input placeholder="pcs, meters, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Description (optional)" 
                        className="min-h-[80px]" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createAccessoryMutation.isPending}>
                  {createAccessoryMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create Accessory
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Accessory Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Accessory</DialogTitle>
            <DialogDescription>
              Update the accessory details.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onEditSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Accessory name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(AccessoryType).map(([key, value]) => (
                            <SelectItem key={value} value={value}>
                              {key.charAt(0) + key.slice(1).toLowerCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="subtype"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subtype</FormLabel>
                      <FormControl>
                        <Input placeholder="Subtype (optional)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="manufacturer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Manufacturer</FormLabel>
                      <FormControl>
                        <Input placeholder="Manufacturer (optional)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="stock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock</FormLabel>
                      <FormControl>
                        <Input type="number" step="1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="unit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit</FormLabel>
                      <FormControl>
                        <Input placeholder="pcs, meters, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Description (optional)" 
                        className="min-h-[80px]" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateAccessoryMutation.isPending}>
                  {updateAccessoryMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Update Accessory
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this accessory?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the accessory
              and remove it from any components it's associated with.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={onDeleteConfirm}
              disabled={deleteAccessoryMutation.isPending}
              className="bg-red-500 hover:bg-red-600"
            >
              {deleteAccessoryMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

// Accessory Table Component
interface AccessoriesTableProps {
  accessories: any[];
  onEdit: (accessory: any) => void;
  onDelete: (accessory: any) => void;
}

const AccessoriesTable: React.FC<AccessoriesTableProps> = ({ accessories, onEdit, onDelete }) => {
  if (!accessories || accessories.length === 0) {
    return (
      <div className="bg-gray-50 rounded-md p-8 text-center">
        <p className="text-gray-500">No accessories found in this category.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type/Subtype</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {accessories.map((accessory: any) => (
            <tr key={accessory.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                {accessory.name}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">{accessory.type}</div>
                {accessory.subtype && (
                  <div className="text-sm text-gray-500">{accessory.subtype}</div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {accessory.manufacturer || "-"}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                ${accessory.price.toFixed(2)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {accessory.stock} {accessory.unit}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-center">
                <div className="flex justify-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => onEdit(accessory)}
                    className="h-8 px-2 text-blue-600"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => onDelete(accessory)}
                    className="h-8 px-2 text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default AccessoriesManager;