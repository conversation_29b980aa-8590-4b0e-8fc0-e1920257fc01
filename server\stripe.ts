import Stripe from 'stripe';
import { storage } from './storage';
import { sendWelcomeEmail } from './email';

// Define pricing constants
export const PRICING = {
  monthly: 12,  // $12 per month
  annual: 130   // $130 per year (save $14 compared to monthly)
};

if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === 'sk_test_placeholder') {
  console.warn('STRIPE_SECRET_KEY not configured. Stripe functionality will be disabled.');
}

export const stripe = process.env.STRIPE_SECRET_KEY && process.env.STRIPE_SECRET_KEY !== 'sk_test_placeholder'
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2022-11-15' as any,
    })
  : null;

// Using pricing constants from above

/**
 * Create a customer in Stripe for a user
 */
export async function createStripeCustomer(user: { email: string | null, name: string | null, id: number }) {
  try {
    if (!stripe) {
      throw new Error('Stripe is not configured');
    }

    // Check if user already has a Stripe customer ID
    if (!user.email) {
      throw new Error('User email is required to create a Stripe customer');
    }

    // Create a new customer in Stripe
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name || user.email,
      metadata: {
        userId: user.id.toString()
      }
    });

    // Update user in database with Stripe customer ID
    await storage.updateStripeCustomerId(user.id, customer.id);

    return customer;
  } catch (error) {
    console.error('Error creating Stripe customer:', error);
    throw error;
  }
}

/**
 * Create a subscription for a user (not used in the simplified version)
 * 
 * NOTE: This function is just a placeholder and is NOT used in our implementation.
 * We're using one-time payments instead of subscriptions for simplicity.
 */
export async function createSubscription(userId: number, amount: number, period: 'monthly' | 'annual' = 'annual') {
  // This is a simplified implementation using one-time payments rather than actual subscriptions
  try {
    console.log(`Creating ${period} subscription for user ${userId} at $${amount}`);
    return await createPaymentIntent(amount, userId);
  } catch (error) {
    console.error(`Error creating ${period} subscription:`, error);
    throw error;
  }
}

/**
 * Get or create a payment intent for a pro plan purchase
 * In a production app, this would be a subscription, but for testing we're using a one-time payment
 */
export async function getOrCreateSubscription(userId: number, options?: { amount?: number, period?: 'monthly' | 'annual' }) {
  try {
    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // If user already has a pro subscription, return a success indicator 
    if (user.subscriptionPlan === 'pro') {
      return {
        alreadySubscribed: true,
        status: 'active'
      };
    }

    // Determine pricing based on options
    const period = options?.period || 'annual';
    const amount = options?.amount || (period === 'monthly' ? PRICING.monthly : PRICING.annual);
    
    console.log(`Creating payment intent for ${period} subscription at $${amount}`);
    
    // Create a new payment intent for the pro plan
    const paymentIntent = await createPaymentIntent(amount, userId);
    
    return {
      clientSecret: paymentIntent.clientSecret,
      status: 'pending',
      amount,
      period
    };
  } catch (error) {
    console.error('Error in getOrCreateSubscription:', error);
    throw error;
  }
}

/**
 * Create a payment intent for one-time payment
 */
export async function createPaymentIntent(amount: number, userId: number) {
  try {
    if (!stripe) {
      throw new Error('Stripe is not configured');
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // If user doesn't have a Stripe customer ID yet, create one
    let customerId = user.stripeCustomerId;
    if (!customerId) {
      if (!user.email) {
        throw new Error('User email is required to create a Stripe customer');
      }
      const customer = await createStripeCustomer(user);
      customerId = customer.id;
    }

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'usd',
      customer: customerId,
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return {
      clientSecret: paymentIntent.client_secret,
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
}

/**
 * Process webhook events from Stripe
 */
export async function handleWebhookEvent(event: any) {
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        // One-time payment success
        const paymentIntent = event.data.object;
        const customer = paymentIntent.customer;
        if (customer) {
          const user = await findUserByStripeCustomerId(customer);
          if (user) {
            // Determine if it's a monthly or annual subscription based on payment amount
            const paymentAmount = paymentIntent.amount;
            const isMonthly = paymentAmount === Math.round(12 * 100);
            const period = isMonthly ? 'monthly' : 'annual';
            
            // Update user to Pro plan
            await updateUserToProPlan(user.id, period);
            console.log(`User ${user.id} upgraded to Pro plan (${period}) via payment_intent.succeeded`);
            
            // Send welcome email
            await sendWelcomeEmail(user, period);
            console.log(`Welcome email sent to ${user.email} for ${period} subscription`);
          }
        }
        break;
      
      case 'checkout.session.completed':
        // Payment is successful and the subscription is created
        const session = event.data.object;
        // Find the user by looking up the Stripe customer ID
        const user = await findUserByStripeCustomerId(session.customer);
        if (user) {
          // For checkout.session.completed, try to determine period from amount
          const amount = session.amount_total;
          const isMonthly = amount === Math.round(12 * 100);
          const period = isMonthly ? 'monthly' : 'annual';
          
          await updateUserToProPlan(user.id, period);
          console.log(`User ${user.id} upgraded to Pro plan (${period}) via checkout.session.completed`);
          
          // Send welcome email
          await sendWelcomeEmail(user, period);
          console.log(`Welcome email sent to ${user.email} for ${period} subscription`);
        }
        break;
        
      case 'invoice.payment_succeeded':
        // Continue to provision the subscription as payments succeed
        const invoice = event.data.object;
        // Find the user by looking up the Stripe customer ID
        const invoiceUser = await findUserByStripeCustomerId(invoice.customer);
        if (invoiceUser) {
          // For invoice.payment_succeeded, determine period from amount
          const amount = invoice.amount_paid;
          const isMonthly = amount === Math.round(12 * 100);
          const period = isMonthly ? 'monthly' : 'annual';
          
          await updateUserToProPlan(invoiceUser.id, period);
          console.log(`User ${invoiceUser.id} upgraded to Pro plan (${period}) via invoice.payment_succeeded`);
          
          // Send welcome email
          await sendWelcomeEmail(invoiceUser, period);
          console.log(`Welcome email sent to ${invoiceUser.email} for ${period} subscription`);
        }
        break;
        
      case 'invoice.payment_failed':
        // The payment failed or the customer does not have a valid payment method
        break;
        
      case 'customer.subscription.deleted':
        // The subscription has been canceled
        const subscription = event.data.object;
        // Find the user by looking up the Stripe subscription ID
        const subscriptionUser = await findUserByStripeSubscriptionId(subscription.id);
        if (subscriptionUser) {
          await downgradeUserToFreePlan(subscriptionUser.id);
          console.log(`User ${subscriptionUser.id} downgraded to Free plan`);
        }
        break;
        
      default:
        console.log(`Unhandled event type ${event.type}`);
    }
    
    return { received: true };
  } catch (error) {
    console.error('Error handling webhook event:', error);
    throw error;
  }
}

/**
 * Helper function to find a user by Stripe customer ID
 */
async function findUserByStripeCustomerId(stripeCustomerId: string) {
  // This is a simplified example - in a real app, you'd query the database directly
  const users = await storage.getAllUsers();
  return users.find(user => user.stripeCustomerId === stripeCustomerId);
}

/**
 * Helper function to find a user by Stripe subscription ID
 */
async function findUserByStripeSubscriptionId(stripeSubscriptionId: string) {
  // This is a simplified example - in a real app, you'd query the database directly
  const users = await storage.getAllUsers();
  return users.find(user => user.stripeSubscriptionId === stripeSubscriptionId);
}

/**
 * Update user to Pro plan after successful payment
 * @param userId The user ID to update
 * @param period Optional subscription period ('monthly' or 'annual')
 */
async function updateUserToProPlan(userId: number, period: 'monthly' | 'annual' = 'annual') {
  // Calculate expiry date based on subscription period
  const subscriptionExpiry = new Date();
  if (period === 'monthly') {
    subscriptionExpiry.setMonth(subscriptionExpiry.getMonth() + 1);
    console.log(`Setting monthly subscription expiry for user ${userId} to ${subscriptionExpiry.toISOString()}`);
  } else {
    subscriptionExpiry.setFullYear(subscriptionExpiry.getFullYear() + 1);
    console.log(`Setting annual subscription expiry for user ${userId} to ${subscriptionExpiry.toISOString()}`);
  }
  
  return await storage.updateUser(userId, {
    subscriptionPlan: 'pro',
    maxQuantity: 999999,
    subscriptionExpiry: subscriptionExpiry.toISOString()
  });
}

/**
 * Downgrade user to Free plan after subscription cancellation
 */
async function downgradeUserToFreePlan(userId: number) {
  return await storage.updateUser(userId, {
    subscriptionPlan: 'free',
    maxQuantity: 50,
    subscriptionExpiry: null
  });
}