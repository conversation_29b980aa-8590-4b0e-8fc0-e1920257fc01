import Drawing from 'dxf-writer';
import { GlassSheetPlan, GlassStockSettings, GlassTypeResult } from './types';

interface DxfExportOptions {
  drawCutPieces?: boolean; // Draw rectangle for each cut piece
  drawLabels?: boolean; // Include description labels
  drawDimensions?: boolean; // Include dimensions
  drawCuttingPath?: boolean; // Draw cutting paths with kerf width
  scale?: number; // Scale factor (default: 1mm = 1 unit)
}

/**
 * Color constants that match the AutoCAD Color Index (ACI)
 */
const ACI_COLORS = {
  RED: 1,
  YELLOW: 2,
  GREEN: 3,
  CYAN: 4,
  BLUE: 5,
  MAGENTA: 6,
  WHITE: 7
};

/**
 * Generates a DXF file for a glass cutting plan that can be imported into CNC machines
 * 
 * @param sheetPlan The glass sheet plan to convert to DXF
 * @param settings The glass stock settings
 * @param options Export options for DXF generation
 * @returns The DXF file content as a string
 */
export function generateGlassCuttingDxf(
  sheetPlan: GlassSheetPlan,
  settings: GlassStockSettings,
  options: DxfExportOptions = {}
): string {
  // Set default options
  const {
    drawCutPieces = true,
    drawLabels = true,
    drawDimensions = true,
    drawCuttingPath = true,
    scale = 1
  } = options;

  // Create a new drawing
  const dxf = new Drawing();
  
  // Create layers with different colors and linetypes
  dxf.addLayer('Sheet', ACI_COLORS.BLUE, 'CONTINUOUS'); 
  dxf.addLayer('CutPieces', ACI_COLORS.GREEN, 'CONTINUOUS'); 
  dxf.addLayer('Labels', ACI_COLORS.RED, 'CONTINUOUS'); 
  dxf.addLayer('Dimensions', ACI_COLORS.MAGENTA, 'CONTINUOUS'); 
  dxf.addLayer('CuttingPath', ACI_COLORS.YELLOW, 'CONTINUOUS');
  
  // Define sheet dimensions
  const sheetWidth = settings.sheetWidth;
  const sheetHeight = settings.sheetHeight;
  const kerf = settings.kerf;
  
  // Set active layer and draw the sheet outline
  dxf.setActiveLayer('Sheet');
  dxf.drawRect(0, 0, sheetWidth, sheetHeight);
  
  // Draw cut pieces
  if (drawCutPieces) {
    dxf.setActiveLayer('CutPieces');
    
    sheetPlan.cuts.forEach(cut => {
      const x = cut.x;
      const y = cut.y;
      const width = cut.width;
      const height = cut.height;
      
      // Draw rectangle for the cut piece
      dxf.drawRect(x, y, x + width, y + height);
      
      // Add label
      if (drawLabels && cut.description) {
        dxf.setActiveLayer('Labels');
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        dxf.drawText(centerX, centerY, 10, 0, cut.description, 'center');
        dxf.setActiveLayer('CutPieces');
      }
    });
  }
  
  // Add dimensions
  if (drawDimensions) {
    dxf.setActiveLayer('Dimensions');
    
    sheetPlan.cuts.forEach(cut => {
      const x = cut.x;
      const y = cut.y;
      const width = cut.width;
      const height = cut.height;
      
      // Width dimension
      dxf.drawText(x + width / 2, y - 15, 8, 0, `${width}mm`, 'center');
      
      // Height dimension
      dxf.drawText(x - 15, y + height / 2, 8, 90, `${height}mm`, 'center');
    });
  }
  
  // Draw cutting paths with kerf width consideration
  if (drawCuttingPath && kerf > 0) {
    dxf.setActiveLayer('CuttingPath');
    
    sheetPlan.cuts.forEach(cut => {
      const x = cut.x;
      const y = cut.y;
      const width = cut.width;
      const height = cut.height;
      
      // Draw cutting path - half kerf offset from the piece edge
      const kerfOffset = kerf / 2;
      
      // Draw the cutting path rectangle (slightly larger than the piece to account for kerf)
      dxf.drawRect(
        x - kerfOffset, 
        y - kerfOffset, 
        x + width + kerfOffset, 
        y + height + kerfOffset
      );
    });
  }
  
  // Return the DXF content
  return dxf.toDxfString();
}

/**
 * Generates DXF files for all sheets in a glass type result
 * 
 * @param glassTypeResult The glass type result containing multiple sheet plans
 * @param settings The glass stock settings
 * @param options Export options for DXF generation
 * @returns An array of DXF file contents as strings, one for each sheet
 */
export function generateAllGlassCuttingDxfs(
  glassTypeResult: GlassTypeResult,
  settings: GlassStockSettings,
  options: DxfExportOptions = {}
): string[] {
  return glassTypeResult.sheetPlans.map(sheetPlan => 
    generateGlassCuttingDxf(sheetPlan, settings, options)
  );
}

/**
 * Creates and triggers a download of a DXF file
 * 
 * @param dxfContent The DXF file content
 * @param filename The name of the file to download
 */
export function downloadDxfFile(dxfContent: string, filename: string): void {
  const blob = new Blob([dxfContent], { type: 'application/dxf' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  document.body.appendChild(link);
  link.click();
  
  // Clean up
  setTimeout(() => {
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, 100);
}

/**
 * Triggers downloads for all generated DXF files
 * 
 * @param dxfContents Array of DXF file contents
 * @param baseFilename Base filename (will be appended with sheet number)
 */
export function downloadAllDxfFiles(dxfContents: string[], baseFilename: string): void {
  dxfContents.forEach((content, index) => {
    const filename = `${baseFilename}_sheet_${index + 1}.dxf`;
    downloadDxfFile(content, filename);
  });
}