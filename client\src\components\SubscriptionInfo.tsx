import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Crown, LockIcon, CreditCard } from "lucide-react";
import ProUpgradeRequestForm from "@/components/ProUpgradeRequestForm";
import { Link } from "wouter";

interface SubscriptionInfo {
  subscriptionPlan: string;
  maxQuantity: number;
  totalQuantity: number;
  remainingQuantity: number;
  isProUser: boolean;
  subscriptionExpiry: string | null;
}

export default function SubscriptionInfo() {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const { data: subscription, isLoading } = useQuery<SubscriptionInfo>({
    queryKey: ["/api/subscription"],
    enabled: !!user,
  });
  
  if (!user || isLoading) {
    return (
      <Card className="subscription-card animate-pulse">
        <CardHeader>
          <CardTitle className="text-lg">Subscription</CardTitle>
          <CardDescription>Loading subscription information...</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  if (!subscription) {
    return (
      <Card className="subscription-card">
        <CardHeader>
          <CardTitle className="text-lg">Subscription</CardTitle>
          <CardDescription>Unable to load subscription information</CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => window.location.reload()}
            variant="outline" 
            size="sm"
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  const { 
    subscriptionPlan, 
    maxQuantity, 
    totalQuantity, 
    remainingQuantity,
    isProUser
  } = subscription;
  
  const usagePercentage = Math.min(100, Math.round((totalQuantity / maxQuantity) * 100)) || 0;
  
  const handleUpgradeClick = () => {
    toast({
      title: "Upgrade to Pro",
      description: "Please contact an administrator to upgrade your account to Pro.",
    });
  };
  
  return (
    <Card className="subscription-card">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Your Plan</CardTitle>
          <Badge variant={isProUser ? "default" : "outline"} className={isProUser ? "bg-amber-500" : undefined}>
            {isProUser ? (
              <div className="flex items-center">
                <Crown className="w-3 h-3 mr-1" />
                <span>PRO</span>
              </div>
            ) : (
              <span>FREE</span>
            )}
          </Badge>
        </div>
        <CardDescription>
          {isProUser 
            ? "Pro plan with unlimited quantities" 
            : "Free plan with limited quantities"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!isProUser && (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Usage</span>
                <span className="font-medium">
                  {totalQuantity} / {maxQuantity} pieces
                </span>
              </div>
              <Progress value={usagePercentage} className="h-2" />
            </div>
            
            {usagePercentage >= 80 && (
              <div className="text-sm text-amber-500 flex items-center">
                <LockIcon className="w-3 h-3 mr-1" />
                <span>You're approaching your plan limit</span>
              </div>
            )}
            
            <div className="w-full space-y-4">
              <div className="mt-2 space-y-2 text-sm">
                <h4 className="font-medium text-amber-700">Pro Features Include:</h4>
                <ul className="pl-5 space-y-1 list-disc text-muted-foreground">
                  <li>Window Cutting Calculator</li>
                  <li>Window Projects Management</li>
                  <li>Profile Manager with Images</li>
                  <li>Accessories Manager</li>
                  <li>Unlimited Cut Pieces</li>
                </ul>
              </div>
              
              <div className="flex flex-col gap-2 mt-4">
                <Link href="/subscription">
                  <Button className="w-full" variant="default">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Upgrade Now with Payment
                  </Button>
                </Link>
                <div className="text-xs text-center text-muted-foreground">
                  Or request an upgrade via email below
                </div>
              </div>
              <ProUpgradeRequestForm />
            </div>
          </div>
        )}
        
        {isProUser && (
          <div className="py-2">
            <div className="flex items-center text-sm text-green-600">
              <Crown className="w-4 h-4 mr-1" />
              <span>Unlimited cut pieces with Pro</span>
            </div>
            {subscription.subscriptionExpiry && (
              <div className="text-sm text-muted-foreground mt-2">
                Expires: {new Date(subscription.subscriptionExpiry).toLocaleDateString()}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}