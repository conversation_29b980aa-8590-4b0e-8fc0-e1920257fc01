import { useState } from "react";
import StockMaterialForm from "@/components/StockMaterialForm";
import CutListManager from "@/components/CutListManager";
import OptimizationResults from "@/components/OptimizationResults";
import UserMenu from "@/components/UserMenu";
import SubscriptionInfo from "@/components/SubscriptionInfo";
import { Button } from "@/components/ui/button";
import { CutPiece, StockSettings, OptimizationResult } from "@/lib/types";
import { optimizeCutting } from "@/lib/optimizationAlgorithm";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";

export default function Home() {
  const { user } = useAuth();
  const [stockSettings, setStockSettings] = useState<StockSettings>({
    stockLength: 6000,
    kerf: 3.5,
    endTrim: 5
  });
  
  const [cutList, setCutList] = useState<CutPiece[]>([
    { id: 1, length: 1200, quantity: 5, description: "Upper frame", profileType: "Frame" },
    { id: 2, length: 800, quantity: 8, description: "Lower frame supports", profileType: "Frame" },
    { id: 3, length: 1500, quantity: 4, description: "Cross beams", profileType: "Support" }
  ]);
  
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);

  const { toast } = useToast();
  
  const handleOptimize = () => {
    setIsOptimizing(true);
    
    // Check total quantity for free plan users
    if (user && user.subscriptionPlan === 'free') {
      const totalQuantity = cutList.reduce((sum, piece) => sum + piece.quantity, 0);
      if (totalQuantity > user.maxQuantity) {
        toast({
          title: "Free Plan Limit Exceeded",
          description: `Your free plan allows up to ${user.maxQuantity} pieces. You're trying to optimize ${totalQuantity} pieces. Please upgrade to Pro plan or reduce your quantity.`,
          variant: "destructive",
        });
        setIsOptimizing(false);
        return;
      }
    }
    
    // Small delay to allow UI to update
    setTimeout(() => {
      try {
        const result = optimizeCutting(cutList, stockSettings);
        setOptimizationResult(result);
      } catch (error) {
        console.error("Optimization failed:", error);
      } finally {
        setIsOptimizing(false);
      }
    }, 100);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      <header className="relative pb-6">
        <UserMenu />
        
        <div className="text-center">
          <h1 className="text-3xl font-bold text-primary">Aluminum Cutting Optimizer</h1>
          <p className="text-neutral-600 mt-2">
            Optimize your aluminum cutting layouts to minimize waste and costs
          </p>
        </div>
        
        <div className="flex flex-wrap justify-center mt-4 gap-3">
          <Link href="/glass">
            <Button variant="outline">Glass Cutting Optimizer</Button>
          </Link>
          
          {/* Pro features with indicator */}
          <Link href="/window-calculator">
            <Button variant="outline" className="flex items-center gap-1">
              Window Design
              {user?.subscriptionPlan !== 'pro' && (
                <span className="inline-flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs font-medium text-amber-700 ring-1 ring-inset ring-amber-600/20">
                  Pro
                </span>
              )}
            </Button>
          </Link>
          <Link href="/window-projects">
            <Button variant="outline" className="flex items-center gap-1">
              Window Projects
              {user?.subscriptionPlan !== 'pro' && (
                <span className="inline-flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs font-medium text-amber-700 ring-1 ring-inset ring-amber-600/20">
                  Pro
                </span>
              )}
            </Button>
          </Link>
          <Link href="/profiles">
            <Button variant="outline" className="flex items-center gap-1">
              Profile Manager
              {user?.subscriptionPlan !== 'pro' && (
                <span className="inline-flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs font-medium text-amber-700 ring-1 ring-inset ring-amber-600/20">
                  Pro
                </span>
              )}
            </Button>
          </Link>
          <Link href="/accessories">
            <Button variant="outline" className="flex items-center gap-1">
              Accessories Manager
              {user?.subscriptionPlan !== 'pro' && (
                <span className="inline-flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs font-medium text-amber-700 ring-1 ring-inset ring-amber-600/20">
                  Pro
                </span>
              )}
            </Button>
          </Link>
          
          {/* AI Feature - Platinum only */}
          <Link href="/ai-window-analysis">
            <Button variant="outline" className="flex items-center gap-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
              AI Window Analysis
              <span className="inline-flex items-center rounded-md bg-purple-900/50 px-2 py-1 text-xs font-medium text-white ring-1 ring-inset ring-white/20">
                Platinum
              </span>
            </Button>
          </Link>
        </div>
      </header>
      
      <main className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <StockMaterialForm 
              stockSettings={stockSettings} 
              onUpdateSettings={setStockSettings} 
            />
          </div>
          <div className="lg:col-span-1">
            <SubscriptionInfo />
          </div>
        </div>
        
        <CutListManager 
          cutList={cutList} 
          onUpdateCutList={setCutList} 
          onOptimize={handleOptimize}
          isOptimizing={isOptimizing}
        />
        
        {optimizationResult && (
          <OptimizationResults 
            result={optimizationResult} 
            stockSettings={stockSettings}
            onReoptimize={handleOptimize}
          />
        )}
      </main>
      
      <footer className="mt-8 text-center text-sm text-neutral-600">
        <p>© {new Date().getFullYear()} Aluminum Cutting Optimizer. All rights reserved.</p>
      </footer>
    </div>
  );
}
