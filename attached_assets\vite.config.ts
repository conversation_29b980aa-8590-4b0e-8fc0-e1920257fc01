import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import type { Connect } from 'vite'

// Mock user database
const users: { [key: string]: any } = {}

// Mock API handlers
const mockApiHandlers = {
  register: async (body: any) => {
    const { name, email, password } = body

    if (users[email]) {
      throw new Error('Email already registered')
    }

    const user = {
      id: Date.now().toString(),
      name,
      email,
      password,
    }

    users[email] = user

    return {
      success: true,
      message: 'Registration successful',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      }
    }
  },

  login: async (body: any) => {
    const { email, password } = body
    const user = users[email]

    if (!user || user.password !== password) {
      throw new Error('Invalid email or password')
    }

    const token = btoa(JSON.stringify({ id: user.id, email: user.email }))

    return {
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      }
    }
  }
}

// Custom middleware type
type CustomMiddleware = Connect.NextHandleFunction

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    {
      name: 'mock-auth',
      configureServer(server) {
        server.middlewares.use(async (req, res, next) => {
          if (!req.url?.startsWith('/api/auth/')) {
            return next()
          }

          // Extract the endpoint from the URL
          const endpoint = req.url.split('/api/auth/')[1]
          const handler = mockApiHandlers[endpoint]

          if (!handler) {
            return next()
          }

          try {
            // Parse request body
            let body = ''
            await new Promise<void>((resolve) => {
              req.on('data', chunk => { body += chunk })
              req.on('end', () => resolve())
            })

            const result = await handler(JSON.parse(body || '{}'))

            res.setHeader('Content-Type', 'application/json')
            res.statusCode = 200
            res.end(JSON.stringify(result))
          } catch (error: any) {
            res.statusCode = 400
            res.setHeader('Content-Type', 'application/json')
            res.end(JSON.stringify({ 
              success: false,
              message: error.message || 'An error occurred'
            }))
          }
        })
      }
    }
  ],
})
