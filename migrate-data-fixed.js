import dotenv from 'dotenv';
import { Pool as NeonPool, neonConfig } from '@neondatabase/serverless';
import Database from 'better-sqlite3';
import ws from 'ws';

dotenv.config();

// Configure Neon
neonConfig.webSocketConstructor = ws;

// Neon database connection
const neonPool = new NeonPool({
  connectionString: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
});

// SQLite database connection
const sqlite = new Database('./local_database.db');

// Column mappings for tables with different schemas
const columnMappings = {
  users: {
    include: ['id', 'username', 'password', 'name', 'email', 'role', 'stripe_customer_id', 'subscription_plan', 'subscription_status', 'subscription_end_date', 'payment_issue_date', 'created_at', 'updated_at'],
    exclude: ['subscription_expiry'] // This column doesn't exist in SQLite
  },
  profiles: {
    include: ['id', 'name', 'description', 'stock_length', 'image_url', 'user_id', 'created_at', 'updated_at'],
    exclude: ['category']
  },
  window_designs: {
    include: ['id', 'name', 'description', 'user_id', 'created_at', 'updated_at'],
    exclude: ['category']
  },
  window_projects: {
    include: ['id', 'name', 'description', 'user_id', 'created_at', 'updated_at'],
    exclude: ['client_name']
  },
  project_window_items: {
    include: ['id', 'project_id', 'window_design_id', 'window_type', 'width', 'height', 'quantity', 'created_at', 'updated_at'],
    exclude: ['window_cutting_list_id']
  },
  accessories: {
    include: ['id', 'name', 'type', 'description', 'unit_price', 'user_id', 'created_at', 'updated_at'],
    exclude: ['subtype']
  },
  invoices: {
    include: ['id', 'user_id', 'amount', 'status', 'stripe_payment_intent_id', 'created_at', 'updated_at'],
    exclude: ['invoice_number']
  }
};

// Tables that exist in Neon but might have different names
const tableAliases = {
  'window_design_components': 'components',
  'window_design_glass_specifications': 'glass_specifications'
};

async function exportTableData(tableName) {
  const client = await neonPool.connect();
  try {
    // Try the original table name first, then try aliases
    let actualTableName = tableName;
    if (tableAliases[tableName]) {
      actualTableName = tableAliases[tableName];
    }
    
    console.log(`Exporting data from table: ${actualTableName}`);
    const result = await client.query(`SELECT * FROM ${actualTableName}`);
    console.log(`Found ${result.rows.length} rows in ${actualTableName}`);
    return result.rows;
  } catch (error) {
    console.error(`Error exporting ${tableName}:`, error.message);
    return [];
  } finally {
    client.release();
  }
}

function filterColumns(data, tableName) {
  if (!data || data.length === 0) return data;
  
  const mapping = columnMappings[tableName];
  if (!mapping) return data;
  
  return data.map(row => {
    const filteredRow = {};
    for (const [key, value] of Object.entries(row)) {
      // Include column if it's in the include list or if no include list is specified
      if (!mapping.include || mapping.include.includes(key)) {
        // Exclude column if it's in the exclude list
        if (!mapping.exclude || !mapping.exclude.includes(key)) {
          filteredRow[key] = value;
        }
      }
    }
    return filteredRow;
  });
}

function importTableData(tableName, data) {
  if (data.length === 0) {
    console.log(`No data to import for ${tableName}`);
    return;
  }

  try {
    console.log(`Importing ${data.length} rows to ${tableName}`);
    
    // Filter data based on column mappings
    const filteredData = filterColumns(data, tableName);
    
    if (filteredData.length === 0) {
      console.log(`No valid data to import for ${tableName} after filtering`);
      return;
    }
    
    // Get column names from first row
    const columns = Object.keys(filteredData[0]);
    const columnList = columns.join(', ');
    const placeholders = columns.map(() => '?').join(', ');
    
    // Prepare insert statement
    const insertStmt = sqlite.prepare(
      `INSERT OR IGNORE INTO ${tableName} (${columnList}) VALUES (${placeholders})`
    );
    
    // Insert data in a transaction for better performance
    const insertMany = sqlite.transaction((rows) => {
      for (const row of rows) {
        const values = columns.map(col => row[col]);
        insertStmt.run(values);
      }
    });
    
    insertMany(filteredData);
    console.log(`Successfully imported ${filteredData.length} rows to ${tableName}`);
    
  } catch (error) {
    console.error(`Error importing to ${tableName}:`, error.message);
  }
}

function checkTableExists(tableName) {
  try {
    const result = sqlite.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `).get(tableName);
    return !!result;
  } catch (error) {
    return false;
  }
}

async function migrateData() {
  console.log('Starting data migration from Neon to SQLite...');
  
  try {
    // Test Neon connection
    console.log('Testing Neon connection...');
    const neonClient = await neonPool.connect();
    await neonClient.query('SELECT 1');
    neonClient.release();
    console.log('✅ Neon connection successful');
    
    // Test SQLite connection
    console.log('Testing SQLite connection...');
    sqlite.prepare('SELECT 1').get();
    console.log('✅ SQLite connection successful');
    
    // List of tables to migrate
    const tables = ['users', 'profiles', 'window_designs', 'window_projects', 'project_window_items', 'accessories', 'invoices'];
    
    // Migrate each table
    for (const tableName of tables) {
      console.log(`\n--- Processing table: ${tableName} ---`);
      
      // Check if table exists in SQLite database
      const exists = checkTableExists(tableName);
      if (!exists) {
        console.log(`⚠️  Table ${tableName} does not exist in SQLite database. Skipping...`);
        continue;
      }
      
      // Export data from Neon
      const data = await exportTableData(tableName);
      
      // Import data to SQLite
      importTableData(tableName, data);
    }
    
    // Try to migrate components and glass specifications with their actual table names
    console.log(`\n--- Processing components table ---`);
    const componentsData = await exportTableData('window_design_components');
    if (componentsData.length > 0) {
      importTableData('window_design_components', componentsData);
    }
    
    console.log(`\n--- Processing glass specifications table ---`);
    const glassData = await exportTableData('window_design_glass_specifications');
    if (glassData.length > 0) {
      importTableData('window_design_glass_specifications', glassData);
    }
    
    console.log('\n🎉 Data migration completed successfully!');
    console.log('📊 Your data is now available in the local SQLite database');
    console.log('🚀 You can now run: npm run dev');
    
    // Show summary
    const allTables = sqlite.prepare('SELECT name FROM sqlite_master WHERE type=?').all('table');
    console.log('\n📋 Available tables:', allTables.map(t => t.name).filter(name => name !== 'sqlite_sequence'));
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await neonPool.end();
    sqlite.close();
  }
}

// Run migration
migrateData();
