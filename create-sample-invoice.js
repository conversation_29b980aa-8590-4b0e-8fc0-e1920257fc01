import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();
const { Pool } = pg;

// Load database connection string from environment variables
const connectionString = process.env.DATABASE_URL;

// Create a connection pool
const pool = new Pool({
  connectionString
});

async function createSampleInvoice() {
  const client = await pool.connect();
  try {
    console.log('Connected to database');
    
    // Use the logged-in user ID
    const userId = 1;
    console.log(`Found user with ID: ${userId}`);
    
    // Create a sample invoice
    const invoiceNumber = `INV-${Date.now()}-${userId}`;
    const now = new Date();
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 15); // Due in 15 days
    
    const invoiceData = {
      user_id: userId,
      invoice_number: invoiceNumber,
      amount: 1890.00,
      currency: 'USD',
      status: 'pending',
      description: 'Window Craft Pro Platinum Upgrade',
      subscription_plan: 'platinum',
      subscription_period: 'annual',
      issued_date: now,
      due_date: dueDate,
      paid_date: null,
      payment_method: 'credit_card',
      items: JSON.stringify([
        {
          description: 'Platinum Plan Annual Subscription',
          amount: 1890.00,
          type: 'platinum',
          period: 'annual'
        }
      ])
    };
    
    // Insert the invoice
    const insertQuery = `
      INSERT INTO invoices (
        user_id, invoice_number, amount, currency, status, description,
        subscription_plan, subscription_period, issued_date, due_date, paid_date,
        payment_method, items
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
      ) RETURNING id
    `;
    
    const insertValues = [
      invoiceData.user_id,
      invoiceData.invoice_number,
      invoiceData.amount,
      invoiceData.currency,
      invoiceData.status,
      invoiceData.description,
      invoiceData.subscription_plan,
      invoiceData.subscription_period,
      invoiceData.issued_date,
      invoiceData.due_date,
      invoiceData.paid_date,
      invoiceData.payment_method,
      invoiceData.items
    ];
    
    const result = await client.query(insertQuery, insertValues);
    const invoiceId = result.rows[0].id;
    
    console.log(`Created sample invoice with ID: ${invoiceId}`);
    console.log('Sample invoice details:');
    console.log(JSON.stringify(invoiceData, null, 2));
    
  } catch (error) {
    console.error('Error creating sample invoice:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createSampleInvoice();