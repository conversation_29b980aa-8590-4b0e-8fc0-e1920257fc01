import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import i18n from "./lib/i18n"; // Import i18n configuration
import { I18nextProvider } from "react-i18next";

// Initialize document direction and language based on stored preference
try {
  const storedLanguage = localStorage.getItem('language') || 'en';
  document.documentElement.lang = storedLanguage;
  document.documentElement.dir = storedLanguage === 'ar' ? 'rtl' : 'ltr';
  
  // Make sure language is set in i18n
  i18n.changeLanguage(storedLanguage);
  
  console.log(`Initial language setup: ${storedLanguage}, direction: ${document.documentElement.dir}`);
} catch (error) {
  console.error("Error initializing language settings:", error);
}

createRoot(document.getElementById("root")!).render(
  <I18nextProvider i18n={i18n}>
    <App />
  </I18nextProvider>
);
