import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import AuthPage from "@/pages/auth-page";
import AdminPage from "@/pages/admin-page";
import SubscriptionPage from "@/pages/subscription-page";
import PaymentSuccessPage from "@/pages/payment-success";
import ProfilePage from "@/pages/profile-page";
import ContactPage from "@/pages/contact-page";
import GlassOptimizationPage from "@/pages/glass-optimization";
import WindowCalculatorPage from "@/pages/window-calculator";
import ProfileManagerPage from "@/pages/profile-manager";
import WindowProjectsPage from "@/pages/window-projects";
import AccessoriesManagerPage from "@/pages/accessories-manager";
import ChangePasswordPage from "@/pages/change-password-page";
import AIWindowAnalysisPage from "@/pages/ai-window-analysis";
import BillingPage from "@/pages/billing-page";
import { AuthProvider } from "@/hooks/use-auth";
import { ProtectedRoute } from "@/lib/protected-route";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";

function Router() {
  // Use translation hook to ensure the i18n instance is properly initialized
  const { i18n } = useTranslation();
  
  // This effect runs when the component mounts to verify language settings
  useEffect(() => {
    const storedLanguage = localStorage.getItem('language') || 'en';
    if (i18n.language !== storedLanguage) {
      console.log(`Language mismatch: i18n has ${i18n.language} but localStorage has ${storedLanguage}, fixing...`);
      i18n.changeLanguage(storedLanguage);
    }
    document.documentElement.dir = storedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);
  
  return (
    <Switch>
      <ProtectedRoute path="/" component={Home} />
      <ProtectedRoute path="/glass" component={GlassOptimizationPage} />
      {/* Premium features requiring Pro subscription */}
      <ProtectedRoute path="/window-calculator" component={WindowCalculatorPage} requirePro={true} />
      <ProtectedRoute path="/window-projects" component={WindowProjectsPage} requirePro={true} />
      <ProtectedRoute path="/profiles" component={ProfileManagerPage} requirePro={true} />
      <ProtectedRoute path="/accessories" component={AccessoriesManagerPage} requirePro={true} />
      {/* Platinum feature - AI-powered analysis */}
      <ProtectedRoute path="/ai-window-analysis" component={AIWindowAnalysisPage} requirePlatinum={true} />
      {/* Admin and account features */}
      <ProtectedRoute path="/admin" component={AdminPage} />
      <ProtectedRoute path="/subscription" component={SubscriptionPage} />
      <ProtectedRoute path="/payment-success" component={PaymentSuccessPage} />
      <ProtectedRoute path="/profile" component={ProfilePage} />
      <ProtectedRoute path="/billing" component={BillingPage} />
      <Route path="/change-password" component={ChangePasswordPage} />
      <Route path="/contact" component={ContactPage} />
      <Route path="/auth" component={AuthPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router />
        <Toaster />
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
