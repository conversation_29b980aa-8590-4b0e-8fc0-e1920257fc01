import OpenAI from 'openai';

// Initialize the OpenAI client
const openai = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'placeholder'
  ? new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  : null;

if (!openai) {
  console.warn('OPENAI_API_KEY not configured. AI functionality will be disabled.');
}

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. Do not change this unless explicitly requested by the user.
const MODEL = 'gpt-4o';

interface AnalyzeWindowParams {
  windowImage: string;
  width: number;
  height: number;
}

/**
 * Analyzes a window image to extract components and glass specifications
 */
export async function analyzeWindowImage(params: AnalyzeWindowParams) {
  try {
    if (!openai) {
      throw new Error('OpenAI is not configured');
    }

    const { windowImage, width, height } = params;
    
    // Remove data URL prefix to get base64 data
    const base64Image = windowImage.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
    
    // Analyze the image with GPT-4 Vision
    const prompt = `
WINDOW MANUFACTURING ANALYSIS:
This window is ${width}mm wide × ${height}mm high.

Analyze this image to identify ALL window components including:
- Frame pieces
- Sash components
- Mullions
- Transoms
- Glass panels

Format results as JSON with these fields for components:
1. name - descriptive name like "Top Frame", "Left Frame"
2. componentType - one of: "frame", "sash", "mullion", "transom"
3. widthSize - width in mm (for horizontal components)
4. widthQty - usually 1
5. heightSize - height in mm (for vertical components)
6. heightQty - usually 1
7. cutAngle - 45° for frames, 90° for mullions/transoms
8. quantity - usually 1

For glass panels, include:
1. name - e.g. "Main Glass Panel" 
2. glassType - e.g. "Clear Glass"
3. width - width in mm
4. height - height in mm
5. thickness - in mm (typically 6mm)
6. quantity - usually 1

IMPORTANT DIMENSIONS:
- For TOP and BOTTOM frames: width is full window width (${width}mm)
- For LEFT and RIGHT frames: height is full window height (${height}mm)
- For MULLIONS: height is window height minus frame thickness
- For GLASS: dimensions are opening size minus ~20mm for gaskets

FOLLOW THIS EXACT JSON FORMAT:
{
  "components": [
    {
      "name": "Top Frame", 
      "componentType": "frame",
      "widthSize": ${width},
      "widthQty": 1,
      "heightSize": null,
      "heightQty": 0,
      "cutAngle": 45,
      "quantity": 1
    },
    {
      "name": "Left Frame", 
      "componentType": "frame",
      "widthSize": null,
      "widthQty": 0,
      "heightSize": ${height},
      "heightQty": 1,
      "cutAngle": 45,
      "quantity": 1
    }
  ],
  "glass": [
    {
      "name": "Main Glass Panel",
      "glassType": "Clear Glass",
      "width": ${width - 60},
      "height": ${height - 60},
      "thickness": 6,
      "quantity": 1
    }
  ],
  "analysisNotes": "Brief observations"
}
    `;

    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt,
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${base64Image}`,
                detail: "high"
              },
            },
          ],
        },
      ],
      response_format: { type: 'json_object' },
      max_tokens: 4000,
      temperature: 0.7,  // Increase temperature for more creative/detailed analysis
    });

    // Parse and return the analysis result
    console.log("AI RESPONSE:", response.choices[0].message.content);
    const result = JSON.parse(response.choices[0].message.content || '{}');
    console.log("PROCESSED RESULT:", JSON.stringify(result, null, 2));
    return result;
  } catch (error) {
    console.error('Error analyzing window image:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to analyze window image: ${errorMessage}`);
  }
}

/**
 * Converts AI analysis results to actual window design components
 * for saving in the database
 */
export function convertAnalysisToWindowDesign(
  analysisResult: any,
  windowImage: string,
  width: number,
  height: number
) {
  // Process components with proper formulas based on AI analysis
  const processedComponents = analysisResult.components.map((comp: any) => {
    // Get component data directly from the structured AI response
    // Map the new field names to our internal structure
    let widthFormula = '';
    let heightFormula = '';
    let profileName = '';
    
    // Use specific width and height values when available
    if (comp.widthSize) {
      widthFormula = String(comp.widthSize);
    } else if (comp.name?.toLowerCase().includes('top') || comp.name?.toLowerCase().includes('bottom')) {
      widthFormula = 'width';
    }
    
    if (comp.heightSize) {
      heightFormula = String(comp.heightSize);
    } else if (comp.name?.toLowerCase().includes('left') || comp.name?.toLowerCase().includes('right') || 
              comp.name?.toLowerCase().includes('mullion')) {
      heightFormula = 'height';
    }
    
    // Set profile name based on component type
    if (comp.componentType?.toLowerCase().includes('frame')) {
      profileName = 'Frame Profile';
    } else if (comp.componentType?.toLowerCase().includes('sash')) {
      profileName = 'Sash Profile';
    } else if (comp.componentType?.toLowerCase().includes('mullion')) {
      profileName = 'Mullion Profile';
    } else if (comp.componentType?.toLowerCase().includes('transom')) {
      profileName = 'Transom Profile';
    } else {
      profileName = 'Standard Profile';
    }
    
    return {
      name: comp.name || "Unknown Component",
      componentType: comp.componentType || "frame",
      // Use generic profile names based on component type
      profileId: 1, // Will be replaced with actual lookup later
      profileName: profileName,
      // Use the width/height sizes and quantities directly
      widthFormula: widthFormula,
      heightFormula: heightFormula,
      widthQuantity: String(comp.widthQty || '0'),
      heightQuantity: String(comp.heightQty || '0'),
      quantityFormula: String(comp.quantity || '1'),
      leftCutDegree: comp.cutAngle || 45,
      rightCutDegree: comp.cutAngle || 45,
      notes: comp.notes || '',
      // Track original dimensions for display purposes
      originalWidthSize: comp.widthSize,
      originalHeightSize: comp.heightSize
    };
  });
  
  // Process glass panels with proper dimensions
  const processedGlass = analysisResult.glass.map((glass: any) => {
    // Use the glass data directly from the AI response
    let glassCode = 'CL-6MM';
    
    // Set glass code based on glass type and thickness
    if (glass.glassType?.toLowerCase().includes('tint')) {
      glassCode = `TINT-${glass.thickness || 6}MM`;
    } else if (glass.glassType?.toLowerCase().includes('temper')) {
      glassCode = `TEMP-${glass.thickness || 6}MM`;
    } else if (glass.glassType?.toLowerCase().includes('insul')) { 
      glassCode = `DG-${glass.thickness || 24}MM`;
    } else {
      glassCode = `CL-${glass.thickness || 6}MM`;
    }
    
    return {
      name: glass.name || 'Glass Panel',
      glassType: glass.glassType || 'Clear Glass', 
      glassCode: glassCode,
      thickness: glass.thickness || 6,
      widthFormula: glass.width ? String(glass.width) : 'width - 60',
      heightFormula: glass.height ? String(glass.height) : 'height - 60',
      quantityFormula: String(glass.quantity || '1'),
      notes: glass.notes || '',
      // Track original dimensions for display purposes
      originalWidth: glass.width,
      originalHeight: glass.height
    };
  });
  
  return {
    designName: `AI Generated Window (${width}×${height})`,
    components: processedComponents,
    glass: processedGlass,
    imageUrl: windowImage,
    // Add analysis notes if available
    analysisNotes: analysisResult.analysisNotes || '',
    // Add metadata to track this was AI generated
    isAiGenerated: true,
    aiGeneratedDate: new Date().toISOString(),
    originalDimensions: {
      width: width,
      height: height
    }
  };
}