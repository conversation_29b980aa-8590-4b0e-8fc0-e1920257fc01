import { MailService } from '@sendgrid/mail';

// Initialize SendGrid if API key is available
let mailService: MailService | null = null;

if (process.env.SENDGRID_API_KEY) {
  mailService = new MailService();
  mailService.setApiKey(process.env.SENDGRID_API_KEY as string);
  console.log('SendGrid email service initialized');
} else {
  console.warn('SENDGRID_API_KEY not found. Email service will not be available.');
}

interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text?: string;
  html?: string;
}

// Email templates
const emailTemplates = {
  welcomeMonthly: {
    subject: 'Welcome to Aluminum Cutting Optimizer Pro (Monthly Plan)',
    html: (username: string) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #3366cc;">Welcome to Aluminum Cutting Optimizer Pro!</h1>
        </div>
        
        <div style="margin-bottom: 30px;">
          <p>Hello ${username},</p>
          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We're excited to have you on board.</p>
          <p>You now have full access to all the premium features:</p>
          <ul style="padding-left: 20px;">
            <li>Unlimited cut piece quantities</li>
            <li>Advanced optimization algorithms for minimal waste</li>
            <li>Priority customer support</li>
            <li>Advanced reporting tools</li>
          </ul>
        </div>
        
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>
          <p><strong>Plan:</strong> Pro Monthly</p>
          <p><strong>Price:</strong> $12.00/month</p>
          <p><strong>Billing Cycle:</strong> Monthly</p>
          <p>Your subscription will automatically renew each month until canceled.</p>
        </div>
        
        <div style="margin-bottom: 30px;">
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          <p>Happy cutting!</p>
          <p>The Aluminum Cutting Optimizer Team</p>
        </div>
      </div>
    `,
    text: (username: string) => `
      Welcome to Aluminum Cutting Optimizer Pro!
      
      Hello ${username},
      
      Thank you for subscribing to our Pro Monthly Plan! We're excited to have you on board.
      
      You now have full access to all the premium features:
      - Unlimited cut piece quantities
      - Advanced optimization algorithms for minimal waste
      - Priority customer support
      - Advanced reporting tools
      
      Your Subscription Details:
      Plan: Pro Monthly
      Price: $12.00/month
      Billing Cycle: Monthly
      
      Your subscription will automatically renew each month until canceled.
      
      If you have any questions or need assistance, please don't hesitate to contact our support team.
      
      Happy cutting!
      
      The Aluminum Cutting Optimizer Team
    `
  },
  
  welcomeAnnual: {
    subject: 'Welcome to Aluminum Cutting Optimizer Pro (Annual Plan)',
    html: (username: string) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #3366cc;">Welcome to Aluminum Cutting Optimizer Pro!</h1>
        </div>
        
        <div style="margin-bottom: 30px;">
          <p>Hello ${username},</p>
          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We're excited to have you on board for the entire year.</p>
          <p>You now have full access to all the premium features:</p>
          <ul style="padding-left: 20px;">
            <li>Unlimited cut piece quantities</li>
            <li>Advanced optimization algorithms for minimal waste</li>
            <li>Priority customer support</li>
            <li>Advanced reporting tools</li>
          </ul>
        </div>
        
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>
          <p><strong>Plan:</strong> Pro Annual</p>
          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>
          <p><strong>Billing Cycle:</strong> Annual</p>
          <p>Your subscription will automatically renew each year until canceled.</p>
        </div>
        
        <div style="margin-bottom: 30px;">
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          <p>Happy cutting!</p>
          <p>The Aluminum Cutting Optimizer Team</p>
        </div>
      </div>
    `,
    text: (username: string) => `
      Welcome to Aluminum Cutting Optimizer Pro!
      
      Hello ${username},
      
      Thank you for subscribing to our Pro Annual Plan! We're excited to have you on board for the entire year.
      
      You now have full access to all the premium features:
      - Unlimited cut piece quantities
      - Advanced optimization algorithms for minimal waste
      - Priority customer support
      - Advanced reporting tools
      
      Your Subscription Details:
      Plan: Pro Annual
      Price: $130.00/year (you saved $14 compared to monthly billing)
      Billing Cycle: Annual
      
      Your subscription will automatically renew each year until canceled.
      
      If you have any questions or need assistance, please don't hesitate to contact our support team.
      
      Happy cutting!
      
      The Aluminum Cutting Optimizer Team
    `
  }
};

// Helper function to send welcome emails based on subscription type
export async function sendWelcomeEmail(user: { username: string, email: string | null }, subscriptionType: 'monthly' | 'annual'): Promise<boolean> {
  if (!user.email) {
    console.warn(`Cannot send welcome email to user ${user.username} - no email address`);
    return false;
  }
  
  const template = subscriptionType === 'monthly' ? emailTemplates.welcomeMonthly : emailTemplates.welcomeAnnual;
  
  return await sendEmail({
    to: user.email,
    from: '<EMAIL>',
    subject: template.subject,
    text: template.text(user.username),
    html: template.html(user.username)
  });
}

export async function sendEmail(params: EmailParams): Promise<boolean> {
  if (!mailService) {
    console.warn('SendGrid email service not initialized. Email not sent.');
    // For development, log the email that would have been sent
    console.log('Would have sent email:');
    console.log('To:', params.to);
    console.log('From:', params.from);
    console.log('Subject:', params.subject);
    console.log('Content:', params.text || params.html);
    return false;
  }

  try {
    const mailData = {
      to: params.to,
      from: params.from,
      subject: params.subject,
    } as any;
    
    // Add text or html content
    if (params.text) {
      mailData.text = params.text;
    }
    if (params.html) {
      mailData.html = params.html;
    }
    
    await mailService.send(mailData);
    console.log('Email sent successfully to', params.to);
    return true;
  } catch (error) {
    console.error('SendGrid email error:', error);
    return false;
  }
}