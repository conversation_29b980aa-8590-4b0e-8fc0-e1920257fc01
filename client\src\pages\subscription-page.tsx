import { useEffect, useState } from 'react';
import { useStripe, useElements, Elements, PaymentElement } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useToast } from '@/hooks/use-toast';

// Define valid toast variants
type ToastVariant = 'default' | 'destructive' | 'success';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

// Make sure to call loadStripe outside of a component's render to avoid recreating the Stripe object
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

const SubscriptionForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [fraudCheckPassed, setFraudCheckPassed] = useState(true);
  const { user } = useAuth();

  // Function to perform basic fraud detection checks before submission
  const performFraudCheck = () => {
    // In a real production app, you might want to perform frontend checks like:
    // 1. Is the user session consistent?
    // 2. Is the payment attempt coming from a suspicious IP or device?
    // 3. Are there multiple rapid payment attempts?
    // 4. Has the user recently created their account?
    
    // For this demo, we'll always pass as this is just to demonstrate the concept
    setFraudCheckPassed(true);
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      toast({
        title: 'Payment initialization error',
        description: 'Stripe has not loaded properly. Please refresh the page and try again.',
        variant: 'destructive',
      });
      return;
    }
    
    // Perform frontend fraud check
    if (!performFraudCheck()) {
      toast({
        title: 'Payment blocked',
        description: 'This payment attempt has been flagged for security reasons. Please contact support.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);

    try {
      console.log('Processing payment...');
      
      // Check if the form is complete and valid before submitting
      const { error: elementsError } = await elements.submit();
      if (elementsError) {
        throw new Error(elementsError.message || 'Please check your card details');
      }
      
      // Confirm the payment
      const result = await stripe.confirmPayment({
        elements,
        confirmParams: {
          // Make sure to change this to your payment completion page
          return_url: `${window.location.origin}/payment-success`,
          
          // Payment metadata can be used to store additional information
          // that can be useful for fraud detection or customer support
          payment_method_data: {
            billing_details: {
              email: user?.email || undefined,
              name: user?.name || user?.username || undefined
            },
          },
        },
        redirect: 'if_required',
      });

      console.log('Payment result:', result);

      if (result.error) {
        // Handle specific error types
        if (result.error.type === 'card_error') {
          // Card was declined by the bank
          console.error('Card declined:', result.error);
          toast({
            title: 'Card Declined',
            description: result.error.message || 'Your card was declined. Please try a different card.',
            variant: 'destructive',
          });
        } else if (result.error.type === 'validation_error') {
          // Form validation failed
          console.error('Validation error:', result.error);
          toast({
            title: 'Validation Error',
            description: result.error.message || 'Please check your card details and try again.',
            variant: 'default',
          });
        } else {
          // Other errors
          console.error('Payment error:', result.error);
          toast({
            title: 'Payment Failed',
            description: result.error.message || 'An unexpected error occurred',
            variant: 'destructive',
          });
        }
      } else if (result.paymentIntent && result.paymentIntent.status === 'succeeded') {
        // Payment succeeded, manually complete the process
        console.log('Payment succeeded! ID:', result.paymentIntent.id);
        
        try {
          // Call our manual completion endpoint to ensure the user is upgraded
          const completeResponse = await apiRequest('POST', '/api/complete-payment', {
            paymentIntentId: result.paymentIntent.id
          });
          
          if (completeResponse.ok) {
            const completeData = await completeResponse.json();
            
            if (completeData.success) {
              toast({
                title: 'Payment Successful!',
                description: 'Your account has been upgraded to Pro.',
              });
              // Redirect to payment success page
              window.location.href = '/payment-success';
            } else {
              throw new Error(completeData.message || 'Server verification failed');
            }
          } else {
            const errorData = await completeResponse.json();
            throw new Error(errorData.message || 'Failed to complete payment process');
          }
        } catch (completeError) {
          console.error('Error completing payment process:', completeError);
          toast({
            title: 'Payment Verification Error',
            description: 'Your payment was successful, but we had trouble upgrading your account. Please contact support.',
            variant: 'destructive',
          });
        }
      } else if (result.paymentIntent && result.paymentIntent.status === 'requires_action') {
        // Payment needs additional authentication
        toast({
          title: 'Additional Authentication Required',
          description: 'Please complete the additional authentication steps.',
          variant: 'default',
        });
      } else if (result.paymentIntent) {
        // Handle other payment intent statuses
        toast({
          title: 'Payment Status',
          description: `Payment status: ${result.paymentIntent.status}. Please try again or contact support.`,
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Payment exception:', error);
      toast({
        title: 'Payment Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />
      <Button 
        type="submit" 
        disabled={!stripe || isLoading} 
        className="w-full"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          'Subscribe Now'
        )}
      </Button>
    </form>
  );
};

const SubscriptionPage = () => {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionType, setSubscriptionType] = useState<'monthly' | 'annual'>('annual');
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const getSubscription = async () => {
      try {
        setIsLoading(true);
        
        // Pass subscription type to the API
        const amount = subscriptionType === 'monthly' ? 175 : 2100;
        const period = subscriptionType === 'monthly' ? 'monthly' : 'annual';
        
        const response = await apiRequest('POST', '/api/get-or-create-subscription', {
          amount,
          period
        });
        
        const data = await response.json();
        
        if (data.alreadySubscribed) {
          // User is already subscribed to the Pro plan
          window.location.href = '/payment-success';
          return;
        }
        
        setClientSecret(data.clientSecret);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('Failed to set up subscription. Please try again later.');
        toast({
          title: 'Error',
          description: 'Failed to set up subscription. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      getSubscription();
    }
  }, [user, toast]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4">
        <XCircle className="h-16 w-16 text-destructive" />
        <h1 className="text-2xl font-bold">Error</h1>
        <p className="text-muted-foreground">{error}</p>
        <Button onClick={() => window.location.href = '/'}>
          Back to Home
        </Button>
      </div>
    );
  }

  const options = {
    clientSecret: clientSecret!,
    appearance: {
      theme: 'stripe' as const,
    },
  };

  // Helper function to refresh the subscription data when plan type changes
  const handleSubscriptionTypeChange = (type: 'monthly' | 'annual') => {
    setSubscriptionType(type);
    setClientSecret(null);
    setIsLoading(true);
    
    const getNewSubscription = async () => {
      try {
        const amount = type === 'monthly' ? 175 : 2100;
        const period = type === 'monthly' ? 'monthly' : 'annual';
        
        const response = await apiRequest('POST', '/api/get-or-create-subscription', {
          amount,
          period
        });
        
        const data = await response.json();
        
        if (data.alreadySubscribed) {
          window.location.href = '/payment-success';
          return;
        }
        
        setClientSecret(data.clientSecret);
      } catch (err) {
        console.error('Error updating subscription type:', err);
        toast({
          title: 'Error',
          description: 'Failed to update subscription plan. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    if (user) {
      getNewSubscription();
    }
  };

  return (
    <div className="container max-w-4xl py-12">
      <h1 className="text-3xl font-bold text-center mb-8">Upgrade to Pro Plan</h1>
      
      <div className="mb-8">
        <h2 className="text-center text-xl font-medium mb-3">Choose your billing cycle</h2>
        <div className="flex justify-center">
          <div className="bg-muted p-1 rounded-lg inline-flex shadow">
            <button
              className={`px-6 py-3 rounded-md text-lg transition-all duration-200 ${
                subscriptionType === 'monthly' 
                  ? 'bg-background text-foreground shadow-sm font-medium' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => handleSubscriptionTypeChange('monthly')}
            >
              Monthly
            </button>
            <button
              className={`px-6 py-3 rounded-md text-lg transition-all duration-200 ${
                subscriptionType === 'annual' 
                  ? 'bg-background text-foreground shadow-sm font-medium' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => handleSubscriptionTypeChange('annual')}
            >
              Annual (Save 10%)
            </button>
          </div>
        </div>
        <p className="text-center text-sm text-muted-foreground mt-2">
          {subscriptionType === 'monthly' 
            ? 'Try Pro for just one month and cancel anytime' 
            : 'Get two months free with annual billing'}
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Free Plan</CardTitle>
            <CardDescription>Current plan</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold mb-6">$0 <span className="text-sm font-normal text-muted-foreground">/month</span></p>
            <ul className="space-y-2">
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Up to 50 quantity items</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Basic cutting optimization</span>
              </li>
              <li className="flex items-center">
                <XCircle className="h-5 w-5 text-muted-foreground mr-2" />
                <span className="text-muted-foreground">Advanced optimization algorithms</span>
              </li>
              <li className="flex items-center">
                <XCircle className="h-5 w-5 text-muted-foreground mr-2" />
                <span className="text-muted-foreground">Unlimited quantity items</span>
              </li>
            </ul>
          </CardContent>
        </Card>
        
        <Card className={subscriptionType === 'monthly' ? "border-blue-400" : "border-primary"}>
          <CardHeader className={subscriptionType === 'monthly' 
            ? "bg-blue-50 rounded-t-lg" 
            : "bg-primary/10 rounded-t-lg"
          }>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Pro Plan</CardTitle>
                <CardDescription>Recommended</CardDescription>
              </div>
              {subscriptionType === 'monthly' && (
                <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded">
                  Try for 1 month
                </div>
              )}
              {subscriptionType === 'annual' && (
                <div className="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-1 rounded">
                  Best value
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            {subscriptionType === 'monthly' ? (
              <div className="mb-6">
                <p className="text-3xl font-bold">$175 <span className="text-sm font-normal text-muted-foreground">/month</span></p>
                <p className="text-sm text-blue-600 mt-1">Professional window manufacturing solution</p>
                <div className="mt-2 bg-blue-50 text-blue-800 text-xs px-2 py-1 rounded inline-flex items-center">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Cancel anytime
                </div>
              </div>
            ) : (
              <div className="mb-6">
                <p className="text-3xl font-bold">$2100 <span className="text-sm font-normal text-muted-foreground">/year</span></p>
                <p className="text-sm text-green-600 mt-1">Complete professional manufacturing solution</p>
                <div className="mt-2 bg-green-50 text-green-800 text-xs px-2 py-1 rounded inline-flex items-center">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Best value
                </div>
              </div>
            )}
            <ul className="space-y-2">
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Unlimited quantity items</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Advanced cutting optimization</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Priority customer support</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span>Advanced reporting tools</span>
              </li>
              {subscriptionType === 'annual' && (
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span>10% discount with annual plan</span>
                </li>
              )}
            </ul>
          </CardContent>
          
          <Separator />
          
          <CardFooter className="flex flex-col p-6">
            {isLoading ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : clientSecret ? (
              <>
                <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-200 text-sm">
                  <p className="font-medium text-blue-800 mb-2">Test Mode Info:</p>
                  <p className="text-blue-600 mb-2">Use these test card details:</p>
                  <ul className="list-disc pl-4 text-blue-700 space-y-1">
                    <li>Card number: 4242 4242 4242 4242 (Always succeeds)</li>
                    <li>Card number: 4000 0000 0000 0002 (Always declined)</li>
                    <li>Expiration: Any future date</li>
                    <li>CVC: Any 3 digits</li>
                    <li>ZIP: Any 5 digits</li>
                  </ul>
                  <p className="mt-2 text-blue-600">Note: In test mode, Stripe provides testing cards that simulate successful and failed payments. In production, real card details would be properly validated by Stripe's processing network.</p>
                </div>
                <Elements stripe={stripePromise} options={options}>
                  <SubscriptionForm />
                </Elements>
              </>
            ) : (
              <div className="flex justify-center py-4">
                <Button onClick={() => window.location.reload()}>
                  Retry Loading Payment Form
                </Button>
              </div>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default SubscriptionPage;