import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { queryClient } from '@/lib/queryClient';

const PaymentSuccessPage = () => {
  const [_, setLocation] = useLocation();

  useEffect(() => {
    // Invalidate subscription data cache to reflect changes
    queryClient.invalidateQueries({ queryKey: ['/api/subscription'] });
    queryClient.invalidateQueries({ queryKey: ['/api/user'] });
  }, []);

  return (
    <div className="container max-w-md flex flex-col items-center justify-center min-h-screen py-12">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
          <CardDescription>
            Your subscription to the Pro Plan has been activated
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-2">
          <p>Thank you for upgrading to our Pro Plan.</p>
          <p>You now have access to all premium features including unlimited quantity items and advanced optimization algorithms.</p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={() => setLocation('/')}>
            Continue to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default PaymentSuccessPage;