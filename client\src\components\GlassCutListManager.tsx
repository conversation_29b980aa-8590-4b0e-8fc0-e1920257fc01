import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { GlassPiece } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { 
  Trash2, 
  Plus, 
  RefreshCw, 
  FileUp, 
  FileSpreadsheet,
  ArrowUpCircle
} from "lucide-react";
import * as XLSX from "xlsx";

interface GlassCutListManagerProps {
  glassCutList: GlassPiece[];
  onUpdateGlassCutList: (glassCutList: GlassPiece[]) => void;
  onOptimize: () => void;
  isOptimizing: boolean;
}

export default function GlassCutListManager({ 
  glassCutList, 
  onUpdateGlassCutList,
  onOptimize,
  isOptimizing
}: GlassCutListManagerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [importFile, setImportFile] = useState<File | null>(null);
  const [lastImportedFile, setLastImportedFile] = useState<string | null>(null);

  // Handle input changes
  const handleInputChange = (id: number, field: keyof GlassPiece, value: any) => {
    onUpdateGlassCutList(
      glassCutList.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };
  
  // Add a new row to the glass cut list
  const handleAddRow = () => {
    const nextId = glassCutList.length > 0 
      ? Math.max(...glassCutList.map(item => item.id)) + 1 
      : 1;
    
    onUpdateGlassCutList([
      ...glassCutList, 
      { 
        id: nextId, 
        width: 0, 
        height: 0, 
        quantity: 1, 
        description: "", 
        glassType: "", 
        canRotate: true 
      }
    ]);
  };
  
  // Remove a row from the glass cut list
  const handleRemoveRow = (id: number) => {
    onUpdateGlassCutList(glassCutList.filter(item => item.id !== id));
  };
  
  // Clear all glass cut list data
  const handleClearAll = () => {
    onUpdateGlassCutList([]);
    setLastImportedFile(null);
    const fileInput = document.getElementById('import-glass-file') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    
    toast({
      title: "Glass Cut List Cleared",
      description: "All data has been cleared successfully",
    });
  };
  
  // Handle file import (Excel/CSV)
  const handleImportFile = () => {
    if (!importFile) {
      toast({
        title: "No file selected",
        description: "Please select a file to import",
        variant: "destructive"
      });
      return;
    }

    const fileExtension = importFile.name.split('.').pop()?.toLowerCase();
    
    if (fileExtension === 'csv') {
      // Handle CSV import
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string;
          const rows = text.split('\n');
          
          // Check if the first row might be a header
          const headerRow = rows[0].trim().toLowerCase();
          const hasHeader = headerRow.includes('width') || 
                            headerRow.includes('height') || 
                            headerRow.includes('quantity') ||
                            headerRow.includes('description');
          
          // Determine the column indices for our data
          let widthIndex = 0;
          let heightIndex = 1;
          let quantityIndex = 2;
          let descriptionIndex = 3;
          let glassTypeIndex = -1; // -1 means not found
          let rotateIndex = -1;
          
          if (hasHeader) {
            // Parse header to find column indices
            const columns = headerRow.split(',').map(col => col.trim().toLowerCase());
            
            for (let i = 0; i < columns.length; i++) {
              const col = columns[i];
              if (col.includes('width')) widthIndex = i;
              else if (col.includes('height')) heightIndex = i;
              else if (col.includes('quantity') || col.includes('qty')) quantityIndex = i;
              else if (col.includes('description') || col.includes('desc')) descriptionIndex = i;
              else if (col.includes('glass') || col.includes('type')) glassTypeIndex = i;
              else if (col.includes('rotate') || col.includes('rotation')) rotateIndex = i;
            }
            
            console.log("CSV Headers detected:", { 
              widthIndex, 
              heightIndex,
              quantityIndex, 
              descriptionIndex, 
              glassTypeIndex,
              rotateIndex,
              columns
            });
          }
          
          const importedGlassList: GlassPiece[] = rows
            .filter(row => row.trim() !== '')
            // Skip header row if it exists
            .slice(hasHeader ? 1 : 0)
            .map((row, index) => {
              const columns = row.split(',').map(col => col.trim());
              
              const width = parseFloat(columns[widthIndex]) || 0;
              const height = parseFloat(columns[heightIndex]) || 0;
              const quantity = parseInt(columns[quantityIndex]) || 1;
              const description = columns[descriptionIndex] || '';
              
              // Get glass type if the column exists
              const glassType = glassTypeIndex >= 0 ? columns[glassTypeIndex] || '' : '';
              
              // Parse rotation preference (default to true if not specified)
              const canRotate = rotateIndex >= 0 
                ? columns[rotateIndex].toLowerCase() === 'true' || columns[rotateIndex].toLowerCase() === 'yes' || columns[rotateIndex].toLowerCase() === '1'
                : true;
              
              return {
                id: index + 1,
                width,
                height,
                quantity,
                description,
                glassType,
                canRotate
              };
            });
          
          if (importedGlassList.length === 0) {
            throw new Error("No valid data found in CSV");
          }
          
          // Check subscription limit for imported data
          const totalImportedQuantity = importedGlassList.reduce((sum, piece) => sum + piece.quantity, 0);
          if (user?.subscriptionPlan === 'free' && totalImportedQuantity > user.maxQuantity) {
            toast({
              title: "Free Plan Limit Exceeded",
              description: `Your free plan allows up to ${user.maxQuantity} pieces. This file contains ${totalImportedQuantity} pieces. Please upgrade to a Pro plan or reduce quantities.`,
              variant: "destructive",
            });
            return;
          }
          
          onUpdateGlassCutList(importedGlassList);
          setLastImportedFile(importFile.name);
          toast({
            title: "CSV Import Successful",
            description: `Imported ${importedGlassList.length} glass pieces from ${importFile.name}`,
          });
        } catch (error) {
          toast({
            title: "CSV Import Failed",
            description: error instanceof Error ? error.message : "Unknown error occurred",
            variant: "destructive"
          });
        }
      };
      reader.readAsText(importFile);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      // Handle Excel import
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Assume first sheet contains the data
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData.length === 0) {
            throw new Error("No data found in Excel file");
          }
          
          // Map Excel data to glass pieces
          const importedGlassList: GlassPiece[] = jsonData.map((row: any, index) => {
            // Try different possible column names
            const width = row['Width'] || row['width'] || row['WIDTH'] || row[0] || 0;
            const height = row['Height'] || row['height'] || row['HEIGHT'] || row[1] || 0;
            const quantity = row['Quantity'] || row['quantity'] || row['QUANTITY'] || row['Qty'] || row['qty'] || row[2] || 1;
            const description = row['Description'] || row['description'] || row['DESCRIPTION'] || row['Desc'] || row['desc'] || row[3] || '';
            
            // Log all columns to debug what's coming from the Excel file
            console.log("Excel row data:", row);
            
            // Try to find glass type column with more variations (case insensitive)
            let glassType = '';
            let canRotate = true; // Default to allowing rotation
            
            // Check all object keys case-insensitively for glass type and rotation columns
            Object.keys(row).forEach(key => {
              const lowerKey = key.toLowerCase();
              if (lowerKey.includes('glass') || lowerKey.includes('type')) {
                console.log(`Found potential glass type column: ${key} with value: ${row[key]}`);
                glassType = row[key] || '';
              }
              
              if (lowerKey.includes('rotate') || lowerKey.includes('rotation')) {
                console.log(`Found potential rotation column: ${key} with value: ${row[key]}`);
                const rotateValue = String(row[key]).toLowerCase();
                canRotate = rotateValue === 'true' || rotateValue === 'yes' || rotateValue === '1';
              }
            });
            
            return {
              id: index + 1,
              width: typeof width === 'number' ? width : parseFloat(width) || 0,
              height: typeof height === 'number' ? height : parseFloat(height) || 0,
              quantity: typeof quantity === 'number' ? quantity : parseInt(quantity) || 1,
              description: description?.toString().trim() || '',
              glassType,
              canRotate
            };
          });
          
          // Check subscription limit for imported data
          const totalImportedQuantity = importedGlassList.reduce((sum, piece) => sum + piece.quantity, 0);
          if (user?.subscriptionPlan === 'free' && totalImportedQuantity > user.maxQuantity) {
            toast({
              title: "Free Plan Limit Exceeded",
              description: `Your free plan allows up to ${user.maxQuantity} pieces. This file contains ${totalImportedQuantity} pieces. Please upgrade to a Pro plan or reduce quantities.`,
              variant: "destructive",
            });
            return;
          }
          
          onUpdateGlassCutList(importedGlassList);
          setLastImportedFile(importFile.name);
          toast({
            title: "Excel Import Successful",
            description: `Imported ${importedGlassList.length} glass pieces from ${importFile.name}`,
          });
        } catch (error) {
          console.error("Excel import error:", error);
          toast({
            title: "Excel Import Failed",
            description: error instanceof Error ? error.message : "Unknown error occurred",
            variant: "destructive"
          });
        }
      };
      reader.readAsArrayBuffer(importFile);
    } else {
      toast({
        title: "Unsupported File Format",
        description: "Please select a CSV or Excel file (.xlsx, .xls)",
        variant: "destructive"
      });
    }
    
    // Reset file input
    setImportFile(null);
    const fileInput = document.getElementById('import-glass-file') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Glass Cut List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="min-w-full border border-neutral-300 cut-list-table">
            <thead>
              <tr className="bg-neutral-100">
                <th className="w-14 py-2 border-b border-r border-neutral-300 text-center">#</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Width (mm)</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Height (mm)</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Quantity</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Description</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Glass Type</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Can Rotate</th>
                <th className="w-20 px-4 py-2 border-b border-neutral-300">Actions</th>
              </tr>
            </thead>
            <tbody>
              {glassCutList.map((item, index) => (
                <tr key={item.id}>
                  <td className="row-number">{index + 1}</td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="number"
                      value={item.width}
                      onChange={(e) => handleInputChange(item.id, 'width', parseFloat(e.target.value) || 0)}
                      className="border-0"
                      min="0"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="number"
                      value={item.height}
                      onChange={(e) => handleInputChange(item.id, 'height', parseFloat(e.target.value) || 0)}
                      className="border-0"
                      min="0"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleInputChange(item.id, 'quantity', parseInt(e.target.value) || 1)}
                      className="border-0"
                      min="1"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => handleInputChange(item.id, 'description', e.target.value)}
                      className="border-0"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="text"
                      value={item.glassType}
                      onChange={(e) => handleInputChange(item.id, 'glassType', e.target.value)}
                      className="border-0"
                      placeholder="Enter glass type"
                      title="Enter the glass type (e.g. Clear, Tinted, etc.)"
                    />
                  </td>
                  <td className="border-r border-neutral-300 text-center">
                    <Checkbox
                      checked={item.canRotate}
                      onCheckedChange={(checked) => handleInputChange(item.id, 'canRotate', !!checked)}
                    />
                  </td>
                  <td className="border-neutral-300 text-center p-1">
                    <Button 
                      variant="ghost"
                      size="icon"
                      className="text-danger hover:bg-red-50"
                      onClick={() => handleRemoveRow(item.id)}
                      aria-label="Remove"
                    >
                      <Trash2 className="h-5 w-5" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="mt-4 flex flex-wrap justify-between items-center gap-3">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={handleAddRow}
              className="px-3 py-1.5 bg-secondary text-white flex items-center"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Row
            </Button>
            
            <Button 
              onClick={handleClearAll}
              variant="outline"
              className="px-3 py-1.5 border-red-300 text-red-500 hover:bg-red-50 flex items-center"
              disabled={glassCutList.length === 0 && !lastImportedFile}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Clear All
            </Button>
            
            <div className="flex flex-col">
              <div className="flex items-center gap-2 mb-1">
                <input
                  type="file"
                  id="import-glass-file"
                  accept=".csv,.xlsx,.xls"
                  className="hidden"
                  onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-glass-file')?.click()}
                  className="px-3 py-1.5"
                >
                  <FileUp className="h-4 w-4 mr-2" />
                  Select File
                </Button>
                {importFile && (
                  <Button
                    variant="outline"
                    onClick={handleImportFile}
                    className="px-3 py-1.5"
                  >
                    Import
                  </Button>
                )}
              </div>
              
              {lastImportedFile && (
                <div className="text-sm text-neutral-600 flex items-center">
                  <FileSpreadsheet className="h-4 w-4 mr-1 text-primary" />
                  <span>Imported: {lastImportedFile}</span>
                </div>
              )}
            </div>
          </div>
          
          <Button
            onClick={onOptimize}
            disabled={isOptimizing || glassCutList.length === 0}
            className="px-4 py-2 bg-primary text-white"
          >
            {isOptimizing ? (
              <>Optimizing...</>
            ) : (
              <>
                <ArrowUpCircle className="h-4 w-4 mr-2" />
                Optimize Glass Cutting
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}