import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, ArrowUpCircle, FileUp, FileSpreadsheet, RefreshCw } from "lucide-react";
import { CutPiece } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import * as XLSX from 'xlsx';

interface CutListManagerProps {
  cutList: CutPiece[];
  onUpdateCutList: (cutList: CutPiece[]) => void;
  onOptimize: () => void;
  isOptimizing: boolean;
}

export default function CutListManager({ 
  cutList, 
  onUpdateCutList, 
  onOptimize,
  isOptimizing
}: CutListManagerProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [importFile, setImportFile] = useState<File | null>(null);
  const [lastImportedFile, setLastImportedFile] = useState<string | null>(null);
  
  const handleInputChange = (id: number, field: keyof CutPiece, value: string | number) => {
    // Special handling for quantity changes to enforce subscription limits
    if (field === 'quantity' && user?.subscriptionPlan === 'free') {
      const currentItem = cutList.find(item => item.id === id);
      if (!currentItem) return;
      
      const currentQuantity = currentItem.quantity;
      const newQuantity = typeof value === 'string' ? parseInt(value) : value;
      const quantityDifference = newQuantity - currentQuantity;
      
      if (quantityDifference > 0) {
        // Only check when increasing quantity
        const totalQuantity = cutList.reduce((sum, piece) => sum + (piece.id === id ? 0 : piece.quantity), 0) + newQuantity;
        
        if (totalQuantity > user.maxQuantity) {
          toast({
            title: "Free Plan Limit Exceeded",
            description: `Your free plan allows up to ${user.maxQuantity} pieces. You're trying to use ${totalQuantity} pieces. Please upgrade to Pro plan or reduce your quantity.`,
            variant: "destructive",
          });
          return;
        }
      }
    }
    
    const updatedList = cutList.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    });
    onUpdateCutList(updatedList);
  };
  
  // Check if adding a new item would exceed subscription limits
  const checkSubscriptionLimit = (additionalQuantity: number = 1): boolean => {
    if (!user || user.subscriptionPlan === 'pro') {
      return true; // Pro users or non-logged in users can add without limits
    }
    
    const totalQuantity = cutList.reduce((sum, piece) => sum + piece.quantity, 0);
    if (totalQuantity + additionalQuantity > user.maxQuantity) {
      toast({
        title: "Free Plan Limit Exceeded",
        description: `Your free plan allows up to ${user.maxQuantity} pieces. You're trying to use ${totalQuantity + additionalQuantity} pieces. Please upgrade to Pro plan or reduce your quantity.`,
        variant: "destructive",
      });
      return false;
    }
    return true;
  };
  
  const handleAddRow = () => {
    if (!checkSubscriptionLimit(1)) {
      return;
    }
    
    const nextId = cutList.length > 0 
      ? Math.max(...cutList.map(item => item.id)) + 1 
      : 1;
    
    onUpdateCutList([
      ...cutList, 
      { id: nextId, length: 0, quantity: 1, description: "", profileType: "" }
    ]);
  };
  
  const handleRemoveRow = (id: number) => {
    onUpdateCutList(cutList.filter(item => item.id !== id));
  };
  
  // Clear all cut list data
  const handleClearAll = () => {
    onUpdateCutList([]);
    setLastImportedFile(null);
    const fileInput = document.getElementById('import-file') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    
    toast({
      title: "Cut List Cleared",
      description: "All data has been cleared successfully",
    });
  };

  const handleImportFile = () => {
    if (!importFile) {
      toast({
        title: "No file selected",
        description: "Please select a file to import",
        variant: "destructive"
      });
      return;
    }

    const fileExtension = importFile.name.split('.').pop()?.toLowerCase();
    
    if (fileExtension === 'csv') {
      // Handle CSV import
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string;
          const rows = text.split('\n');
          
          // Check if the first row might be a header
          const headerRow = rows[0].trim().toLowerCase();
          const hasHeader = headerRow.includes('length') || 
                            headerRow.includes('quantity') || 
                            headerRow.includes('description') ||
                            headerRow.includes('profile');
          
          // Determine the column indices for our data
          let lengthIndex = 0;
          let quantityIndex = 1;
          let descriptionIndex = 2;
          let profileTypeIndex = -1; // -1 means not found
          
          if (hasHeader) {
            // Parse header to find column indices
            const columns = headerRow.split(',').map(col => col.trim().toLowerCase());
            
            for (let i = 0; i < columns.length; i++) {
              const col = columns[i];
              if (col.includes('length')) lengthIndex = i;
              else if (col.includes('quantity') || col.includes('qty')) quantityIndex = i;
              else if (col.includes('description') || col.includes('desc')) descriptionIndex = i;
              else if (col.includes('profile') || col.includes('type')) profileTypeIndex = i;
            }
            
            console.log("CSV Headers detected:", { 
              lengthIndex, 
              quantityIndex, 
              descriptionIndex, 
              profileTypeIndex,
              columns
            });
          }
          
          const importedCutList: CutPiece[] = rows
            .filter(row => row.trim() !== '')
            // Skip header row if it exists
            .slice(hasHeader ? 1 : 0)
            .map((row, index) => {
              const columns = row.split(',').map(col => col.trim());
              
              const length = parseFloat(columns[lengthIndex]) || 0;
              const quantity = parseInt(columns[quantityIndex]) || 1;
              const description = columns[descriptionIndex] || '';
              
              // Get profile type if the column exists
              const profileType = profileTypeIndex >= 0 ? columns[profileTypeIndex] || '' : '';
              
              return {
                id: index + 1,
                length,
                quantity,
                description,
                profileType
              };
            });
          
          if (importedCutList.length === 0) {
            throw new Error("No valid data found in CSV");
          }
          
          // Check subscription limit for imported data
          const totalImportedQuantity = importedCutList.reduce((sum, piece) => sum + piece.quantity, 0);
          if (user?.subscriptionPlan === 'free' && totalImportedQuantity > user.maxQuantity) {
            toast({
              title: "Free Plan Limit Exceeded",
              description: `Your free plan allows up to ${user.maxQuantity} pieces. This file contains ${totalImportedQuantity} pieces. Please upgrade to a Pro plan or reduce quantities.`,
              variant: "destructive",
            });
            return;
          }
          
          onUpdateCutList(importedCutList);
          setLastImportedFile(importFile.name);
          toast({
            title: "CSV Import Successful",
            description: `Imported ${importedCutList.length} cut pieces from ${importFile.name}`,
          });
        } catch (error) {
          toast({
            title: "CSV Import Failed",
            description: error instanceof Error ? error.message : "Unknown error occurred",
            variant: "destructive"
          });
        }
      };
      reader.readAsText(importFile);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      // Handle Excel import
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Assume first sheet contains the data
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData.length === 0) {
            throw new Error("No data found in Excel file");
          }
          
          // Map Excel data to cut pieces
          const importedCutList: CutPiece[] = jsonData.map((row: any, index) => {
            // Try different possible column names
            const length = row['Length'] || row['length'] || row['LENGTH'] || row[0] || 0;
            const quantity = row['Quantity'] || row['quantity'] || row['QUANTITY'] || row[1] || 1;
            const description = row['Description'] || row['description'] || row['DESCRIPTION'] || row[2] || '';
            
            // Log all columns to debug what's coming from the Excel file
            console.log("Excel row data:", row);
            
            // Try to find profile type column with more variations (case insensitive)
            let profileType = '';
            
            // Check all object keys case-insensitively for profile type column
            Object.keys(row).forEach(key => {
              const lowerKey = key.toLowerCase();
              if (lowerKey.includes('profile') || lowerKey.includes('type')) {
                console.log(`Found potential profile column: ${key} with value: ${row[key]}`);
                profileType = row[key];
              }
            });
            
            return {
              id: index + 1,
              length: typeof length === 'number' ? length : parseFloat(length) || 0,
              quantity: typeof quantity === 'number' ? quantity : parseInt(quantity) || 1,
              description: description?.toString().trim() || '',
              profileType: profileType || ''  
            };
          });
          
          // Check subscription limit for imported data
          const totalImportedQuantity = importedCutList.reduce((sum, piece) => sum + piece.quantity, 0);
          if (user?.subscriptionPlan === 'free' && totalImportedQuantity > user.maxQuantity) {
            toast({
              title: "Free Plan Limit Exceeded",
              description: `Your free plan allows up to ${user.maxQuantity} pieces. This file contains ${totalImportedQuantity} pieces. Please upgrade to a Pro plan or reduce quantities.`,
              variant: "destructive",
            });
            return;
          }
          
          onUpdateCutList(importedCutList);
          setLastImportedFile(importFile.name);
          toast({
            title: "Excel Import Successful",
            description: `Imported ${importedCutList.length} cut pieces from ${importFile.name}`,
          });
        } catch (error) {
          console.error("Excel import error:", error);
          toast({
            title: "Excel Import Failed",
            description: error instanceof Error ? error.message : "Unknown error occurred",
            variant: "destructive"
          });
        }
      };
      reader.readAsArrayBuffer(importFile);
    } else {
      toast({
        title: "Unsupported File Format",
        description: "Please select a CSV or Excel file (.xlsx, .xls)",
        variant: "destructive"
      });
    }
    
    // Reset file input
    setImportFile(null);
    const fileInput = document.getElementById('import-file') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Cut List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="min-w-full border border-neutral-300 cut-list-table">
            <thead>
              <tr className="bg-neutral-100">
                <th className="w-14 py-2 border-b border-r border-neutral-300 text-center">#</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Length (mm)</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Quantity</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Description</th>
                <th className="px-4 py-2 border-b border-r border-neutral-300">Profile Type</th>
                <th className="w-20 px-4 py-2 border-b border-neutral-300">Actions</th>
              </tr>
            </thead>
            <tbody>
              {cutList.map((item, index) => (
                <tr key={item.id}>
                  <td className="row-number">{index + 1}</td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="number"
                      value={item.length}
                      onChange={(e) => handleInputChange(item.id, 'length', parseFloat(e.target.value) || 0)}
                      className="border-0"
                      min="0"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleInputChange(item.id, 'quantity', parseInt(e.target.value) || 1)}
                      className="border-0"
                      min="1"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="text"
                      value={item.description}
                      onChange={(e) => handleInputChange(item.id, 'description', e.target.value)}
                      className="border-0"
                    />
                  </td>
                  <td className="border-r border-neutral-300 p-0">
                    <input
                      type="text"
                      value={item.profileType}
                      onChange={(e) => handleInputChange(item.id, 'profileType', e.target.value)}
                      className="border-0"
                      placeholder="Enter profile type"
                      title="Enter the profile type (e.g. Frame, Panel, Window, Door, etc)"
                    />
                  </td>
                  <td className="border-neutral-300 text-center p-1">
                    <Button 
                      variant="ghost"
                      size="icon"
                      className="text-danger hover:bg-red-50"
                      onClick={() => handleRemoveRow(item.id)}
                      aria-label="Remove"
                    >
                      <Trash2 className="h-5 w-5" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="mt-4 flex flex-wrap justify-between items-center gap-3">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={handleAddRow}
              className="px-3 py-1.5 bg-secondary text-white flex items-center"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Row
            </Button>
            
            <Button 
              onClick={handleClearAll}
              variant="outline"
              className="px-3 py-1.5 border-red-300 text-red-500 hover:bg-red-50 flex items-center"
              disabled={cutList.length === 0 && !lastImportedFile}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Clear All
            </Button>
            
            <div className="flex flex-col">
              <div className="flex items-center gap-2 mb-1">
                <input
                  type="file"
                  id="import-file"
                  accept=".csv,.xlsx,.xls"
                  className="hidden"
                  onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-file')?.click()}
                  className="px-3 py-1.5"
                >
                  <FileUp className="h-4 w-4 mr-2" />
                  Select File
                </Button>
                {importFile && (
                  <Button
                    variant="outline"
                    onClick={handleImportFile}
                    className="px-3 py-1.5"
                  >
                    Import
                  </Button>
                )}
              </div>
              
              {lastImportedFile && (
                <div className="text-sm text-neutral-600 flex items-center">
                  <FileSpreadsheet className="h-4 w-4 mr-1 text-primary" />
                  <span>Imported: {lastImportedFile}</span>
                </div>
              )}
            </div>
          </div>
          
          <Button
            onClick={onOptimize}
            disabled={isOptimizing || cutList.length === 0}
            className="px-4 py-2 bg-primary text-white"
          >
            {isOptimizing ? (
              <>Optimizing...</>
            ) : (
              <>
                <ArrowUpCircle className="h-4 w-4 mr-2" />
                Optimize Cutting Plan
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
