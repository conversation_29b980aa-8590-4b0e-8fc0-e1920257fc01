import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Save, Download } from "lucide-react";
import { StockSettings } from "@/lib/types";

interface StockMaterialFormProps {
  stockSettings: StockSettings;
  onUpdateSettings: (settings: StockSettings) => void;
}

const formSchema = z.object({
  stockLength: z.coerce.number().positive("Stock length must be positive"),
  kerf: z.coerce.number().positive("Kerf must be positive"),
  endTrim: z.coerce.number().min(0, "End trim must be non-negative"),
});

export default function StockMaterialForm({ stockSettings, onUpdateSettings }: StockMaterialFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      stockLength: stockSettings.stockLength,
      kerf: stockSettings.kerf,
      endTrim: stockSettings.endTrim,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    onUpdateSettings({
      stockLength: values.stockLength,
      kerf: values.kerf,
      endTrim: values.endTrim,
    });
  }

  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate saving to storage
    const settings = form.getValues();
    localStorage.setItem('stock-settings', JSON.stringify(settings));
    
    setTimeout(() => setIsSaving(false), 500);
  };

  const loadSettings = () => {
    setIsLoading(true);
    
    // Load from storage
    const savedSettings = localStorage.getItem('stock-settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      form.reset(settings);
      onUpdateSettings(settings);
    }
    
    setTimeout(() => setIsLoading(false), 500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Stock Material</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onChange={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="stockLength"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Stock Length (mm)</FormLabel>
                    <FormControl>
                      <Input type="number" step="1" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="kerf"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Saw Kerf (mm)</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.1" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="endTrim"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">End Trim (mm)</FormLabel>
                    <FormControl>
                      <Input type="number" step="1" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Button
                type="button"
                className="bg-primary text-white"
                onClick={saveSettings} 
                disabled={isSaving}
              >
                <Save className="h-5 w-5 mr-2" />
                {isSaving ? "Saving..." : "Save Settings"}
              </Button>
              
              <Button
                type="button"
                className="bg-warning text-neutral-900" 
                onClick={loadSettings}
                disabled={isLoading}
              >
                <Download className="h-5 w-5 mr-2" />
                {isLoading ? "Loading..." : "Load Saved Settings"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
