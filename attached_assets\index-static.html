<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aluminum Optimization Tool</title>
    <style>
        :root {
            font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            font-weight: 400;
            color: #213547;
            background-color: #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .app-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #1a73e8;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #5f6368;
            font-size: 1.2rem;
        }

        main {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .optimization-form-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .optimization-form-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .optimization-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            text-align: left;
            flex: 1;
        }

        .form-row {
            display: flex;
            gap: 1rem;
        }

        label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #202124;
        }

        input, select, textarea {
            padding: 0.75rem;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        .error {
            color: #d93025;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .submit-button {
            background-color: #1a73e8;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 1rem;
            transition: background-color 0.2s;
            align-self: center;
        }

        .submit-button:hover {
            background-color: #1765cc;
        }

        .results-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: left;
            display: none;
        }

        .results-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .result-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .result-card h3 {
            margin-top: 0;
            color: #5f6368;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .result-value {
            font-size: 1.5rem;
            font-weight: 500;
            color: #1a73e8;
            margin: 0;
        }

        .quality-section {
            margin-bottom: 2rem;
        }

        .quality-meter {
            height: 20px;
            background-color: #e8eaed;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .quality-fill {
            height: 100%;
            background-color: #1a73e8;
            border-radius: 10px;
            transition: width 0.5s ease-in-out;
        }

        .quality-value {
            text-align: right;
            font-weight: 500;
            color: #1a73e8;
        }

        .recommendations-section h3 {
            color: #202124;
            margin-bottom: 1rem;
        }

        .recommendations-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .recommendations-list li {
            background-color: white;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1a73e8;
        }

        footer {
            margin-top: 2rem;
            text-align: center;
            font-size: 0.8rem;
            color: #888;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header class="header">
            <h1>Aluminum Optimization Tool</h1>
            <p>Optimize your aluminum processing parameters for maximum efficiency and quality</p>
        </header>
        
        <main>
            <div class="optimization-form-container">
                <h2>Enter Aluminum Processing Parameters</h2>
                <form id="optimizationForm" class="optimization-form">
                    <div class="form-group">
                        <label for="alloyType">Aluminum Alloy Type</label>
                        <select id="alloyType" name="alloyType">
                            <option value="1100">1100 - Pure Aluminum</option>
                            <option value="2024">2024 - Copper Alloy</option>
                            <option value="3003">3003 - Manganese Alloy</option>
                            <option value="5052">5052 - Magnesium Alloy</option>
                            <option value="6061" selected>6061 - Magnesium & Silicon Alloy</option>
                            <option value="7075">7075 - Zinc Alloy</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="thickness">Thickness (mm)</label>
                            <input type="number" id="thickness" name="thickness" value="1.0" step="0.1" min="0.1">
                        </div>

                        <div class="form-group">
                            <label for="width">Width (mm)</label>
                            <input type="number" id="width" name="width" value="100" step="1" min="1">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="length">Length (mm)</label>
                            <input type="number" id="length" name="length" value="200" step="1" min="1">
                        </div>

                        <div class="form-group">
                            <label for="quantity">Quantity</label>
                            <input type="number" id="quantity" name="quantity" value="10" step="1" min="1">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Temperature Range (°C)</label>
                        <div class="form-row">
                            <div class="form-group">
                                <input type="number" id="minTemp" name="minTemp" value="450" placeholder="Min" step="1">
                            </div>
                            <div class="form-group">
                                <input type="number" id="maxTemp" name="maxTemp" value="550" placeholder="Max" step="1">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="additionalRequirements">Additional Requirements</label>
                        <textarea id="additionalRequirements" name="additionalRequirements" placeholder="Enter any special requirements or constraints..."></textarea>
                    </div>

                    <button type="submit" class="submit-button">Calculate Optimal Parameters</button>
                </form>
            </div>
            
            <div id="resultsContainer" class="results-container">
                <h2>Optimization Results</h2>
                
                <div class="results-grid">
                    <div class="result-card">
                        <h3>Optimal Temperature</h3>
                        <p id="optimalTemperature" class="result-value">490°C</p>
                    </div>
                    
                    <div class="result-card">
                        <h3>Processing Time</h3>
                        <p id="processingTime" class="result-value">15 minutes</p>
                    </div>
                    
                    <div class="result-card">
                        <h3>Energy Consumption</h3>
                        <p id="energyConsumption" class="result-value">3.3 kWh</p>
                    </div>
                    
                    <div class="result-card">
                        <h3>Estimated Cost</h3>
                        <p id="estimatedCost" class="result-value">$4.50</p>
                    </div>
                </div>
                
                <div class="quality-section">
                    <h3>Quality Score</h3>
                    <div class="quality-meter">
                        <div id="qualityFill" class="quality-fill" style="width: 85%"></div>
                    </div>
                    <p id="qualityValue" class="quality-value">85%</p>
                </div>
                
                <div class="recommendations-section">
                    <h3>Recommendations</h3>
                    <ul id="recommendationsList" class="recommendations-list">
                        <!-- Recommendations will be added here -->
                    </ul>
                </div>
            </div>
        </main>
        
        <footer>
            <p>© <span id="currentYear"></span> Aluminum Optimization Tool</p>
        </footer>
    </div>

    <script>
        // Aluminum alloy properties (simplified for demonstration)
        const alloyProperties = {
            '1100': {
                optimalTempRange: [500, 550],
                processingTimeMultiplier: 0.8,
                energyConsumptionFactor: 0.7,
                costFactor: 0.6
            },
            '2024': {
                optimalTempRange: [480, 520],
                processingTimeMultiplier: 1.0,
                energyConsumptionFactor: 0.9,
                costFactor: 1.2
            },
            '3003': {
                optimalTempRange: [490, 530],
                processingTimeMultiplier: 0.9,
                energyConsumptionFactor: 0.8,
                costFactor: 0.8
            },
            '5052': {
                optimalTempRange: [470, 510],
                processingTimeMultiplier: 1.1,
                energyConsumptionFactor: 1.0,
                costFactor: 1.0
            },
            '6061': {
                optimalTempRange: [460, 520],
                processingTimeMultiplier: 1.2,
                energyConsumptionFactor: 1.1,
                costFactor: 1.1
            },
            '7075': {
                optimalTempRange: [450, 490],
                processingTimeMultiplier: 1.3,
                energyConsumptionFactor: 1.2,
                costFactor: 1.4
            }
        };

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Form submission handler
        document.getElementById('optimizationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const formData = {
                alloyType: document.getElementById('alloyType').value,
                thickness: parseFloat(document.getElementById('thickness').value),
                width: parseFloat(document.getElementById('width').value),
                length: parseFloat(document.getElementById('length').value),
                quantity: parseInt(document.getElementById('quantity').value),
                temperatureRange: [
                    parseFloat(document.getElementById('minTemp').value),
                    parseFloat(document.getElementById('maxTemp').value)
                ],
                additionalRequirements: document.getElementById('additionalRequirements').value
            };
            
            // Validate form
            if (formData.temperatureRange[0] >= formData.temperatureRange[1]) {
                alert('Minimum temperature must be less than maximum temperature');
                return;
            }
            
            // Calculate optimization
            const results = calculateOptimization(formData);
            
            // Display results
            displayResults(results);
        });

        function calculateOptimization(params) {
            // Get alloy properties
            const alloy = alloyProperties[params.alloyType] || alloyProperties['6061'];
            
            // Calculate optimal temperature (weighted average of user range and alloy optimal range)
            const userRangeMidpoint = (params.temperatureRange[0] + params.temperatureRange[1]) / 2;
            const alloyRangeMidpoint = (alloy.optimalTempRange[0] + alloy.optimalTempRange[1]) / 2;
            const optimalTemperature = Math.round((userRangeMidpoint + alloyRangeMidpoint * 2) / 3);
            
            // Calculate volume in cubic centimeters
            const volume = (params.thickness * params.width * params.length) / 1000;
            
            // Calculate total volume
            const totalVolume = volume * params.quantity;
            
            // Calculate processing time (minutes)
            const baseProcessingTime = Math.sqrt(volume) * 0.5;
            const processingTime = Math.round(baseProcessingTime * alloy.processingTimeMultiplier * Math.sqrt(params.quantity));
            
            // Calculate energy consumption (kWh)
            const energyConsumption = parseFloat((totalVolume * 0.0015 * alloy.energyConsumptionFactor).toFixed(2));
            
            // Calculate estimated cost ($)
            const estimatedCost = parseFloat((energyConsumption * 0.15 + totalVolume * 0.02 * alloy.costFactor).toFixed(2));
            
            // Calculate quality score (0-100%)
            const tempDeviation = Math.abs(optimalTemperature - alloyRangeMidpoint) / 
                                (alloy.optimalTempRange[1] - alloy.optimalTempRange[0]);
            const qualityScore = Math.round(Math.max(0, Math.min(100, 100 - tempDeviation * 50)));
            
            // Generate recommendations
            const recommendations = generateRecommendations(params, optimalTemperature, alloy);
            
            return {
                optimalTemperature,
                processingTime,
                energyConsumption,
                estimatedCost,
                qualityScore,
                recommendations
            };
        }

        function generateRecommendations(params, optimalTemp, alloy) {
            const recommendations = [];
            
            // Temperature recommendations
            if (optimalTemp < alloy.optimalTempRange[0]) {
                recommendations.push(`Consider increasing the processing temperature to at least ${alloy.optimalTempRange[0]}°C for optimal results.`);
            } else if (optimalTemp > alloy.optimalTempRange[1]) {
                recommendations.push(`Consider decreasing the processing temperature to at most ${alloy.optimalTempRange[1]}°C to prevent overheating.`);
            } else {
                recommendations.push(`The calculated temperature of ${optimalTemp}°C is within the optimal range for this alloy.`);
            }
            
            // Batch size recommendations
            if (params.quantity > 20) {
                recommendations.push(`For large batch sizes (${params.quantity} units), consider splitting into smaller batches for more consistent quality.`);
            }
            
            // Thickness recommendations
            if (params.thickness < 0.5) {
                recommendations.push(`For very thin material (${params.thickness}mm), use slower heating rates to prevent warping.`);
            } else if (params.thickness > 5) {
                recommendations.push(`For thick material (${params.thickness}mm), consider longer heating times to ensure uniform temperature throughout.`);
            }
            
            // Add general recommendations
            recommendations.push(`Maintain consistent temperature throughout the process for best results.`);
            recommendations.push(`Regular equipment calibration is recommended for precision in aluminum processing.`);
            
            return recommendations;
        }

        function displayResults(results) {
            // Update result values
            document.getElementById('optimalTemperature').textContent = `${results.optimalTemperature}°C`;
            document.getElementById('processingTime').textContent = `${results.processingTime} minutes`;
            document.getElementById('energyConsumption').textContent = `${results.energyConsumption} kWh`;
            document.getElementById('estimatedCost').textContent = `$${results.estimatedCost.toFixed(2)}`;
            
            // Update quality score
            document.getElementById('qualityFill').style.width = `${results.qualityScore}%`;
            document.getElementById('qualityValue').textContent = `${results.qualityScore}%`;
            
            // Update recommendations
            const recommendationsList = document.getElementById('recommendationsList');
            recommendationsList.innerHTML = '';
            results.recommendations.forEach(recommendation => {
                const li = document.createElement('li');
                li.textContent = recommendation;
                recommendationsList.appendChild(li);
            });
            
            // Show results container
            document.getElementById('resultsContainer').style.display = 'block';
            
            // Scroll to results
            document.getElementById('resultsContainer').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
