import { useState, useEffect, useMemo, useRef } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash, Edit, Check, X } from 'lucide-react';
import { Link } from 'wouter';

import { generateCuttingList, generateGlassCuttingList } from '@/lib/formulaEvaluator';
import { useAuth } from '@/hooks/use-auth';
import {
  WindowDesign,
  Component,
  GlassSpecification,
  Profile
} from '@shared/schema';

const DEFAULT_WIDTH = 1000;
const DEFAULT_HEIGHT = 1200;
const DEFAULT_QUANTITY = 1;

// Form schemas
const windowDesignSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  category: z.string().min(1, { message: 'Category is required' }),
  description: z.string().optional(),
  imageUrl: z.string().optional(),
});

const componentSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  componentType: z.string().min(1, { message: 'Type is required' }),
  profileId: z.number().min(1, { message: 'Profile is required' }),
  widthFormula: z.string().optional(), // Now optional
  widthQuantity: z.string().min(1, { message: 'Width quantity is required' }),
  heightFormula: z.string().optional(),
  heightQuantity: z.string().min(1, { message: 'Height quantity is required' }),
  isFixedHeight: z.boolean().default(false),
  fixedHeightValue: z.string().optional().nullable(),
  quantityFormula: z.string().min(1, { message: 'Quantity is required' }),
  leftCutDegree: z.number().default(90).refine(val => val === 45 || val === 90, {
    message: 'Cut degree must be either 45 or 90 degrees'
  }),
  rightCutDegree: z.number().default(90).refine(val => val === 45 || val === 90, {
    message: 'Cut degree must be either 45 or 90 degrees'
  }),
});

const glassSpecSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  glassType: z.string().min(1, { message: 'Type is required' }),
  thickness: z.number().min(1, { message: 'Thickness is required' }),
  widthFormula: z.string().min(1, { message: 'Width formula is required' }),
  heightFormula: z.string().min(1, { message: 'Height formula is required' }),
  isFixedHeight: z.boolean().default(false),
  fixedHeightValue: z.string().optional(),
  quantityFormula: z.string().min(1, { message: 'Quantity is required' }),
});

// Window design categories
const WINDOW_CATEGORIES = [
  'Casement',
  'Sliding',
  'Fixed',
  'Awning',
  'Hopper',
  'Double Hung',
  'Bay',
  'Bow',
  'Specialty',
  'Custom'
];

// Component types
const COMPONENT_TYPES = [
  'Frame',
  'Sash',
  'Transom',
  'Mullion',
  'Glazing Bead',
  'Thermal Break',
  'Reinforcement',
  'Other'
];

// Glass types
const GLASS_TYPES = [
  'Float Glass',
  'Tempered Glass',
  'Laminated Glass',
  'Insulated Glass',
  'Low-E Glass',
  'Tinted Glass',
  'Obscured Glass',
  'Reflective Glass',
  'Other'
];

export default function WindowCalculatorPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('designs');
  const [selectedDesignId, setSelectedDesignId] = useState<number | null>(null);
  const [dimensions, setDimensions] = useState({
    width: DEFAULT_WIDTH,
    height: DEFAULT_HEIGHT,
    quantity: DEFAULT_QUANTITY
  });
  
  // Manage dialogs
  const [showCreateDesignDialog, setShowCreateDesignDialog] = useState(false);
  const [showEditDesignDialog, setShowEditDesignDialog] = useState(false);
  const [showAddComponentDialog, setShowAddComponentDialog] = useState(false);
  const [showAddGlassDialog, setShowAddGlassDialog] = useState(false);
  const [editingComponent, setEditingComponent] = useState<Component | null>(null);
  const [editingGlass, setEditingGlass] = useState<GlassSpecification | null>(null);
  const [editingDesign, setEditingDesign] = useState<WindowDesign | null>(null);
  
  // Image handling for window designs
  const [windowImagePreview, setWindowImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle window design image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageDataUrl = e.target?.result as string;
      setWindowImagePreview(imageDataUrl);
      designForm.setValue('imageUrl', imageDataUrl);
    };
    reader.readAsDataURL(file);
  };
  
  // Create window design form
  const designForm = useForm<z.infer<typeof windowDesignSchema>>({
    resolver: zodResolver(windowDesignSchema),
    defaultValues: {
      name: '',
      category: '',
      description: '',
      imageUrl: '',
    },
  });
  
  // Component form
  const componentForm = useForm<z.infer<typeof componentSchema>>({
    resolver: zodResolver(componentSchema),
    defaultValues: {
      name: '',
      componentType: '',
      profileId: 0,
      widthFormula: '',
      widthQuantity: '1',
      heightFormula: '',
      heightQuantity: '1',
      isFixedHeight: false,
      fixedHeightValue: null,
      quantityFormula: '1',
      leftCutDegree: 90,
      rightCutDegree: 90,
    },
  });
  
  // Glass specification form
  const glassForm = useForm<z.infer<typeof glassSpecSchema>>({
    resolver: zodResolver(glassSpecSchema),
    defaultValues: {
      name: '',
      glassType: '',
      thickness: 4,
      widthFormula: '',
      heightFormula: '',
      isFixedHeight: false,
      fixedHeightValue: '',
      quantityFormula: '1',
    },
  });

  // Fetch all window designs
  const { data: windowDesigns = [], isLoading: isLoadingDesigns } = useQuery({
    queryKey: ['/api/window-designs'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/window-designs');
      return (await res.json()) as WindowDesign[];
    }
  });

  // Fetch all profiles for reference
  const { data: profiles = [], isLoading: isLoadingProfiles } = useQuery({
    queryKey: ['/api/profiles'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/profiles');
      return (await res.json()) as Profile[];
    }
  });

  // Fetch components for selected design
  const { data: components = [], isLoading: isLoadingComponents } = useQuery({
    queryKey: ['/api/window-designs', selectedDesignId, 'components'],
    queryFn: async () => {
      if (!selectedDesignId) return [];
      const res = await apiRequest('GET', `/api/window-designs/${selectedDesignId}/components`);
      return (await res.json()) as Component[];
    },
    enabled: !!selectedDesignId
  });

  // Fetch glass specifications for selected design
  const { data: glassSpecs = [], isLoading: isLoadingGlass } = useQuery({
    queryKey: ['/api/window-designs', selectedDesignId, 'glass-specifications'],
    queryFn: async () => {
      if (!selectedDesignId) return [];
      const res = await apiRequest('GET', `/api/window-designs/${selectedDesignId}/glass-specifications`);
      return (await res.json()) as GlassSpecification[];
    },
    enabled: !!selectedDesignId
  });

  // Create window design mutation
  const createDesignMutation = useMutation({
    mutationFn: async (data: z.infer<typeof windowDesignSchema>) => {
      const res = await apiRequest('POST', '/api/window-designs', data);
      return await res.json();
    },
    onSuccess: (newDesign) => {
      toast({
        title: t('Design created'),
        description: t('Window design created successfully'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-designs'] });
      setShowCreateDesignDialog(false);
      setSelectedDesignId(newDesign.id);
      designForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error creating design'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Create component mutation
  const createComponentMutation = useMutation({
    mutationFn: async (data: z.infer<typeof componentSchema> & { windowDesignId: number }) => {
      const res = await apiRequest('POST', '/api/components', data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Component added'),
        description: t('Component added to window design'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'components'] 
      });
      setShowAddComponentDialog(false);
      setEditingComponent(null);
      componentForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error adding component'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Update component mutation
  const updateComponentMutation = useMutation({
    mutationFn: async (params: { id: number, data: Partial<z.infer<typeof componentSchema>> }) => {
      const { id, data } = params;
      const res = await apiRequest('PUT', `/api/components/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Component updated'),
        description: t('Component updated successfully'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'components'] 
      });
      setShowAddComponentDialog(false);
      setEditingComponent(null);
      componentForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error updating component'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Delete component mutation
  const deleteComponentMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/components/${id}`);
    },
    onSuccess: () => {
      toast({
        title: t('Component deleted'),
        description: t('Component deleted successfully'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'components'] 
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('Error deleting component'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Create glass specification mutation
  const createGlassMutation = useMutation({
    mutationFn: async (data: z.infer<typeof glassSpecSchema> & { windowDesignId: number }) => {
      const res = await apiRequest('POST', '/api/glass-specifications', data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Glass specification added'),
        description: t('Glass specification added to window design'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'glass-specifications'] 
      });
      setShowAddGlassDialog(false);
      glassForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error adding glass specification'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Update glass specification mutation
  const updateGlassMutation = useMutation({
    mutationFn: async (params: { id: number, data: Partial<z.infer<typeof glassSpecSchema>> }) => {
      const { id, data } = params;
      const res = await apiRequest('PUT', `/api/glass-specifications/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Glass specification updated'),
        description: t('Glass specification updated successfully'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'glass-specifications'] 
      });
      setShowAddGlassDialog(false);
      setEditingGlass(null);
      glassForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error updating glass specification'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Delete glass specification mutation
  const deleteGlassMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/glass-specifications/${id}`);
    },
    onSuccess: () => {
      toast({
        title: t('Glass specification deleted'),
        description: t('Glass specification deleted successfully'),
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/window-designs', selectedDesignId, 'glass-specifications'] 
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('Error deleting glass specification'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Create cutting list mutation
  const createCuttingListMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest('POST', '/api/window-cutting-lists', data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Cutting list saved'),
        description: t('Your cutting list has been saved successfully'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-cutting-lists'] });
    },
    onError: (error: Error) => {
      toast({
        title: t('Error saving cutting list'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Selected design
  const selectedDesign = useMemo(() => {
    return windowDesigns.find(d => d.id === selectedDesignId) || null;
  }, [windowDesigns, selectedDesignId]);

  // Generate cutting list
  const cuttingList = useMemo(() => {
    if (!components.length) return [];
    return generateCuttingList(
      components,
      dimensions.width,
      dimensions.height,
      dimensions.quantity
    );
  }, [components, dimensions]);

  // Generate glass cutting list
  const glassCuttingList = useMemo(() => {
    if (!glassSpecs.length) return [];
    return generateGlassCuttingList(
      glassSpecs,
      dimensions.width,
      dimensions.height,
      dimensions.quantity
    );
  }, [glassSpecs, dimensions]);

  // Get profile information for a component
  const getProfile = (profileId: number) => {
    return profiles.find(p => p.id === profileId);
  };

  // Handle saving the cutting list
  const handleSaveCuttingList = () => {
    if (!selectedDesign) return;

    createCuttingListMutation.mutate({
      name: `${selectedDesign.name} ${dimensions.width}x${dimensions.height}`,
      windowDesignId: selectedDesign.id,
      width: dimensions.width,
      height: dimensions.height,
      quantity: dimensions.quantity,
      components: JSON.stringify(cuttingList),
      glassItems: JSON.stringify(glassCuttingList),
      userId: user?.id
    });
  };

  // Reset dimensions when design changes
  useEffect(() => {
    if (selectedDesign) {
      // Could set default dimensions based on the design if needed
      setDimensions({
        width: DEFAULT_WIDTH,
        height: DEFAULT_HEIGHT,
        quantity: DEFAULT_QUANTITY
      });
    }
  }, [selectedDesign]);

  // Update window design mutation
  const updateDesignMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const res = await apiRequest('PUT', `/api/window-designs/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: t('Design updated'),
        description: t('Window design updated successfully'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-designs'] });
      setShowEditDesignDialog(false);
      setEditingDesign(null);
      designForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: t('Error updating design'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Event handlers
  const handleCreateDesign = (data: z.infer<typeof windowDesignSchema>) => {
    createDesignMutation.mutate(data);
  };
  
  const handleUpdateDesign = (data: z.infer<typeof windowDesignSchema>) => {
    if (!editingDesign) return;
    
    updateDesignMutation.mutate({
      id: editingDesign.id,
      data: data
    });
  };
  
  const handleAddComponent = (data: z.infer<typeof componentSchema>) => {
    if (!selectedDesignId) return;
    
    // If we're editing an existing component
    if (editingComponent) {
      updateComponentMutation.mutate({
        id: editingComponent.id,
        data: data
      });
      return;
    }
    
    // No longer checking for duplicates - allowing identical components
    createComponentMutation.mutate({
      ...data,
      windowDesignId: selectedDesignId
    });
  };
  
  // Handle duplicating a component
  const handleDuplicateComponent = (component: Component) => {
    if (!selectedDesignId) return;
    
    // Create a new component with the same properties
    const duplicatedComponent = {
      name: `${component.name} (Copy)`,
      componentType: component.componentType,
      profileId: component.profileId,
      widthFormula: component.widthFormula || '',
      widthQuantity: component.widthQuantity || '1',
      heightFormula: component.heightFormula || '',
      heightQuantity: component.heightQuantity || '1',
      isFixedHeight: component.isFixedHeight || false,
      fixedHeightValue: component.fixedHeightValue || '',
      quantityFormula: component.quantityFormula,
      leftCutDegree: component.leftCutDegree === 45 ? 45 : 90 as const,
      rightCutDegree: component.rightCutDegree === 45 ? 45 : 90 as const,
      notes: component.notes || '',
      position: component.position,
      customProperties: component.customProperties,
      windowDesignId: selectedDesignId
    };
    
    createComponentMutation.mutate(duplicatedComponent);
    
    toast({
      title: t('Component duplicated'),
      description: t('Component has been duplicated successfully'),
    });
  };
  
  // Handle editing a component
  const handleEditComponent = (component: Component) => {
    setEditingComponent(component);
    
    // Fill the form with the component data
    componentForm.reset({
      name: component.name,
      componentType: component.componentType,
      profileId: component.profileId,
      widthFormula: component.widthFormula || '',
      widthQuantity: component.widthQuantity || '1',
      heightFormula: component.heightFormula || '',
      heightQuantity: component.heightQuantity || '1',
      isFixedHeight: component.isFixedHeight || false,
      fixedHeightValue: component.fixedHeightValue || '',
      quantityFormula: component.quantityFormula,
      leftCutDegree: component.leftCutDegree === 45 ? 45 : 90 as const,
      rightCutDegree: component.rightCutDegree === 45 ? 45 : 90 as const,
      notes: component.notes || ''
    });
    
    // Open the component dialog
    setShowAddComponentDialog(true);
  };
  
  // Handle deleting a component
  const handleDeleteComponent = (id: number) => {
    // Ask for confirmation
    if (confirm(t('Are you sure you want to delete this component? This action cannot be undone.'))) {
      deleteComponentMutation.mutate(id);
    }
  };
  
  const handleAddGlass = (data: z.infer<typeof glassSpecSchema>) => {
    if (!selectedDesignId) return;
    
    // If we're editing an existing glass specification
    if (editingGlass) {
      updateGlassMutation.mutate({
        id: editingGlass.id,
        data: data
      });
      return;
    }
    
    // No longer checking for duplicates - allowing identical glass specifications
    createGlassMutation.mutate({
      ...data,
      windowDesignId: selectedDesignId
    });
  };
  
  // Handle duplicating a glass specification
  const handleDuplicateGlass = (glass: GlassSpecification) => {
    if (!selectedDesignId) return;
    
    // Create a new glass specification with the same properties
    const duplicatedGlass = {
      name: `${glass.name} (Copy)`,
      glassType: glass.glassType,
      thickness: glass.thickness,
      widthFormula: glass.widthFormula,
      heightFormula: glass.heightFormula,
      isFixedHeight: glass.isFixedHeight || false,
      fixedHeightValue: glass.fixedHeightValue || '',
      quantityFormula: glass.quantityFormula || '1',
      notes: glass.notes || '',
      position: glass.position,
      customProperties: glass.customProperties,
      windowDesignId: selectedDesignId
    };
    
    createGlassMutation.mutate(duplicatedGlass);
    
    toast({
      title: t('Glass specification duplicated'),
      description: t('Glass specification has been duplicated successfully'),
    });
  };
  
  // Handle editing a glass specification
  const handleEditGlass = (glass: GlassSpecification) => {
    setEditingGlass(glass);
    
    // Fill the form with the glass specification data
    glassForm.reset({
      name: glass.name,
      glassType: glass.glassType,
      thickness: glass.thickness,
      widthFormula: glass.widthFormula || '',
      heightFormula: glass.heightFormula || '',
      isFixedHeight: glass.isFixedHeight || false,
      fixedHeightValue: glass.fixedHeightValue || '',
      quantityFormula: glass.quantityFormula,
    });
    
    // Open the glass dialog
    setShowAddGlassDialog(true);
  };
  
  // Handle deleting a glass specification
  const handleDeleteGlass = (id: number) => {
    // Ask for confirmation
    if (confirm(t('Are you sure you want to delete this glass specification? This action cannot be undone.'))) {
      deleteGlassMutation.mutate(id);
    }
  };
  
  const handleEditDesign = (design: WindowDesign) => {
    setEditingDesign(design);
    
    // Fill the form with the design data
    designForm.reset({
      name: design.name,
      category: design.category || '',
      description: design.description || '',
      imageUrl: design.imageUrl || '',
    });
    
    // Set image preview if available
    setWindowImagePreview(design.imageUrl || null);
    
    // Open the edit dialog
    setShowEditDesignDialog(true);
  };
  
  const handleCopyDesign = async (design: WindowDesign) => {
    try {
      // Create a new design based on the current one
      const newDesignData = {
        name: `${design.name} (${t('Copy')})`,
        category: design.category,
        description: design.description,
        imageUrl: design.imageUrl,
        userId: user?.id
      };
      
      // Create the new design
      const res = await apiRequest('POST', '/api/window-designs', newDesignData);
      const newDesign = await res.json();
      
      // Get all components and glass specifications from the original design
      const componentsRes = await apiRequest('GET', `/api/window-designs/${design.id}/components`);
      const components = await componentsRes.json();
      
      const glassRes = await apiRequest('GET', `/api/window-designs/${design.id}/glass-specifications`);
      const glassSpecs = await glassRes.json();
      
      // Copy all components to the new design
      for (const component of components) {
        await apiRequest('POST', '/api/components', {
          windowDesignId: newDesign.id,
          profileId: component.profileId,
          name: component.name,
          componentType: component.componentType,
          widthFormula: component.widthFormula || '',
          widthQuantity: component.widthQuantity || '1',
          heightFormula: component.heightFormula || '',
          heightQuantity: component.heightQuantity || '1',
          isFixedHeight: component.isFixedHeight || false,
          fixedHeightValue: component.fixedHeightValue || '',
          quantityFormula: component.quantityFormula || '1',
          leftCutDegree: component.leftCutDegree === 45 ? 45 : 90 as const,
          rightCutDegree: component.rightCutDegree === 45 ? 45 : 90 as const,
          notes: component.notes || '',
          position: component.position,
          customProperties: component.customProperties
        });
      }
      
      // Copy all glass specifications to the new design
      for (const glass of glassSpecs) {
        await apiRequest('POST', '/api/glass-specifications', {
          windowDesignId: newDesign.id,
          name: glass.name,
          glassType: glass.glassType,
          thickness: glass.thickness,
          widthFormula: glass.widthFormula,
          heightFormula: glass.heightFormula,
          isFixedHeight: glass.isFixedHeight || false,
          fixedHeightValue: glass.fixedHeightValue || '',
          quantityFormula: glass.quantityFormula || '1',
          notes: glass.notes || '',
          position: glass.position,
          customProperties: glass.customProperties
        });
      }
      
      // Refresh the designs list
      queryClient.invalidateQueries({ queryKey: ['/api/window-designs'] });
      
      toast({
        title: t('Design copied'),
        description: t('Window design has been duplicated with all components and glass specifications'),
      });
      
      // Select the new design
      setSelectedDesignId(newDesign.id);
    } catch (error) {
      toast({
        title: t('Error copying design'),
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    }
  };
  
  const handleDeleteDesign = async (design: WindowDesign) => {
    // Confirm deletion with the user
    const warningMessage = t('Are you sure you want to delete this window design? This will also delete all related components, glass specifications, cutting lists, and remove it from any projects. This action cannot be undone.');
    if (!confirm(warningMessage)) {
      return;
    }
    
    try {
      // Our backend now handles cascading deletion - just make a single request
      await apiRequest('DELETE', `/api/window-designs/${design.id}`);
      
      // Refresh designs list
      queryClient.invalidateQueries({ queryKey: ['/api/window-designs'] });
      
      // Also invalidate any project data that might have referenced this design
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects'] });
      
      // Show success message
      toast({
        title: t('Design deleted'),
        description: t('Window design and all related data have been permanently deleted'),
      });
      
      // If the deleted design was selected, clear the selection
      if (selectedDesignId === design.id) {
        setSelectedDesignId(null);
      }
      
    } catch (error) {
      console.error('Error deleting design:', error);
      toast({
        title: t('Error deleting design'),
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    }
  };
  
  // When design is selected, setup component/glass buttons
  useEffect(() => {
    if (selectedDesignId) {
      // Reset form data
      componentForm.reset({
        name: '',
        componentType: '',
        profileId: 0,
        widthFormula: 'width - 10',
        widthQuantity: '1',
        heightFormula: '',
        heightQuantity: '1',
        isFixedHeight: false,
        fixedHeightValue: '',
        quantityFormula: '1',
        leftCutDegree: 90,
        rightCutDegree: 90,
      });
      
      glassForm.reset({
        name: 'Glass Panel',
        glassType: GLASS_TYPES[0],
        thickness: 4,
        widthFormula: 'width - 20',
        heightFormula: 'height - 20',
        isFixedHeight: false,
        fixedHeightValue: '',
        quantityFormula: '1',
      });
    }
  }, [selectedDesignId]);

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            asChild
          >
            <Link href="/">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="m15 18-6-6 6-6"/></svg>
              {t('Back')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t('Window Cutting List Calculator')}</h1>
        </div>
        <Button 
          variant="outline" 
          asChild
        >
          <Link href="/window-projects">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2z"/><path d="M10 6h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z"/><path d="M17 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2z"/></svg>
            {t('Window Projects')}
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left column - Design selection */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>{t('Window Design')}</CardTitle>
            <CardDescription>{t('Select a window design to calculate cutting list')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="designs" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full">
                <TabsTrigger value="designs" className="flex-1">{t('Designs')}</TabsTrigger>
                <TabsTrigger value="dimensions" className="flex-1">{t('Dimensions')}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="designs" className="mt-4">
                {isLoadingDesigns ? (
                  <div className="flex justify-center py-6">
                    <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
                  </div>
                ) : windowDesigns.length === 0 ? (
                  <div className="text-center py-6 text-muted-foreground">
                    <p>{t('No window designs available')}</p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => setShowCreateDesignDialog(true)}
                    >
                      {t('Create New Design')}
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => setShowCreateDesignDialog(true)}
                      >
                        {t('Create New Design')}
                      </Button>
                    </div>
                    <ScrollArea className="h-[350px]">
                      <div className="space-y-2">
                        {windowDesigns.map(design => (
                          <div key={design.id} className="group relative">
                            <Button
                              variant={design.id === selectedDesignId ? "default" : "outline"}
                              className="w-full justify-start pr-20"
                              onClick={() => setSelectedDesignId(design.id)}
                            >
                              <div className="flex items-center gap-3 w-full">
                                {design.thumbnailUrl && (
                                  <div className="h-10 w-12 flex-shrink-0">
                                    <img 
                                      src={design.thumbnailUrl}
                                      alt=""
                                      className="h-full w-full object-contain"
                                      onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                      }}
                                    />
                                  </div>
                                )}
                                <div className="flex flex-col items-start">
                                  <span>{design.name}</span>
                                  <span className="text-xs text-muted-foreground">{design.category}</span>
                                </div>
                              </div>
                            </Button>
                            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditDesign(design);
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/></svg>
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCopyDesign(design);
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"/><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/></svg>
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-destructive"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteDesign(design);
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </>
                )}
              </TabsContent>
              
              <TabsContent value="dimensions" className="mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">{t('Width (mm)')}</Label>
                    <Input
                      id="width"
                      type="number"
                      value={dimensions.width}
                      onChange={(e) => setDimensions(prev => ({ ...prev, width: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="height">{t('Height (mm)')}</Label>
                    <Input
                      id="height"
                      type="number"
                      value={dimensions.height}
                      onChange={(e) => setDimensions(prev => ({ ...prev, height: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="quantity">{t('Quantity')}</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="1"
                      value={dimensions.quantity}
                      onChange={(e) => setDimensions(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter>
            {selectedDesign && (
              <div className="w-full">
                <div className="bg-muted p-3 rounded-md mb-3">
                  <h3 className="font-medium">{selectedDesign.name}</h3>
                  
                  {/* Display window design image if available */}
                  {selectedDesign.imageUrl && (
                    <div className="flex justify-center my-3">
                      <img 
                        src={selectedDesign.imageUrl} 
                        alt={selectedDesign.name} 
                        className="max-h-40 object-contain rounded-md border border-border"
                        onError={(e) => {
                          // Hide broken images
                          e.currentTarget.style.display = 'none';
                          console.error('Failed to load window design image:', selectedDesign.imageUrl);
                        }}
                      />
                    </div>
                  )}
                  
                  <p className="text-sm text-muted-foreground">{selectedDesign.description}</p>
                  <Badge variant="outline" className="mt-2">{selectedDesign.category}</Badge>
                </div>
                <Button 
                  className="w-full" 
                  onClick={handleSaveCuttingList}
                  disabled={!components.length && !glassSpecs.length}
                >
                  {t('Save Cutting List')}
                </Button>
              </div>
            )}
          </CardFooter>
        </Card>

        {/* Right column - Results */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{t('Cutting List')}</CardTitle>
            <CardDescription>
              {selectedDesign 
                ? t('Calculated components for window dimensions: {{width}}mm × {{height}}mm × {{quantity}}', dimensions) 
                : t('Select a window design to view cutting list')
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedDesignId ? (
              <div className="text-center py-12 text-muted-foreground">
                {t('Please select a window design from the left panel')}
              </div>
            ) : isLoadingComponents || isLoadingGlass ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : (
              <Tabs defaultValue="components">
                <TabsList className="w-full">
                  <TabsTrigger value="components" className="flex-1">{t('Aluminum Components')}</TabsTrigger>
                  <TabsTrigger value="glass" className="flex-1">{t('Glass')}</TabsTrigger>
                </TabsList>
                
                <TabsContent value="components" className="mt-4">
                  <div className="mb-4 flex justify-end">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setShowAddComponentDialog(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('Add Component')}
                    </Button>
                  </div>
                  
                  {cuttingList.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>{t('No components defined for this window design')}</p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => setShowAddComponentDialog(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        {t('Add Component')}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="rounded-md border">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-muted/50">
                              <th className="p-2 text-left">{t('Component')}</th>
                              <th className="p-2 text-left">{t('Profile')}</th>
                              <th className="p-2 text-right">{t('Width')}</th>
                              <th className="p-2 text-right">{t('Width Qty')}</th>
                              <th className="p-2 text-right">{t('Height')}</th>
                              <th className="p-2 text-right">{t('Height Qty')}</th>
                              <th className="p-2 text-right">{t('Left Cut')}</th>
                              <th className="p-2 text-right">{t('Right Cut')}</th>
                              <th className="p-2 text-right">{t('Total Qty')}</th>
                              <th className="p-2 text-center">{t('Actions')}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {cuttingList.map((item, index) => {
                              const profile = getProfile(item.profileId);
                              // Find the original component from the components array to get access to id
                              const originalComponent = components.find(c => c.id === item.id);
                              
                              return (
                                <tr key={index} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/30'}>
                                  <td className="p-2">
                                    <div>
                                      <span className="font-medium">{item.name}</span>
                                      <span className="text-xs block text-muted-foreground">{item.componentType}</span>
                                    </div>
                                  </td>
                                  <td className="p-2">
                                    {profile ? (
                                      <div className="flex items-center gap-2">
                                        {profile.imageUrl || profile.thumbnailUrl ? (
                                          <div className="relative group">
                                            <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden border border-border cursor-pointer">
                                              <img
                                                src={profile.thumbnailUrl || profile.imageUrl}
                                                alt={profile.name}
                                                className="h-full w-full object-cover"
                                                onError={(e) => {
                                                  // Hide broken images
                                                  e.currentTarget.style.display = 'none';
                                                }}
                                              />
                                            </div>
                                            {/* Enlarged image on hover */}
                                            <div className="hidden group-hover:block absolute z-50 left-0 -top-2 transform -translate-y-full shadow-lg rounded-md border border-border bg-background p-1">
                                              <div className="h-44 w-44 overflow-hidden rounded">
                                                <img
                                                  src={profile.imageUrl || profile.thumbnailUrl}
                                                  alt={profile.name}
                                                  className="h-full w-full object-contain"
                                                  onError={(e) => {
                                                    // Hide broken images
                                                    e.currentTarget.style.display = 'none';
                                                  }}
                                                />
                                              </div>
                                              <div className="text-xs text-center mt-1 font-medium">{profile.name}</div>
                                            </div>
                                          </div>
                                        ) : (
                                          <div className="h-10 w-10 flex-shrink-0 rounded bg-muted/50 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M3 15v4h4"/><path d="M21 9V5h-4"/></svg>
                                          </div>
                                        )}
                                        <div>
                                          <span>{profile.name}</span>
                                          <span className="text-xs block text-muted-foreground">{profile.category}</span>
                                          {profile.profileCode && (
                                            <span className="text-xs text-muted-foreground">{profile.profileCode}</span>
                                          )}
                                        </div>
                                      </div>
                                    ) : t('Unknown')}
                                  </td>
                                  <td className="p-2 text-right">{item.error ? '—' : `${item.calculatedWidth} mm`}</td>
                                  <td className="p-2 text-right">{item.error ? '—' : (item.calculatedWidthQuantity || 1)}</td>
                                  <td className="p-2 text-right">
                                    {item.calculatedHeight === null ? '—' : (item.error ? '—' : `${item.calculatedHeight} mm`)}
                                  </td>
                                  <td className="p-2 text-right">{item.error ? '—' : (item.calculatedHeightQuantity || 1)}</td>
                                  <td className="p-2 text-right">{item.leftCutDegree ? `${item.leftCutDegree}°` : '90°'}</td>
                                  <td className="p-2 text-right">{item.rightCutDegree ? `${item.rightCutDegree}°` : '90°'}</td>
                                  <td className="p-2 text-right">{item.error ? '—' : item.totalQuantity}</td>
                                  <td className="p-2 text-right">
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalComponent && handleDuplicateComponent(originalComponent)}
                                        disabled={!originalComponent}
                                        title={t('Duplicate')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="8" y="8" width="12" height="12" rx="2" ry="2"/><path d="M16 8V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h2"/></svg>
                                        <span className="sr-only">{t('Duplicate')}</span>
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalComponent && handleEditComponent(originalComponent)}
                                        disabled={!originalComponent}
                                        title={t('Edit')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path></svg>
                                        <span className="sr-only">{t('Edit')}</span>
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalComponent && handleDeleteComponent(originalComponent.id)}
                                        disabled={!originalComponent}
                                        title={t('Delete')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                                        <span className="sr-only">{t('Delete')}</span>
                                      </Button>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="glass" className="mt-4">
                  <div className="mb-4 flex justify-end">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setShowAddGlassDialog(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('Add Glass Specification')}
                    </Button>
                  </div>
                  
                  {glassCuttingList.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>{t('No glass specifications defined for this window design')}</p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => setShowAddGlassDialog(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        {t('Add Glass Specification')}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="rounded-md border">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-muted/50">
                              <th className="p-2 text-left">{t('Glass Item')}</th>
                              <th className="p-2 text-left">{t('Type')}</th>
                              <th className="p-2 text-right">{t('Width')}</th>
                              <th className="p-2 text-right">{t('Height')}</th>
                              <th className="p-2 text-right">{t('Thickness')}</th>
                              <th className="p-2 text-right">{t('Qty')}</th>
                              <th className="p-2 text-right">{t('Area (m²)')}</th>
                              <th className="p-2 text-right">{t('Actions')}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {glassCuttingList.map((item, index) => {
                              // Find the original glass specification to edit/delete
                              const originalGlass = glassSpecs.find(g => g.id === item.id);
                              
                              return (
                                <tr key={index} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/30'}>
                                  <td className="p-2">
                                    <span className="font-medium">{item.name}</span>
                                  </td>
                                  <td className="p-2">{item.glassType}</td>
                                  <td className="p-2 text-right">{item.error ? '—' : `${item.calculatedWidth} mm`}</td>
                                  <td className="p-2 text-right">{item.error ? '—' : `${item.calculatedHeight} mm`}</td>
                                  <td className="p-2 text-right">{item.thickness} mm</td>
                                  <td className="p-2 text-right">{item.error ? '—' : item.totalQuantity}</td>
                                  <td className="p-2 text-right">{item.error ? '—' : item.totalAreaM2.toFixed(2)}</td>
                                  <td className="p-2 text-right">
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalGlass && handleDuplicateGlass(originalGlass)}
                                        disabled={!originalGlass}
                                        title={t('Duplicate')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="8" y="8" width="12" height="12" rx="2" ry="2"/><path d="M16 8V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h2"/></svg>
                                        <span className="sr-only">{t('Duplicate')}</span>
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalGlass && handleEditGlass(originalGlass)}
                                        disabled={!originalGlass}
                                        title={t('Edit')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path></svg>
                                        <span className="sr-only">{t('Edit')}</span>
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => originalGlass && handleDeleteGlass(originalGlass.id)}
                                        disabled={!originalGlass}
                                        title={t('Delete')}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                                        <span className="sr-only">{t('Delete')}</span>
                                      </Button>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                            {/* Summary row */}
                            <tr className="bg-muted/70 font-medium">
                              <td colSpan={5} className="p-2 text-right">{t('Total')}</td>
                              <td className="p-2 text-right">
                                {glassCuttingList.reduce((sum, item) => sum + (item.error ? 0 : item.totalQuantity), 0)}
                              </td>
                              <td className="p-2 text-right">
                                {glassCuttingList.reduce((sum, item) => sum + (item.error ? 0 : item.totalAreaM2), 0).toFixed(2)}
                              </td>
                              <td className="p-2"></td> {/* Empty cell for actions column */}
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Window Design Dialog */}
      <Dialog open={showCreateDesignDialog} onOpenChange={setShowCreateDesignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('Create Window Design')}</DialogTitle>
            <DialogDescription>{t('Create a new window design template')}</DialogDescription>
          </DialogHeader>
          <Form {...designForm}>
            <form onSubmit={designForm.handleSubmit(handleCreateDesign)} className="space-y-6">
              <FormField
                control={designForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Name')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('e.g. Standard Window')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={designForm.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Category')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('Select a category')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {WINDOW_CATEGORIES.map(category => (
                          <SelectItem key={category} value={category}>
                            {t(category)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={designForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Description')}</FormLabel>
                    <FormControl>
                      <Textarea placeholder={t('Brief description of the window design')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="space-y-4">
                <FormLabel>{t('Window Design Image')}</FormLabel>
                
                <div className="flex items-center justify-center w-full">
                  <label
                    htmlFor="window-image-upload"
                    className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-lg cursor-pointer bg-muted/30 hover:bg-muted/50"
                  >
                    {windowImagePreview ? (
                      <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
                        <img 
                          src={windowImagePreview}
                          alt={t('Window design preview')}
                          className="object-contain max-h-full max-w-full"
                        />
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg className="w-8 h-8 mb-4 text-muted-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <p className="mb-2 text-sm text-muted-foreground">{t('Drag and drop or click to upload')}</p>
                        <p className="text-xs text-muted-foreground">{t('PNG, JPG or SVG (max. 5MB)')}</p>
                      </div>
                    )}
                    <input
                      id="window-image-upload"
                      type="file"
                      className="hidden"
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleImageUpload}
                    />
                  </label>
                </div>
                
                {windowImagePreview && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setWindowImagePreview(null);
                      designForm.setValue('imageUrl', '');
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                  >
                    {t('Remove Image')}
                  </Button>
                )}
              </div>
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setShowCreateDesignDialog(false)}>
                  {t('Cancel')}
                </Button>
                <Button type="submit" disabled={createDesignMutation.isPending}>
                  {createDesignMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      {t('Creating...')}
                    </div>
                  ) : t('Create Design')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Window Design Dialog */}
      <Dialog open={showEditDesignDialog} onOpenChange={setShowEditDesignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('Edit Window Design')}</DialogTitle>
            <DialogDescription>{t('Update window design details')}</DialogDescription>
          </DialogHeader>
          <Form {...designForm}>
            <form onSubmit={designForm.handleSubmit(handleUpdateDesign)} className="space-y-6">
              <FormField
                control={designForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Name')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('e.g. Standard Window')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={designForm.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Category')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('Select a category')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {WINDOW_CATEGORIES.map(category => (
                          <SelectItem key={category} value={category}>
                            {t(category)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={designForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Description')}</FormLabel>
                    <FormControl>
                      <Textarea placeholder={t('Brief description of the window design')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="space-y-4">
                <FormLabel>{t('Window Design Image')}</FormLabel>
                
                <div className="flex items-center justify-center w-full">
                  <label
                    htmlFor="window-image-upload-edit"
                    className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-lg cursor-pointer bg-muted/30 hover:bg-muted/50"
                  >
                    {windowImagePreview ? (
                      <img 
                        src={windowImagePreview} 
                        alt={t('Window design preview')} 
                        className="h-full object-contain"
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-3 text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M21 14h-3a2 2 0 0 1-2-2V6"/><circle cx="12" cy="11" r="2"/><path d="m9 18 3-3 3 3"/></svg>
                        <p className="mb-2 text-sm text-muted-foreground">{t('Click to upload image')}</p>
                      </div>
                    )}
                    <input 
                      id="window-image-upload-edit" 
                      type="file" 
                      className="hidden" 
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleImageUpload}
                    />
                  </label>
                </div>
                
                {windowImagePreview && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setWindowImagePreview(null);
                      designForm.setValue('imageUrl', '');
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }}
                  >
                    {t('Remove Image')}
                  </Button>
                )}
              </div>
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setShowEditDesignDialog(false)}>
                  {t('Cancel')}
                </Button>
                <Button type="submit" disabled={updateDesignMutation.isPending}>
                  {updateDesignMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      {t('Updating...')}
                    </div>
                  ) : t('Update Design')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Add/Edit Component Dialog */}
      <Dialog open={showAddComponentDialog} onOpenChange={setShowAddComponentDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingComponent ? t('Edit Component') : t('Add Component')}</DialogTitle>
            <DialogDescription>
              {editingComponent 
                ? t('Edit component properties for the window design') 
                : t('Add a component to the window design')
              }
            </DialogDescription>
          </DialogHeader>
          <Form {...componentForm}>
            <form onSubmit={componentForm.handleSubmit(handleAddComponent)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={componentForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Name')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('e.g. Frame Top')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={componentForm.control}
                  name="componentType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Component Type')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('Select a type')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {COMPONENT_TYPES.map(type => (
                            <SelectItem key={type} value={type}>
                              {t(type)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={componentForm.control}
                name="profileId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Profile')}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))} 
                      value={field.value ? field.value.toString() : undefined}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('Select a profile')}>
                            {field.value && (() => {
                              const selectedProfile = profiles.find((p: any) => p.id === field.value);
                              if (selectedProfile) {
                                return (
                                  <div className="flex items-center gap-2">
                                    {selectedProfile.imageUrl || selectedProfile.thumbnailUrl ? (
                                      <div className="h-6 w-6 flex-shrink-0 rounded overflow-hidden border border-border">
                                        <img
                                          src={selectedProfile.thumbnailUrl || selectedProfile.imageUrl}
                                          alt={selectedProfile.name}
                                          className="h-full w-full object-cover"
                                          onError={(e) => {
                                            // Hide broken images
                                            e.currentTarget.style.display = 'none';
                                          }}
                                        />
                                      </div>
                                    ) : (
                                      <div className="h-6 w-6 flex-shrink-0 rounded bg-muted/50 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M3 15v4h4"/><path d="M21 9V5h-4"/></svg>
                                      </div>
                                    )}
                                    <span>{selectedProfile.name}</span>
                                  </div>
                                );
                              }
                              return null;
                            })()}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-80">
                        {profiles.map((profile: any) => (
                          <SelectItem key={profile.id} value={profile.id.toString()} className="flex items-center py-2">
                            <div className="flex items-center gap-2">
                              {profile.imageUrl || profile.thumbnailUrl ? (
                                <div className="relative group">
                                  <div className="h-8 w-8 flex-shrink-0 rounded overflow-hidden border border-border cursor-pointer">
                                    <img
                                      src={profile.thumbnailUrl || profile.imageUrl}
                                      alt={profile.name}
                                      className="h-full w-full object-cover"
                                      onError={(e) => {
                                        // Hide broken images
                                        e.currentTarget.style.display = 'none';
                                      }}
                                    />
                                  </div>
                                  {/* Enlarged image on hover */}
                                  <div className="hidden group-hover:block absolute z-50 left-0 top-0 transform -translate-y-1/2 shadow-lg rounded-md border border-border bg-background p-1">
                                    <div className="h-40 w-40 overflow-hidden rounded">
                                      <img
                                        src={profile.imageUrl || profile.thumbnailUrl}
                                        alt={profile.name}
                                        className="h-full w-full object-contain"
                                        onError={(e) => {
                                          // Hide broken images
                                          e.currentTarget.style.display = 'none';
                                        }}
                                      />
                                    </div>
                                    <div className="text-xs text-center mt-1 font-medium">{profile.name}</div>
                                  </div>
                                </div>
                              ) : (
                                <div className="h-8 w-8 flex-shrink-0 rounded bg-muted/50 flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M3 15v4h4"/><path d="M21 9V5h-4"/></svg>
                                </div>
                              )}
                              <div>
                                <div>{profile.name}</div>
                                <div className="text-xs text-muted-foreground">{profile.category}</div>
                                {profile.profileCode && (
                                  <div className="text-xs text-muted-foreground">{profile.profileCode}</div>
                                )}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>{t('The aluminum profile to use for this component')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="space-y-4 border p-4 rounded-md">
                <h3 className="font-medium">{t('Formula Definitions')}</h3>
                <p className="text-sm text-muted-foreground">
                  {t('Define formulas using width, height as variables. Example: width - 20')}
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={componentForm.control}
                    name="widthFormula"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('Width Formula (optional)')}</FormLabel>
                        <FormControl>
                          <Input placeholder="width - 10" {...field} />
                        </FormControl>
                        <FormDescription>{t('Length calculation for width profiles')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={componentForm.control}
                    name="widthQuantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('Width Quantity')}</FormLabel>
                        <FormControl>
                          <Input placeholder="1" {...field} />
                        </FormControl>
                        <FormDescription>{t('Number of pieces for width profile')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={componentForm.control}
                    name="isFixedHeight"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>{t('Height Calculation Method')}</FormLabel>
                        <FormControl>
                          <RadioGroup 
                            onValueChange={(value) => {
                              field.onChange(value === "fixed");
                              if (value === "formula") {
                                componentForm.setValue("fixedHeightValue", null);
                              }
                            }}
                            value={field.value ? "fixed" : "formula"}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="formula" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {t('Formula-based (adjusts with window height)')}
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="fixed" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {t('Fixed size (maintains constant height)')}
                              </FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormDescription>
                          {t('Choose fixed size for bottom components that maintain their height regardless of window size')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {componentForm.watch("isFixedHeight") ? (
                    <FormField
                      control={componentForm.control}
                      name="fixedHeightValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('Fixed Height Formula')}</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="300" 
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('Enter a fixed value (e.g., "300") or a formula (e.g., "500 - 20"). This height will remain constant regardless of window dimensions.')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={componentForm.control}
                        name="heightFormula"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('Height Formula (optional)')}</FormLabel>
                            <FormControl>
                              <Input placeholder="height - 10" {...field} />
                            </FormControl>
                            <FormDescription>{t('Length calculation for height profiles')}</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={componentForm.control}
                        name="heightQuantity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('Height Quantity')}</FormLabel>
                            <FormControl>
                              <Input placeholder="1" {...field} />
                            </FormControl>
                            <FormDescription>{t('Number of pieces for height profile')}</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
                
                <FormField
                  control={componentForm.control}
                  name="quantityFormula"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Quantity Formula')}</FormLabel>
                      <FormControl>
                        <Input placeholder="1" {...field} />
                      </FormControl>
                      <FormDescription>{t('Can use mathematical expressions. Example: 2 * (width > 1000 ? 2 : 1)')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="border p-4 rounded-md space-y-4">
                <h3 className="font-medium">{t('Cutting Angles')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={componentForm.control}
                    name="leftCutDegree"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>{t('Left Side Cut')}</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            defaultValue={field.value ? field.value.toString() : "90"}
                            className="flex space-x-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="90" id="left-90" />
                              <Label htmlFor="left-90">90°</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="45" id="left-45" />
                              <Label htmlFor="left-45">45°</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormDescription>{t('Cut angle for the left side of the component')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={componentForm.control}
                    name="rightCutDegree"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>{t('Right Side Cut')}</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            defaultValue={field.value ? field.value.toString() : "90"}
                            className="flex space-x-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="90" id="right-90" />
                              <Label htmlFor="right-90">90°</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="45" id="right-45" />
                              <Label htmlFor="right-45">45°</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormDescription>{t('Cut angle for the right side of the component')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setShowAddComponentDialog(false)}>
                  {t('Cancel')}
                </Button>
                <Button 
                  type="submit" 
                  disabled={createComponentMutation.isPending || updateComponentMutation.isPending}
                >
                  {createComponentMutation.isPending || updateComponentMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      {editingComponent ? t('Updating...') : t('Adding...')}
                    </div>
                  ) : editingComponent ? t('Update Component') : t('Add Component')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Add/Edit Glass Dialog */}
      <Dialog open={showAddGlassDialog} onOpenChange={setShowAddGlassDialog}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingGlass ? t('Edit Glass Specification') : t('Add Glass Specification')}
            </DialogTitle>
            <DialogDescription>
              {editingGlass 
                ? t('Edit glass specification properties for the window design')
                : t('Add glass specification to the window design')
              }
            </DialogDescription>
          </DialogHeader>
          <Form {...glassForm}>
            <form onSubmit={glassForm.handleSubmit(handleAddGlass)} className="space-y-6">
              <FormField
                control={glassForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Name')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('e.g. Main Glass')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={glassForm.control}
                  name="glassType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Glass Type')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('Select a type')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {GLASS_TYPES.map(type => (
                            <SelectItem key={type} value={type}>
                              {t(type)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={glassForm.control}
                  name="thickness"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Thickness (mm)')}</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="space-y-4 border p-4 rounded-md">
                <h3 className="font-medium">{t('Formula Definitions')}</h3>
                <p className="text-sm text-muted-foreground">
                  {t('Define formulas using width, height as variables. Example: width - 20')}
                </p>
                
                <FormField
                  control={glassForm.control}
                  name="widthFormula"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Width Formula')}</FormLabel>
                      <FormControl>
                        <Input placeholder="width - 40" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={glassForm.control}
                  name="heightFormula"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Height Formula')}</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="height - 40" 
                          {...field} 
                          disabled={glassForm.watch('isFixedHeight')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="space-y-4">
                  <FormField
                    control={glassForm.control}
                    name="isFixedHeight"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>{t('Fixed Height')}</FormLabel>
                          <FormDescription>
                            {t('Use a fixed height value regardless of window height')}
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {glassForm.watch('isFixedHeight') && (
                    <FormField
                      control={glassForm.control}
                      name="fixedHeightValue"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('Fixed Height Value/Formula')}</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="500 or width/2" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            {t('Enter a fixed value or a formula (e.g. "500 - 20" or "width/2")')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
                
                <FormField
                  control={glassForm.control}
                  name="quantityFormula"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('Quantity Formula')}</FormLabel>
                      <FormControl>
                        <Input placeholder="1" {...field} />
                      </FormControl>
                      <FormDescription>{t('Can use mathematical expressions. Example: 2 * (width > 1000 ? 2 : 1)')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setShowAddGlassDialog(false)}>
                  {t('Cancel')}
                </Button>
                <Button 
                  type="submit" 
                  disabled={createGlassMutation.isPending || updateGlassMutation.isPending}
                >
                  {createGlassMutation.isPending || updateGlassMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                      {editingGlass ? t('Updating...') : t('Adding...')}
                    </div>
                  ) : editingGlass ? t('Update Glass Specification') : t('Add Glass Specification')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}