import dotenv from "dotenv";
dotenv.config();

// Check if we're using local PostgreSQL or Neon
const isLocalDB = process.env.PGHOST === 'localhost' || process.env.DATABASE_URL?.includes('localhost');

if (isLocalDB) {
  console.log('Using local PostgreSQL database');
} else {
  console.log('Using Neon serverless database');
}

// Import the appropriate modules based on database type
let Pool, drizzle;
let schema;

if (isLocalDB) {
  // Use standard PostgreSQL for local development
  const { Pool: PgPool } = require('pg');
  const { drizzle: pgDrizzle } = require('drizzle-orm/node-postgres');
  Pool = PgPool;
  drizzle = pgDrizzle;
  schema = require("@shared/schema");
} else {
  // Use Neon for remote database
  const { Pool: NeonPool, neonConfig } = require('@neondatabase/serverless');
  const { drizzle: neonDrizzle } = require('drizzle-orm/neon-serverless');
  const ws = require("ws");

  Pool = NeonPool;
  drizzle = neonDrizzle;
  schema = require("@shared/schema");

  neonConfig.webSocketConstructor = ws;
}

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

// Configure connection pool with better settings for reliability
export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 10, // Maximum number of connections in the pool
  idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
  connectionTimeoutMillis: 10000, // Timeout for new connections after 10 seconds
});

// Add error handling for the pool
pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
});

// Create database instance based on the database type
export const db = isLocalDB
  ? drizzle(pool, { schema })
  : drizzle({ client: pool, schema });

// Helper function to retry database operations
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, error);

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}
