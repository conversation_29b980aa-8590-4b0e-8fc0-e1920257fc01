import dotenv from "dotenv";
dotenv.config();

// Check if we're using SQLite, local PostgreSQL or Neon
const isSQLite = process.env.DATABASE_URL?.startsWith('file:');
const isLocalDB = process.env.PGHOST === 'localhost' || process.env.DATABASE_URL?.includes('localhost');

if (isSQLite) {
  console.log('Using SQLite database');

  // Use SQLite for local development
  const { drizzle: sqliteDrizzle } = await import('drizzle-orm/better-sqlite3');
  const Database = (await import('better-sqlite3')).default;
  const schema = await import("../shared/schema.js");

  const sqlite = new Database('./local_database.db');

  // Create SQLite database instance
  export const db = sqliteDrizzle(sqlite, { schema });
  export const pool = null; // SQLite doesn't use connection pools

  // Helper function to retry database operations
  export async function withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, error);
        if (attempt === maxRetries) break;
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    throw lastError;
  }

} else if (isLocalDB) {
  console.log('Using local PostgreSQL database');

  // Use standard PostgreSQL for local development
  const { Pool: PgPool } = await import('pg');
  const { drizzle: pgDrizzle } = await import('drizzle-orm/node-postgres');
  const schema = await import("../shared/schema.js");

  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL must be set. Did you forget to provision a database?");
  }

  // Configure connection pool with better settings for reliability
  export const pool = new PgPool({
    connectionString: process.env.DATABASE_URL,
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
  });

  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
  });

  export const db = pgDrizzle(pool, { schema });

  // Helper function to retry database operations
  export async function withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, error);
        if (attempt === maxRetries) break;
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    throw lastError;
  }

} else {
  console.log('Using Neon serverless database');

  // Use Neon for remote database
  const { Pool: NeonPool, neonConfig } = await import('@neondatabase/serverless');
  const { drizzle: neonDrizzle } = await import('drizzle-orm/neon-serverless');
  const ws = await import("ws");
  const schema = await import("../shared/schema.js");

  neonConfig.webSocketConstructor = ws.default;

  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL must be set. Did you forget to provision a database?");
  }

  // Configure connection pool with better settings for reliability
  export const pool = new NeonPool({
    connectionString: process.env.DATABASE_URL,
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
  });

  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
  });

  export const db = neonDrizzle({ client: pool, schema });

  // Helper function to retry database operations
  export async function withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, error);
        if (attempt === maxRetries) break;
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    throw lastError;
  }
}

