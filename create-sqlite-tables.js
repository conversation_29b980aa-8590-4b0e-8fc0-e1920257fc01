import Database from 'better-sqlite3';

const db = new Database('./local_database.db');

console.log('Creating SQLite tables...');

// Create tables based on the schema
const createTables = [
  `CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT,
    email TEXT,
    role TEXT DEFAULT 'user',
    stripe_customer_id TEXT,
    subscription_plan TEXT DEFAULT 'free',
    subscription_status TEXT DEFAULT 'active',
    subscription_end_date TEXT,
    payment_issue_date TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
  )`,
  
  `CREATE TABLE IF NOT EXISTS profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    stock_length INTEGER DEFAULT 6000,
    image_url TEXT,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS window_designs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS window_design_components (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    window_design_id INTEGER NOT NULL,
    profile_id INTEGER,
    name TEXT NOT NULL,
    component_type TEXT NOT NULL,
    width_formula TEXT,
    width_quantity INTEGER DEFAULT 1,
    is_fixed_height INTEGER DEFAULT 0,
    fixed_height_value TEXT,
    height_quantity INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (window_design_id) REFERENCES window_designs(id),
    FOREIGN KEY (profile_id) REFERENCES profiles(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS window_design_glass_specifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    window_design_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    width_formula TEXT,
    height_formula TEXT,
    quantity_formula TEXT DEFAULT '1',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (window_design_id) REFERENCES window_designs(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS window_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS project_window_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    window_design_id INTEGER,
    window_type TEXT,
    width REAL NOT NULL,
    height REAL NOT NULL,
    quantity INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES window_projects(id),
    FOREIGN KEY (window_design_id) REFERENCES window_designs(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS accessories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    unit_price REAL,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    amount REAL NOT NULL,
    status TEXT DEFAULT 'pending',
    stripe_payment_intent_id TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`,
  
  `CREATE TABLE IF NOT EXISTS subscription_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    features TEXT,
    max_quantity INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
  )`
];

try {
  // Create tables
  for (const sql of createTables) {
    db.exec(sql);
  }
  
  console.log('✅ All tables created successfully');
  
  // Check tables
  const tables = db.prepare('SELECT name FROM sqlite_master WHERE type=?').all('table');
  console.log('📊 Created tables:', tables.map(t => t.name));
  
} catch (error) {
  console.error('❌ Error creating tables:', error);
} finally {
  db.close();
}
