import { AlertCircle, CreditCard, BanIcon } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useSafeLocation } from "@/hooks/use-safe-location";

interface AccountStatusAlertProps {
  status: 'active' | 'suspended' | 'pending_payment';
  message?: string;
}

export function AccountStatusAlert({ status, message }: AccountStatusAlertProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [_, setLocation] = useSafeLocation();
  
  if (status === 'active') return null;
  
  if (status === 'suspended') {
    return (
      <Alert variant="destructive" className="mb-4">
        <BanIcon className="h-4 w-4" />
        <AlertTitle>Account Suspended</AlertTitle>
        <AlertDescription className="flex flex-col gap-2">
          <p>
            Your account has been suspended due to a payment issue or policy violation.
            {message && ` ${message}`}
          </p>
          <Button 
            variant="destructive" 
            className="mt-2 w-fit"
            onClick={() => setLocation("/subscription")}
          >
            <CreditCard className="mr-2 h-4 w-4" /> Update Payment
          </Button>
        </AlertDescription>
      </Alert>
    );
  }
  
  if (status === 'pending_payment') {
    return (
      <Alert className="mb-4 border-amber-500 bg-amber-50">
        <AlertCircle className="h-4 w-4 text-amber-600" />
        <AlertTitle className="text-amber-600">Payment Required</AlertTitle>
        <AlertDescription className="flex flex-col gap-2">
          <p>
            Your subscription payment is overdue. Some Pro features may be restricted 
            if payment is not received soon.
            {message && ` ${message}`}
          </p>
          
          <div className="flex gap-2 mt-2">
            <Button 
              variant="outline" 
              className="border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Show Less' : 'Learn More'}
            </Button>
            
            <Button 
              variant="default" 
              className="bg-amber-600 hover:bg-amber-700"
              onClick={() => setLocation("/subscription")}
            >
              <CreditCard className="mr-2 h-4 w-4" /> Update Payment
            </Button>
          </div>
          
          {isExpanded && (
            <Card className="mt-4 border-amber-200">
              <CardContent className="pt-4 text-sm">
                <p className="mb-2">
                  To maintain uninterrupted access to Pro features, please update your payment method.
                  If you continue to have payment issues, your account may be suspended.
                </p>
                <p>
                  If you believe this is an error, please contact our support team.
                </p>
              </CardContent>
            </Card>
          )}
        </AlertDescription>
      </Alert>
    );
  }
  
  return null;
}