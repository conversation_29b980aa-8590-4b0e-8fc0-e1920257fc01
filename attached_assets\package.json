{"name": "aluminum-optimization", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "miragejs": "^0.1.48", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.0", "react-spreadsheet": "^0.9.5", "typescript": "^5.2.2", "uuid": "^11.1.0", "vite": "^5.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5"}}