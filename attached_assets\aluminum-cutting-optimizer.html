<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aluminum Cutting Optimizer</title>
    <style>
        :root {
            font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
            line-height: 1.5;
            font-weight: 400;
            color: #213547;
            background-color: #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .app-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #1a73e8;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #5f6368;
            font-size: 1.2rem;
        }

        main {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .optimization-form-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .optimization-form-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .optimization-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            text-align: left;
            flex: 1;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #202124;
        }

        input, select, textarea {
            padding: 0.75rem;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
        }

        .error {
            color: #d93025;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        button {
            background-color: #1a73e8;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #1765cc;
        }

        .submit-button {
            background-color: #1a73e8;
            flex: 1;
        }

        .save-button {
            background-color: #34a853;
            flex: 1;
        }

        .save-button:hover {
            background-color: #2d9249;
        }

        .load-button {
            background-color: #fbbc05;
            color: #202124;
            flex: 1;
        }

        .load-button:hover {
            background-color: #f9ab00;
        }

        .add-button {
            background-color: #34a853;
        }

        .add-button:hover {
            background-color: #2d9249;
        }

        .remove-button {
            background-color: #ea4335;
            padding: 0.75rem;
        }

        .remove-button:hover {
            background-color: #d33426;
        }

        .cut-list-container {
            margin-top: 1rem;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 1rem;
            overflow-x: auto;
        }

        /* Excel-like table styles */
        .excel-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            border: 1px solid #e0e0e0;
        }

        .excel-table th {
            background-color: #f3f3f3;
            border: 1px solid #e0e0e0;
            padding: 8px 12px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .excel-table td {
            border: 1px solid #e0e0e0;
            padding: 0;
        }

        .excel-table input {
            width: 100%;
            border: none;
            padding: 8px 12px;
            box-sizing: border-box;
            text-align: right;
        }

        .excel-table input:focus {
            outline: 2px solid #1a73e8;
            outline-offset: -2px;
        }

        .excel-table .row-number {
            background-color: #f3f3f3;
            text-align: center;
            font-weight: 500;
            width: 40px;
            padding: 8px 0;
        }

        .excel-table .action-cell {
            width: 100px;
            text-align: center;
            padding: 4px;
        }

        .excel-table tr:hover {
            background-color: #f9f9f9;
        }

        .excel-table tr:hover .row-number {
            background-color: #e9e9e9;
        }

        .excel-controls {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            justify-content: space-between;
        }

        .excel-controls button {
            padding: 6px 12px;
            font-size: 0.9rem;
        }

        .excel-controls .left-controls {
            display: flex;
            gap: 0.5rem;
        }

        .excel-controls .right-controls {
            display: flex;
            gap: 0.5rem;
        }

        .excel-table .selected {
            background-color: #e8f0fe;
        }

        .results-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: left;
            display: none;
        }

        .results-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .summary-card h3 {
            margin-top: 0;
            color: #5f6368;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: 500;
            color: #1a73e8;
            margin: 0;
        }

        .cutting-plans {
            margin-top: 2rem;
        }

        .cutting-plan {
            margin-bottom: 2rem;
        }

        .cutting-plan-title {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .bar-visualization {
            height: 40px;
            background-color: #e8eaed;
            border-radius: 4px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .cut-piece {
            position: absolute;
            height: 100%;
            background-color: #1a73e8;
            border-right: 2px dashed white;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .scrap-piece {
            position: absolute;
            height: 100%;
            background-color: #ea4335;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .cut-list-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .cut-list-table th, .cut-list-table td {
            border: 1px solid #dadce0;
            padding: 0.75rem;
            text-align: left;
        }

        .cut-list-table th {
            background-color: #f1f3f4;
            font-weight: 500;
        }

        .print-button {
            background-color: #5f6368;
            margin-top: 1rem;
        }

        .print-button:hover {
            background-color: #494c50;
        }

        footer {
            margin-top: 2rem;
            text-align: center;
            font-size: 0.8rem;
            color: #888;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                align-items: stretch;
            }

            .results-summary {
                grid-template-columns: 1fr;
            }
        }

        /* Authentication and User Management Styles */
        .auth-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .auth-modal {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            width: 400px;
            max-width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .auth-modal h2 {
            margin-top: 0;
            color: #1a73e8;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .auth-form input {
            padding: 0.75rem;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 1rem;
        }

        .auth-form button {
            margin-top: 1rem;
        }

        .auth-links {
            margin-top: 1rem;
            text-align: center;
            font-size: 0.9rem;
        }

        .auth-links a {
            color: #1a73e8;
            text-decoration: none;
            cursor: pointer;
        }

        .auth-links a:hover {
            text-decoration: underline;
        }

        /* User Menu Styles */
        .user-menu {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #1a73e8;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .user-dropdown {
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            min-width: 200px;
            display: none;
            z-index: 100;
        }

        .user-dropdown.active {
            display: block;
        }

        .user-dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
        }

        .user-dropdown-item:hover {
            background-color: #f1f3f4;
        }

        .user-dropdown-divider {
            height: 1px;
            background-color: #dadce0;
            margin: 0.5rem 0;
        }

        .user-info {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dadce0;
        }

        .user-name {
            font-weight: 500;
        }

        .user-email {
            font-size: 0.8rem;
            color: #5f6368;
        }

        .user-plan {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: #e8f0fe;
            color: #1a73e8;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        /* Subscription Plan Styles */
        .subscription-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .subscription-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .plan-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .plan-card {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .plan-card.active {
            border: 2px solid #1a73e8;
        }

        .plan-card.active::before {
            content: '✓';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 30px;
            height: 30px;
            background-color: #1a73e8;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #202124;
        }

        .plan-price {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1a73e8;
        }

        .plan-price span {
            font-size: 1rem;
            font-weight: 400;
            color: #5f6368;
        }

        .plan-features {
            list-style-type: none;
            padding: 0;
            margin: 0 0 2rem 0;
            flex-grow: 1;
        }

        .plan-features li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .plan-features li::before {
            content: '✓';
            color: #34a853;
            font-weight: bold;
        }

        .plan-features li.disabled {
            color: #9aa0a6;
        }

        .plan-features li.disabled::before {
            content: '×';
            color: #ea4335;
        }

        .plan-button {
            margin-top: auto;
        }

        /* Admin Panel Styles */
        .admin-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .admin-container h2 {
            margin-top: 0;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .admin-tabs {
            display: flex;
            border-bottom: 1px solid #dadce0;
            margin-bottom: 1.5rem;
        }

        .admin-tab {
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
        }

        .admin-tab.active {
            border-bottom-color: #1a73e8;
            color: #1a73e8;
            font-weight: 500;
        }

        .admin-tab-content {
            display: none;
        }

        .admin-tab-content.active {
            display: block;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th, .admin-table td {
            border: 1px solid #dadce0;
            padding: 0.75rem;
            text-align: left;
        }

        .admin-table th {
            background-color: #f1f3f4;
            font-weight: 500;
        }

        .admin-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .admin-table tr:hover {
            background-color: #e8f0fe;
        }

        .admin-actions {
            display: flex;
            gap: 0.5rem;
        }

        .admin-actions button {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .admin-stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .admin-stat-value {
            font-size: 2rem;
            font-weight: 500;
            color: #1a73e8;
            margin: 0.5rem 0;
        }

        .admin-stat-label {
            color: #5f6368;
            font-size: 0.9rem;
        }

        /* Change Password Modal */
        .change-password-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Print Styles */
        @media print {
            .optimization-form-container, .header p, footer, .print-button, .user-menu, .auth-container {
                display: none;
            }

            .app-container {
                padding: 0;
            }

            .results-container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header class="header">
            <h1>Aluminum Cutting Optimizer</h1>
            <p>Optimize your aluminum cutting to minimize waste and maximize efficiency</p>

            <!-- User Menu (visible when logged in) -->
            <div id="userMenu" class="user-menu" style="display: none;">
                <div class="user-avatar" id="userAvatar" onclick="toggleUserDropdown()">JD</div>
                <div class="user-dropdown" id="userDropdown">
                    <div class="user-info">
                        <div class="user-name" id="userNameDisplay">John Doe</div>
                        <div class="user-email" id="userEmailDisplay"><EMAIL></div>
                        <div class="user-plan" id="userPlanDisplay">Free Plan</div>
                    </div>
                    <div class="user-dropdown-item" onclick="showSubscriptionPlans()">Subscription Plans</div>
                    <div class="user-dropdown-item" onclick="showChangePasswordModal()">Change Password</div>
                    <div class="user-dropdown-divider"></div>
                    <div class="user-dropdown-item" id="adminPanelLink" onclick="showAdminPanel()" style="display: none;">Admin Panel</div>
                    <div class="user-dropdown-item" onclick="logout()">Logout</div>
                </div>
            </div>

            <!-- Login/Register Button (visible when not logged in) -->
            <div id="authButtons" class="user-menu">
                <button onclick="showLoginModal()">Login</button>
                <button onclick="showRegisterModal()">Register</button>
            </div>
        </header>

        <main>
            <div class="optimization-form-container">
                <h2>Enter Cutting Requirements</h2>
                <form id="optimizationForm" class="optimization-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="barLength">Standard Bar Length (mm)</label>
                            <input type="number" id="barLength" name="barLength" value="6000" step="1" min="1" required>
                        </div>

                        <div class="form-group">
                            <label for="kerf">Kerf / Saw Width (mm)</label>
                            <input type="number" id="kerf" name="kerf" value="3" step="0.1" min="0.1" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Cut List</label>
                        <div class="cut-list-container">
                            <table id="excelTable" class="excel-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Length (mm)</th>
                                        <th>Quantity</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="excelTableBody">
                                    <tr>
                                        <td class="row-number">1</td>
                                        <td><input type="number" class="cut-length" value="1000" step="1" min="1" required></td>
                                        <td><input type="number" class="cut-quantity" value="5" step="1" min="1" required></td>
                                        <td><input type="text" class="cut-description" placeholder="Optional description"></td>
                                        <td class="action-cell">
                                            <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="row-number">2</td>
                                        <td><input type="number" class="cut-length" value="1500" step="1" min="1" required></td>
                                        <td><input type="number" class="cut-quantity" value="3" step="1" min="1" required></td>
                                        <td><input type="text" class="cut-description" placeholder="Optional description"></td>
                                        <td class="action-cell">
                                            <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="row-number">3</td>
                                        <td><input type="number" class="cut-length" value="2000" step="1" min="1" required></td>
                                        <td><input type="number" class="cut-quantity" value="2" step="1" min="1" required></td>
                                        <td><input type="text" class="cut-description" placeholder="Optional description"></td>
                                        <td class="action-cell">
                                            <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="excel-controls">
                                <div class="left-controls">
                                    <button type="button" class="add-button" onclick="addExcelRow()">Add Row</button>
                                    <button type="button" class="add-button" onclick="addMultipleRows()">Add Multiple Rows</button>
                                </div>
                                <div class="right-controls">
                                    <button type="button" onclick="clearExcelTable()">Clear All</button>
                                    <button type="button" onclick="importFromCsv()">Import CSV</button>
                                    <button type="button" onclick="exportToCsv()">Export CSV</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <button type="submit" class="submit-button">Calculate Optimal Cutting Plan</button>
                        <button type="button" class="save-button" onclick="saveCutList()">Save Cut List</button>
                        <button type="button" class="load-button" onclick="loadCutList()">Load Cut List</button>
                    </div>
                </form>
            </div>

            <div id="resultsContainer" class="results-container">
                <h2>Cutting Optimization Results</h2>

                <div class="results-summary">
                    <div class="summary-card">
                        <h3>Total Bars Needed</h3>
                        <p id="totalBars" class="summary-value">3</p>
                    </div>

                    <div class="summary-card">
                        <h3>Material Utilization</h3>
                        <p id="materialUtilization" class="summary-value">85%</p>
                    </div>

                    <div class="summary-card">
                        <h3>Total Waste</h3>
                        <p id="totalWaste" class="summary-value">2700 mm</p>
                    </div>

                    <div class="summary-card">
                        <h3>Waste Percentage</h3>
                        <p id="wastePercentage" class="summary-value">15%</p>
                    </div>
                </div>

                <div class="cut-list-summary">
                    <h3>Cut List Summary</h3>
                    <table class="cut-list-table">
                        <thead>
                            <tr>
                                <th>Length (mm)</th>
                                <th>Quantity</th>
                                <th>Total Length (mm)</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody id="cutListSummary">
                            <!-- Cut list summary will be added here -->
                        </tbody>
                    </table>
                </div>

                <div class="cutting-plans">
                    <h3>Cutting Plans</h3>
                    <div id="cuttingPlans">
                        <!-- Cutting plans will be added here -->
                    </div>
                </div>

                <div class="form-row">
                    <button type="button" class="print-button" onclick="window.print()">Print Cutting Plan</button>
                    <button type="button" class="save-button" onclick="saveResults()">Save Results</button>
                </div>
            </div>
        </main>

        <footer>
            <p>© <span id="currentYear"></span> Aluminum Cutting Optimizer</p>
        </footer>
    </div>

    <script>
        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Function to add a new Excel-like row
        function addExcelRow() {
            const tableBody = document.getElementById('excelTableBody');
            const rowCount = tableBody.rows.length;
            const newRow = document.createElement('tr');

            newRow.innerHTML = `
                <td class="row-number">${rowCount + 1}</td>
                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>
                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>
                <td><input type="text" class="cut-description" placeholder="Optional description"></td>
                <td class="action-cell">
                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                </td>
            `;

            tableBody.appendChild(newRow);

            // Focus on the length input of the new row
            const lengthInput = newRow.querySelector('.cut-length');
            lengthInput.focus();

            // Update row numbers
            updateRowNumbers();

            // Add keyboard navigation
            setupKeyboardNavigation(newRow);
        }

        // Function to add multiple rows at once
        function addMultipleRows() {
            const count = prompt('How many rows would you like to add?', '5');
            const numRows = parseInt(count);

            if (!isNaN(numRows) && numRows > 0) {
                for (let i = 0; i < numRows; i++) {
                    addExcelRow();
                }
            }
        }

        // Function to remove an Excel row
        function removeExcelRow(button) {
            const tableBody = document.getElementById('excelTableBody');
            if (tableBody.rows.length > 1) {
                const row = button.closest('tr');
                row.remove();

                // Update row numbers
                updateRowNumbers();
            } else {
                alert('You must have at least one cut item.');
            }
        }

        // Function to update row numbers
        function updateRowNumbers() {
            const tableBody = document.getElementById('excelTableBody');
            const rows = tableBody.rows;

            for (let i = 0; i < rows.length; i++) {
                rows[i].cells[0].textContent = i + 1;
            }
        }

        // Function to clear the Excel table
        function clearExcelTable() {
            if (confirm('Are you sure you want to clear all rows?')) {
                const tableBody = document.getElementById('excelTableBody');
                tableBody.innerHTML = '';
                addExcelRow(); // Add one empty row
            }
        }

        // Function to export table data to CSV
        function exportToCsv() {
            const tableBody = document.getElementById('excelTableBody');
            const rows = tableBody.rows;
            let csvContent = 'Length,Quantity,Description\n';

            for (let i = 0; i < rows.length; i++) {
                const length = rows[i].querySelector('.cut-length').value || '';
                const quantity = rows[i].querySelector('.cut-quantity').value || '';
                const description = rows[i].querySelector('.cut-description').value || '';

                // Escape commas and quotes in the description
                const escapedDescription = description.replace(/"/g, '""');

                csvContent += `${length},${quantity},"${escapedDescription}"\n`;
            }

            // Create a download link
            const encodedUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'aluminum_cut_list.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Function to import data from CSV
        function importFromCsv() {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.csv';

            fileInput.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    const contents = e.target.result;
                    const lines = contents.split('\n');

                    // Check if there's a header row
                    let startRow = 0;
                    if (lines[0].toLowerCase().includes('length') &&
                        lines[0].toLowerCase().includes('quantity')) {
                        startRow = 1; // Skip header row
                    }

                    // Clear existing rows
                    clearExcelTable();
                    const tableBody = document.getElementById('excelTableBody');
                    tableBody.innerHTML = '';

                    // Parse CSV and add rows
                    for (let i = startRow; i < lines.length; i++) {
                        if (!lines[i].trim()) continue; // Skip empty lines

                        // Handle CSV parsing (including quoted fields with commas)
                        const values = parseCSVLine(lines[i]);

                        if (values.length >= 2) {
                            const length = values[0];
                            const quantity = values[1];
                            const description = values[2] || '';

                            const newRow = document.createElement('tr');
                            newRow.innerHTML = `
                                <td class="row-number">${i - startRow + 1}</td>
                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>
                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>
                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>
                                <td class="action-cell">
                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                                </td>
                            `;

                            tableBody.appendChild(newRow);
                            setupKeyboardNavigation(newRow);
                        }
                    }

                    // If no rows were added, add one empty row
                    if (tableBody.rows.length === 0) {
                        addExcelRow();
                    }

                    // Update row numbers
                    updateRowNumbers();

                    alert('CSV data imported successfully!');
                };
                reader.readAsText(file);
            };

            fileInput.click();
        }

        // Helper function to parse CSV line (handles quoted fields)
        function parseCSVLine(line) {
            const result = [];
            let startPos = 0;
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                if (line[i] === '"') {
                    inQuotes = !inQuotes;
                } else if (line[i] === ',' && !inQuotes) {
                    result.push(line.substring(startPos, i).replace(/^"|"$/g, '').replace(/""/g, '"'));
                    startPos = i + 1;
                }
            }

            // Add the last field
            result.push(line.substring(startPos).replace(/^"|"$/g, '').replace(/""/g, '"'));

            return result;
        }

        // Setup keyboard navigation for Excel-like experience
        function setupKeyboardNavigation(row) {
            const inputs = row.querySelectorAll('input');

            for (let i = 0; i < inputs.length; i++) {
                inputs[i].addEventListener('keydown', function(e) {
                    const currentRow = this.closest('tr');
                    const currentCell = this.closest('td');
                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);

                    // Tab key - move to next cell or next row
                    if (e.key === 'Tab' && !e.shiftKey) {
                        if (i === inputs.length - 1) {
                            // Last cell in row, add a new row if this is the last row
                            const tableBody = document.getElementById('excelTableBody');
                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {
                                e.preventDefault();
                                addExcelRow();
                            }
                        }
                    }

                    // Enter key - move to same cell in next row or add new row
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const tableBody = document.getElementById('excelTableBody');
                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);

                        if (rowIndex < tableBody.rows.length - 1) {
                            // Move to next row, same cell
                            const nextRow = tableBody.rows[rowIndex + 1];
                            const nextInput = nextRow.cells[cellIndex].querySelector('input');
                            if (nextInput) nextInput.focus();
                        } else {
                            // Add new row and focus on same cell
                            addExcelRow();
                        }
                    }

                    // Arrow down - move to cell below
                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        const tableBody = document.getElementById('excelTableBody');
                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);

                        if (rowIndex < tableBody.rows.length - 1) {
                            const nextRow = tableBody.rows[rowIndex + 1];
                            const nextInput = nextRow.cells[cellIndex].querySelector('input');
                            if (nextInput) nextInput.focus();
                        }
                    }

                    // Arrow up - move to cell above
                    if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        const tableBody = document.getElementById('excelTableBody');
                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);

                        if (rowIndex > 0) {
                            const prevRow = tableBody.rows[rowIndex - 1];
                            const prevInput = prevRow.cells[cellIndex].querySelector('input');
                            if (prevInput) prevInput.focus();
                        }
                    }
                });
            }
        }

        // Initialize keyboard navigation for existing rows
        document.addEventListener('DOMContentLoaded', function() {
            const tableBody = document.getElementById('excelTableBody');
            const rows = tableBody.rows;

            for (let i = 0; i < rows.length; i++) {
                setupKeyboardNavigation(rows[i]);
            }
        });

        // Form submission handler
        document.getElementById('optimizationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form values
            const barLength = parseFloat(document.getElementById('barLength').value);
            const kerf = parseFloat(document.getElementById('kerf').value);

            // Get cut list items from Excel table
            const cutItems = [];
            const tableBody = document.getElementById('excelTableBody');
            const rows = tableBody.rows;

            for (let i = 0; i < rows.length; i++) {
                const lengthInput = rows[i].querySelector('.cut-length');
                const quantityInput = rows[i].querySelector('.cut-quantity');
                const descriptionInput = rows[i].querySelector('.cut-description');

                const length = parseFloat(lengthInput.value);
                const quantity = parseInt(quantityInput.value);
                const description = descriptionInput.value;

                if (length && quantity) {
                    cutItems.push({
                        length,
                        quantity,
                        description
                    });
                }
            }

            // Validate inputs
            if (barLength <= 0) {
                alert('Bar length must be greater than 0.');
                return;
            }

            if (kerf <= 0) {
                alert('Kerf must be greater than 0.');
                return;
            }

            if (cutItems.length === 0) {
                alert('You must add at least one cut item.');
                return;
            }

            // Check if any cut length is greater than bar length
            for (const item of cutItems) {
                if (item.length > barLength) {
                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);
                    return;
                }
            }

            // Calculate optimization
            const result = calculateCuttingOptimization(barLength, kerf, cutItems);

            // Display results
            displayResults(result, barLength, cutItems);
        });

        // Function to calculate cutting optimization
        function calculateCuttingOptimization(barLength, kerf, cutItems) {
            // Create a flat array of all pieces needed
            const pieces = [];
            for (const item of cutItems) {
                for (let i = 0; i < item.quantity; i++) {
                    pieces.push({
                        length: item.length,
                        originalLength: item.length // Keep track of original length for display
                    });
                }
            }

            // Sort pieces in descending order (largest first)
            pieces.sort((a, b) => b.length - a.length);

            // Try different algorithms and pick the best result
            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);
            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);

            // Compare results and return the best one
            if (ffdResult.totalBars < bfdResult.totalBars ||
                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {
                return ffdResult;
            } else {
                return bfdResult;
            }
        }

        // First-Fit Decreasing algorithm
        function firstFitDecreasing(pieces, barLength, kerf) {
            // Make a deep copy of pieces to avoid modifying the original
            const piecesCopy = JSON.parse(JSON.stringify(pieces));

            // Initialize bars
            const bars = [];

            for (const piece of piecesCopy) {
                let placed = false;

                // Try to place the piece in an existing bar
                for (let i = 0; i < bars.length; i++) {
                    const remainingSpace = barLength - bars[i].usedLength;

                    // Check if the piece fits (including kerf)
                    if (piece.length + kerf <= remainingSpace) {
                        bars[i].cuts.push(piece.originalLength);
                        bars[i].usedLength += piece.length + kerf;
                        placed = true;
                        break;
                    }
                }

                // If the piece couldn't be placed in any existing bar, create a new bar
                if (!placed) {
                    bars.push({
                        cuts: [piece.originalLength],
                        usedLength: piece.length + kerf
                    });
                }
            }

            return calculateResults(bars, barLength, kerf);
        }

        // Best-Fit Decreasing algorithm
        function bestFitDecreasing(pieces, barLength, kerf) {
            // Make a deep copy of pieces to avoid modifying the original
            const piecesCopy = JSON.parse(JSON.stringify(pieces));

            // Initialize bars
            const bars = [];

            for (const piece of piecesCopy) {
                let bestBarIndex = -1;
                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible

                // Find the bar with the smallest remaining space that can still fit the piece
                for (let i = 0; i < bars.length; i++) {
                    const remainingSpace = barLength - bars[i].usedLength;

                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {
                        bestBarIndex = i;
                        bestRemainingSpace = remainingSpace;
                    }
                }

                // If a suitable bar was found, place the piece there
                if (bestBarIndex !== -1) {
                    bars[bestBarIndex].cuts.push(piece.originalLength);
                    bars[bestBarIndex].usedLength += piece.length + kerf;
                } else {
                    // Otherwise, create a new bar
                    bars.push({
                        cuts: [piece.originalLength],
                        usedLength: piece.length + kerf
                    });
                }
            }

            return calculateResults(bars, barLength, kerf);
        }

        // Calculate waste and utilization metrics
        function calculateResults(bars, barLength, kerf) {
            let totalUsedLength = 0;
            let totalWaste = 0;

            for (const bar of bars) {
                // Subtract one kerf from the used length (no kerf after the last cut)
                bar.usedLength -= kerf;
                totalUsedLength += bar.usedLength;

                // Calculate waste for this bar
                bar.waste = barLength - bar.usedLength;
                totalWaste += bar.waste;
            }

            const totalMaterial = bars.length * barLength;
            const materialUtilization = (totalUsedLength / totalMaterial) * 100;
            const wastePercentage = (totalWaste / totalMaterial) * 100;

            return {
                bars,
                totalBars: bars.length,
                totalUsedLength,
                totalWaste,
                materialUtilization,
                wastePercentage
            };
        }

        // Function to display results
        function displayResults(result, barLength, cutItems) {
            // Update summary values
            document.getElementById('totalBars').textContent = result.totalBars;
            document.getElementById('materialUtilization').textContent = `${result.materialUtilization.toFixed(1)}%`;
            document.getElementById('totalWaste').textContent = `${result.totalWaste.toFixed(0)} mm`;
            document.getElementById('wastePercentage').textContent = `${result.wastePercentage.toFixed(1)}%`;

            // Update cut list summary
            const cutListSummary = document.getElementById('cutListSummary');
            cutListSummary.innerHTML = '';

            for (const item of cutItems) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.length} mm</td>
                    <td>${item.quantity}</td>
                    <td>${item.length * item.quantity} mm</td>
                    <td>${item.description || ''}</td>
                `;
                cutListSummary.appendChild(row);
            }

            // Update cutting plans
            const cuttingPlans = document.getElementById('cuttingPlans');
            cuttingPlans.innerHTML = '';

            for (let i = 0; i < result.bars.length; i++) {
                const bar = result.bars[i];
                const barDiv = document.createElement('div');
                barDiv.className = 'cutting-plan';

                // Create bar title
                const barTitle = document.createElement('div');
                barTitle.className = 'cutting-plan-title';
                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;
                barDiv.appendChild(barTitle);

                // Create bar visualization
                const barVisualization = document.createElement('div');
                barVisualization.className = 'bar-visualization';

                // Add cut pieces to visualization
                let currentPosition = 0;
                for (const cutLength of bar.cuts) {
                    const cutPiece = document.createElement('div');
                    cutPiece.className = 'cut-piece';
                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;
                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;
                    cutPiece.textContent = `${cutLength}mm`;
                    barVisualization.appendChild(cutPiece);

                    currentPosition += cutLength + kerf;
                }

                // Add scrap piece to visualization if there is waste
                if (bar.waste > 0) {
                    const scrapPiece = document.createElement('div');
                    scrapPiece.className = 'scrap-piece';
                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;
                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;
                    barVisualization.appendChild(scrapPiece);
                }

                barDiv.appendChild(barVisualization);

                // Add cut list for this bar
                const cutList = document.createElement('div');
                cutList.className = 'bar-cut-list';
                cutList.textContent = `Cuts: ${bar.cuts.join('mm, ')}mm`;
                barDiv.appendChild(cutList);

                cuttingPlans.appendChild(barDiv);
            }

            // Show results container
            document.getElementById('resultsContainer').style.display = 'block';

            // Scroll to results
            document.getElementById('resultsContainer').scrollIntoView({ behavior: 'smooth' });
        }

        // Function to save cut list to local storage
        function saveCutList() {
            const barLength = document.getElementById('barLength').value;
            const kerf = document.getElementById('kerf').value;

            // Get cut list items from Excel table
            const cutItems = [];
            const tableBody = document.getElementById('excelTableBody');
            const rows = tableBody.rows;

            for (let i = 0; i < rows.length; i++) {
                const lengthInput = rows[i].querySelector('.cut-length');
                const quantityInput = rows[i].querySelector('.cut-quantity');
                const descriptionInput = rows[i].querySelector('.cut-description');

                const length = lengthInput.value;
                const quantity = quantityInput.value;
                const description = descriptionInput.value;

                if (length && quantity) {
                    cutItems.push({
                        length,
                        quantity,
                        description
                    });
                }
            }

            // Create save data object
            const saveData = {
                barLength,
                kerf,
                cutItems,
                timestamp: new Date().toISOString()
            };

            // Generate a filename based on date
            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;

            // Create a download link
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", filename);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
        }

        // Function to load cut list from file
        function loadCutList() {
            // Create a file input element
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.json';

            fileInput.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const saveData = JSON.parse(e.target.result);

                        // Set bar length and kerf
                        document.getElementById('barLength').value = saveData.barLength;
                        document.getElementById('kerf').value = saveData.kerf;

                        // Clear existing table rows
                        const tableBody = document.getElementById('excelTableBody');
                        tableBody.innerHTML = '';

                        // Add cut items from saved data
                        for (let i = 0; i < saveData.cutItems.length; i++) {
                            const item = saveData.cutItems[i];
                            const newRow = document.createElement('tr');

                            newRow.innerHTML = `
                                <td class="row-number">${i + 1}</td>
                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>
                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>
                                <td><input type="text" class="cut-description" value="${item.description || ''}" placeholder="Optional description"></td>
                                <td class="action-cell">
                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>
                                </td>
                            `;

                            tableBody.appendChild(newRow);
                            setupKeyboardNavigation(newRow);
                        }

                        // Update row numbers
                        updateRowNumbers();

                        alert('Cut list loaded successfully!');
                    } catch (error) {
                        alert('Error loading file: ' + error.message);
                    }
                };
                reader.readAsText(file);
            };

            fileInput.click();
        }

        // Function to save optimization results
        function saveResults() {
            // Get the results container
            const resultsContainer = document.getElementById('resultsContainer');

            // Create a text representation of the results
            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\n";
            resultsText += "=================================\n\n";

            // Add summary information
            resultsText += `Total Bars Needed: ${document.getElementById('totalBars').textContent}\n`;
            resultsText += `Material Utilization: ${document.getElementById('materialUtilization').textContent}\n`;
            resultsText += `Total Waste: ${document.getElementById('totalWaste').textContent}\n`;
            resultsText += `Waste Percentage: ${document.getElementById('wastePercentage').textContent}\n\n`;

            // Add cut list summary
            resultsText += "CUT LIST SUMMARY:\n";
            resultsText += "----------------\n";
            const cutListRows = document.querySelectorAll('#cutListSummary tr');
            for (const row of cutListRows) {
                const cells = row.querySelectorAll('td');
                if (cells.length === 3) {
                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\n`;
                }
            }
            resultsText += "\n";

            // Add cutting plans
            resultsText += "CUTTING PLANS:\n";
            resultsText += "-------------\n";
            const cuttingPlans = document.querySelectorAll('.cutting-plan');
            for (let i = 0; i < cuttingPlans.length; i++) {
                const plan = cuttingPlans[i];
                const title = plan.querySelector('.cutting-plan-title').textContent;
                const cutList = plan.querySelector('.bar-cut-list').textContent;

                resultsText += `${title}\n${cutList}\n\n`;
            }

            // Generate a filename based on date
            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;

            // Create a download link
            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", filename);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
        }
    </script>
</body>
</html>
