import { useState, useRef, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { z } from "zod";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Custom tooltip component for the formula tooltips
const Tooltip = ({ children, content }: { children: React.ReactNode, content: string }) => {
  return (
    <div className="group relative inline-block">
      {children}
      <div className="pointer-events-none absolute z-50 w-auto min-w-[120px] -translate-x-1/2 -translate-y-[180%] rounded-md bg-black px-2 py-1 text-xs text-white opacity-0 shadow-sm transition-opacity duration-300 group-hover:opacity-100 left-[50%]">
        {content}
        <div className="absolute left-1/2 top-full h-0 w-0 -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-black"></div>
      </div>
    </div>
  );
};

// Define the form schema
const aiWindowAnalysisSchema = z.object({
  width: z.coerce.number().min(100, "Width must be at least 100mm").max(10000, "Width must be less than 10000mm"),
  height: z.coerce.number().min(100, "Height must be at least 100mm").max(10000, "Height must be less than 10000mm"),
  windowImage: z.string().optional(),
});

type AIWindowAnalysisForm = z.infer<typeof aiWindowAnalysisSchema>;

export default function AIWindowAnalysisPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [windowImagePreview, setWindowImagePreview] = useState<string | null>(null);
  const [analysisResult, setAnalysisResult] = useState<any | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Create form
  const form = useForm<AIWindowAnalysisForm>({
    resolver: zodResolver(aiWindowAnalysisSchema),
    defaultValues: {
      width: 1000,
      height: 1200,
      windowImage: '',
    },
  });

  // Handle image upload
  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: t('File too large'),
        description: t('Maximum file size is 5MB'),
        variant: 'destructive',
      });
      return;
    }
    
    // Create a preview
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        const result = event.target.result as string;
        setWindowImagePreview(result);
        form.setValue('windowImage', result);
      }
    };
    reader.readAsDataURL(file);
  };

  // AI Analysis mutation
  const analyzeWindowMutation = useMutation({
    mutationFn: async (data: AIWindowAnalysisForm) => {
      const res = await apiRequest("POST", "/api/ai/analyze-window", data);
      return await res.json();
    },
    onSuccess: (data) => {
      setAnalysisResult(data);
      setIsGenerating(false);
      toast({
        title: t('Analysis complete'),
        description: t('AI has analyzed your window and generated a bill of materials'),
      });
    },
    onError: (error: Error) => {
      setIsGenerating(false);
      toast({
        title: t('Analysis failed'),
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Handle form submission
  const onSubmit = (data: AIWindowAnalysisForm) => {
    if (!data.windowImage) {
      toast({
        title: t('Image required'),
        description: t('Please upload an image of the window'),
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    analyzeWindowMutation.mutate(data);
  };

  // Check if user has access to AI features (platinum subscription or admin)
  const hasAccess = user?.subscriptionPlan === "platinum" || user?.role === "admin";

  if (!hasAccess) {
    return (
      <div className="container mx-auto py-12">
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <CardTitle className="text-center text-3xl">{t('AI Window Analysis')}</CardTitle>
            <CardDescription className="text-center text-lg">
              {t('This feature is available for Platinum subscribers and admins only')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-8 text-center">
              <div className="mx-auto mb-6 h-24 w-24 rounded-full bg-primary/10 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><rect width="20" height="14" x="2" y="5" rx="2"/><path d="M2 10h20"/><path d="M6 15h4"/><path d="M14 15h4"/></svg>
              </div>
              <h3 className="mb-2 text-xl font-medium">{t('Upgrade to Platinum')}</h3>
              <p className="text-muted-foreground">
                {t('Unlock powerful AI features that automatically analyze window images to generate designs, components, and cutting lists.')}
              </p>
            </div>
            <div className="flex justify-center gap-4">
              <Button variant="outline" asChild>
                <Link href="/">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="m15 18-6-6 6-6"/></svg>
                  {t('Back to Home')}
                </Link>
              </Button>
              <Button asChild>
                <Link href="/subscription-page">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>
                  {t('Upgrade Now')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="mr-4"
            asChild
          >
            <Link href="/">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="m15 18-6-6 6-6"/></svg>
              {t('Back')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t('AI Window Analysis')}</h1>
        </div>
        <div className="flex items-center space-x-2">
          <div className="bg-primary/10 text-primary font-medium px-3 py-1 rounded-full text-sm flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>
            {t('Platinum Feature')}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left column - Upload and dimensions */}
        <Card>
          <CardHeader>
            <CardTitle>{t('Window Analysis Input')}</CardTitle>
            <CardDescription>
              {t('Upload a window image and enter dimensions to generate a bill of materials')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <FormLabel>{t('Window Image')}</FormLabel>
                  
                  <div className="flex items-center justify-center w-full">
                    <label
                      htmlFor="window-image-upload"
                      className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/30 hover:bg-muted/50"
                    >
                      {windowImagePreview ? (
                        <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
                          <img 
                            src={windowImagePreview}
                            alt={t('Window image preview')}
                            className="object-contain max-h-full max-w-full"
                          />
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg className="w-10 h-10 mb-4 text-muted-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className="mb-2 text-sm text-muted-foreground">{t('Drag and drop or click to upload')}</p>
                          <p className="text-xs text-muted-foreground">{t('PNG, JPG or SVG (max. 5MB)')}</p>
                        </div>
                      )}
                      <input
                        id="window-image-upload"
                        type="file"
                        className="hidden"
                        accept="image/*"
                        ref={fileInputRef}
                        onChange={handleImageUpload}
                      />
                    </label>
                  </div>
                  
                  {windowImagePreview && (
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setWindowImagePreview(null);
                        form.setValue('windowImage', '');
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                    >
                      {t('Remove Image')}
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('Width (mm)')}</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="1000" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('Height (mm)')}</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="1200" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                      {t('Analyzing...')}
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"/><path d="m8 16 4 4 4-4"/><path d="M16 20h4"/><path d="M4 8h2.01"/><path d="M4 12h2.01"/><path d="M4 16h2.01"/></svg>
                      {t('Analyze with AI')}
                    </>
                  )}
                </Button>
              </form>
            </Form>

            <div className="mt-8">
              <h3 className="font-medium mb-2">{t('How it works')}</h3>
              <ol className="list-decimal pl-5 text-sm text-muted-foreground space-y-2">
                <li>{t('Upload a clear image of a window')}</li>
                <li>{t('Enter the actual dimensions (width and height)')}</li>
                <li>{t('AI analyzes the image to identify profiles, frames and glass')}</li>
                <li>{t('Generates a complete bill of materials and cutting list')}</li>
                <li>{t('You can save the results as a new window design')}</li>
              </ol>
            </div>
          </CardContent>
        </Card>

        {/* Right column - Analysis results */}
        <Card>
          <CardHeader>
            <CardTitle>{t('Analysis Results')}</CardTitle>
            <CardDescription>
              {analysisResult 
                ? t('AI-generated components and cutting list')
                : t('Results will appear here after analysis')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isGenerating ? (
              <div className="flex flex-col items-center justify-center py-12 space-y-4">
                <div className="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                <p className="text-muted-foreground">{t('Analyzing window image...')}</p>
              </div>
            ) : analysisResult ? (
              <div className="space-y-6">
                <Tabs defaultValue="components">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="components">{t('Components')}</TabsTrigger>
                    <TabsTrigger value="glass">{t('Glass')}</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="components" className="pt-4 pb-2">
                    <div className="rounded-md border">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-muted/50">
                            <th className="p-2 text-left">{t('Component')}</th>
                            <th className="p-2 text-left">{t('Type')}</th>
                            <th className="p-2 text-center">{t('Width')}</th>
                            <th className="p-2 text-center">{t('Width Qty')}</th>
                            <th className="p-2 text-center">{t('Height')}</th>
                            <th className="p-2 text-center">{t('Height Qty')}</th>
                            <th className="p-2 text-center">{t('Cut Angle')}</th>
                            <th className="p-2 text-center">{t('Qty')}</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisResult.components?.map((component: any, index: number) => (
                            <tr key={index} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/30'}>
                              <td className="p-2">
                                <div className="flex items-center">
                                  {/* Component type icon - larger and more prominent */}
                                  <div className="mr-3 h-10 w-10 flex-shrink-0 rounded-md bg-blue-50 flex items-center justify-center text-blue-500 border border-blue-200">
                                    {component.componentType?.toLowerCase().includes('frame') ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/></svg>
                                    ) : component.componentType?.toLowerCase().includes('mullion') || component.name?.toLowerCase().includes('mullion') ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="12" x2="12" y1="2" y2="22"/></svg>
                                    ) : component.componentType?.toLowerCase().includes('transom') || component.name?.toLowerCase().includes('transom') ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="2" x2="22" y1="12" y2="12"/></svg>
                                    ) : component.componentType?.toLowerCase().includes('sash') ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3"/><path d="M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v2H7v-2a2 2 0 0 0-4 0Z"/></svg>
                                    ) : (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"/></svg>
                                    )}
                                  </div>
                                  <div>
                                    <span className="font-medium">{component.name}</span>
                                  </div>
                                </div>
                              </td>
                              <td className="p-2">
                                <Badge variant="outline" className="text-xs">
                                  {component.componentType}
                                </Badge>
                              </td>
                              <td className="p-2 text-center">
                                {/* For widthSize, use the new originalWidthSize if available */}
                                {component.originalWidthSize ? (
                                  component.originalWidthSize + " mm"
                                ) : component.widthFormula && component.widthFormula !== "0" && component.widthFormula !== "null" ? (
                                  <Tooltip content={`Formula: ${component.widthFormula}`}>
                                    <span>{component.widthFormula.startsWith('width') 
                                      ? t('Variable') 
                                      : component.widthFormula + " mm"}
                                    </span>
                                  </Tooltip>
                                ) : "-"}
                              </td>
                              <td className="p-2 text-center">{component.widthQuantity && component.widthQuantity !== "0" ? component.widthQuantity : "-"}</td>
                              <td className="p-2 text-center">
                                {/* For heightSize, use the new originalHeightSize if available */}
                                {component.originalHeightSize ? (
                                  component.originalHeightSize + " mm"
                                ) : component.heightFormula && component.heightFormula !== "0" && component.heightFormula !== "null" ? (
                                  <Tooltip content={`Formula: ${component.heightFormula}`}>
                                    <span>{component.heightFormula.startsWith('height') 
                                      ? t('Variable') 
                                      : component.heightFormula + " mm"}
                                    </span>
                                  </Tooltip>
                                ) : "-"}
                              </td>
                              <td className="p-2 text-center">{component.heightQuantity && component.heightQuantity !== "0" ? component.heightQuantity : "-"}</td>
                              <td className="p-2 text-center">{component.cutAngle || component.leftCutDegree || "45°"}</td>
                              <td className="p-2 text-center">{component.quantity || component.quantityFormula || "1"}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="glass" className="pt-4 pb-2">
                    <div className="rounded-md border">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-muted/50">
                            <th className="p-2 text-left">{t('Glass Item')}</th>
                            <th className="p-2 text-left">{t('Type')}</th>
                            <th className="p-2 text-center">{t('Width')}</th>
                            <th className="p-2 text-center">{t('Height')}</th>
                            <th className="p-2 text-center">{t('Thickness')}</th>
                            <th className="p-2 text-center">{t('Qty')}</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisResult.glass?.map((glass: any, index: number) => (
                            <tr key={index} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/30'}>
                              <td className="p-2">
                                <div className="flex items-center">
                                  {/* Glass type icon - larger and more prominent */}
                                  <div className="mr-3 h-10 w-10 flex-shrink-0 rounded-md bg-indigo-50 flex items-center justify-center text-indigo-500 border border-indigo-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" opacity="0.6" /><path d="M9 3v18" opacity="0.3" /><path d="M3 9h18" opacity="0.3" /></svg>
                                  </div>
                                  <div>
                                    <span className="font-medium">{glass.name}</span>
                                  </div>
                                </div>
                              </td>
                              <td className="p-2">
                                <Badge variant="outline" className="text-xs">
                                  {glass.glassType}
                                </Badge>
                              </td>
                              <td className="p-2 text-center">
                                {glass.widthFormula ? (
                                  <Tooltip content={`Formula: ${glass.widthFormula}`}>
                                    <span>{glass.widthFormula.startsWith('width') 
                                      ? t('Variable') 
                                      : glass.originalWidth ? `${glass.originalWidth} mm` : glass.widthFormula}
                                    </span>
                                  </Tooltip>
                                ) : glass.width ? (
                                  `${glass.width} mm`
                                ) : "-"}
                              </td>
                              <td className="p-2 text-center">
                                {glass.heightFormula ? (
                                  <Tooltip content={`Formula: ${glass.heightFormula}`}>
                                    <span>{glass.heightFormula.startsWith('height') 
                                      ? t('Variable') 
                                      : glass.originalHeight ? `${glass.originalHeight} mm` : glass.heightFormula}
                                    </span>
                                  </Tooltip>
                                ) : glass.height ? (
                                  `${glass.height} mm`
                                ) : "-"}
                              </td>
                              <td className="p-2 text-center">{glass.thickness ? `${glass.thickness} mm` : "4 mm"}</td>
                              <td className="p-2 text-center">{glass.quantity || glass.quantityFormula || "1"}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">{t('AI Analysis Notes')}</h3>
                  </div>
                  <div className="p-4 bg-muted/30 rounded-md text-sm">
                    <p>{analysisResult.analysisNotes || t('No additional notes from the analysis.')}</p>
                  </div>
                </div>

                <div className="flex justify-end gap-3">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><path d="M3 15v4h4"/><path d="M21 9V5h-4"/></svg>
                        {t('Save as Design')}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{t('Save as Window Design')}</AlertDialogTitle>
                        <AlertDialogDescription>
                          {t('This will create a new window design with all components and glass specifications generated by the AI. You can edit it later in the Window Calculator.')}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="include-original-image" />
                          <label
                            htmlFor="include-original-image"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {t('Include original image in the design')}
                          </label>
                        </div>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>{t('Cancel')}</AlertDialogCancel>
                        <AlertDialogAction>{t('Save Design')}</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                  
                  <Button>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12" x2="12" y1="15" y2="3"/></svg>
                    {t('Export PDF')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center space-y-4">
                <div className="h-24 w-24 rounded-full bg-muted/30 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground"><path d="M21 7v6h-6"/><path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"/></svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium">{t('No Analysis Results Yet')}</h3>
                  <p className="text-muted-foreground mt-1">{t('Upload a window image and click "Analyze with AI" to get started')}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}