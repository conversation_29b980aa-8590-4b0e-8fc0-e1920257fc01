// Database migrations script
import pg from 'pg';
const { Pool } = pg;

// Connect to the database
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is missing');
}

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Migration to add machining_operations, cnc_tools tables, and account status fields
async function applyMigrations() {
  const client = await pool.connect();
  try {
    console.log('Starting migrations...');
    
    // Start transaction
    await client.query('BEGIN');
    
    // Check if machining_operations table exists
    const checkMachiningOperationsTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'machining_operations'
      );
    `);
    
    if (!checkMachiningOperationsTable.rows[0].exists) {
      console.log('Creating machining_operations table...');
      await client.query(`
        CREATE TABLE machining_operations (
          id SERIAL PRIMARY KEY,
          component_id INTEGER NOT NULL REFERENCES components(id),
          operation_type TEXT NOT NULL,
          name TEXT NOT NULL,
          position_formula TEXT NOT NULL,
          edge_reference TEXT NOT NULL,
          width REAL,
          depth REAL,
          length REAL,
          diameter REAL,
          angle REAL,
          offset_x REAL NOT NULL DEFAULT 0,
          offset_y REAL NOT NULL DEFAULT 0,
          parameters JSONB,
          tool_id INTEGER,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
          user_id INTEGER REFERENCES users(id)
        );
      `);
    } else {
      console.log('machining_operations table already exists.');
    }
    
    // Check if cnc_tools table exists
    const checkCncToolsTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'cnc_tools'
      );
    `);
    
    if (!checkCncToolsTable.rows[0].exists) {
      console.log('Creating cnc_tools table...');
      await client.query(`
        CREATE TABLE cnc_tools (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          diameter REAL NOT NULL,
          length REAL,
          material_type TEXT,
          speed_rpm INTEGER,
          feed_rate REAL,
          description TEXT,
          image_url TEXT,
          parameters JSONB,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
          user_id INTEGER REFERENCES users(id)
        );
      `);
      
      // Add foreign key to machining_operations for tool_id
      console.log('Adding foreign key constraint for tool_id...');
      await client.query(`
        ALTER TABLE machining_operations 
        ADD CONSTRAINT machining_operations_tool_id_fkey 
        FOREIGN KEY (tool_id) REFERENCES cnc_tools(id);
      `);
    } else {
      console.log('cnc_tools table already exists.');
    }
    
    // Check if account_status column exists in users table
    const checkAccountStatusColumn = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'account_status'
      );
    `);
    
    if (!checkAccountStatusColumn.rows[0].exists) {
      console.log('Adding account status fields to users table...');
      await client.query(`
        ALTER TABLE users 
        ADD COLUMN account_status TEXT NOT NULL DEFAULT 'active',
        ADD COLUMN payment_issue_date TEXT,
        ADD COLUMN status_note TEXT;
      `);
      console.log('Account status fields added successfully.');
    } else {
      console.log('Account status fields already exist in users table.');
    }
    
    // Check if password_changed column exists in users table
    const checkPasswordChangedColumn = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'password_changed'
      );
    `);
    
    if (!checkPasswordChangedColumn.rows[0].exists) {
      console.log('Adding password_changed field to users table...');
      await client.query(`
        ALTER TABLE users 
        ADD COLUMN password_changed BOOLEAN NOT NULL DEFAULT true;
      `);
      console.log('Password changed field added successfully.');
    } else {
      console.log('Password changed field already exists in users table.');
    }
    
    // Commit the transaction
    await client.query('COMMIT');
    
    console.log('Migrations completed successfully.');
  } catch (err) {
    // Rollback the transaction on error
    await client.query('ROLLBACK');
    console.error('Error applying migrations:', err);
    throw err;
  } finally {
    client.release();
  }
}

// Run migrations
applyMigrations()
  .then(() => {
    console.log('All migrations completed.');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });