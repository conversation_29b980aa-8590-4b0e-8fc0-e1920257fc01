import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Loader2, Plus, FileText, Trash2, Edit, ArrowLeft, Save, FileOutput, PlusCircle, Calculator, Dot, RefreshCw, Printer, Eye } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Link, useLocation } from "wouter";
import { useTranslation } from "react-i18next";
import jsPDF from "jspdf";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { WindowDesign, WindowProject, ProjectWindowItem } from "@shared/schema";
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors, DragEndEvent } from "@dnd-kit/core";
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

// New project form schema
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  clientName: z.string().optional(),
  clientContact: z.string().optional(),
  location: z.string().optional(),
  dueDate: z.string().optional(),
  status: z.string().default("draft"),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

// Project Item form schema
const projectItemSchema = z.object({
  windowDesignId: z.number(),
  width: z.number().min(1, "Width must be at least 1"),
  height: z.number().min(1, "Height must be at least 1"),
  quantity: z.number().min(1, "Quantity must be at least 1").default(1),
  windowType: z.string().optional(),
  notes: z.string().optional(),
});

type ProjectItemFormValues = z.infer<typeof projectItemSchema>;

// Sortable project item component
const SortableItem = ({ 
  id, 
  item, 
  windowDesign, 
  onEdit, 
  onDelete,
  onGeneratePdf
}: { 
  id: number, 
  item: ProjectWindowItem, 
  windowDesign: WindowDesign | undefined, 
  onEdit: () => void, 
  onDelete: () => void,
  onGeneratePdf: () => void
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });
  const { t } = useTranslation();
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    touchAction: "none"
  };
  
  return (
    <div 
      ref={setNodeRef}
      style={style}
      className="border rounded-md p-4 mb-2 bg-card flex items-center justify-between gap-2 cursor-move"
      {...attributes}
      {...listeners}
    >
      <div className="flex items-center gap-3 flex-1">
        <div className="bg-muted rounded-md p-2 w-14 h-14 flex-shrink-0 flex items-center justify-center relative group">
          {windowDesign?.imageUrl ? (
            <>
              <img 
                src={windowDesign.imageUrl} 
                alt={windowDesign?.name || 'Window design'} 
                className="max-w-full max-h-full object-contain z-10" 
              />
              <div className="absolute opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20">
                <div className="fixed -translate-x-1/2 -translate-y-full mt-[-20px] p-2 bg-background border shadow-md rounded-lg">
                  <img 
                    src={windowDesign.imageUrl} 
                    alt={windowDesign?.name || 'Window design'} 
                    className="max-w-[240px] max-h-[240px] object-contain" 
                  />
                </div>
              </div>
            </>
          ) : (
            <FileText className="text-muted-foreground" />
          )}
        </div>
        <div className="flex flex-col">
          <div className="font-medium">
            {windowDesign?.name || 'Unknown Design'}
            {item.windowType && <span className="text-primary ml-2">({item.windowType})</span>}
          </div>
          <div className="text-sm text-muted-foreground">
            {item.width} × {item.height} mm | Qty: {item.quantity}
          </div>
          {item.notes && <div className="text-xs text-muted-foreground mt-1">{item.notes}</div>}
        </div>
      </div>
      <div className="flex gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={onGeneratePdf}>
                <FileOutput className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {t("Generate Window Cutting List")}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button variant="outline" size="icon" onClick={onEdit}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="icon" onClick={onDelete}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default function WindowProjectsPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  
  // State for the project list display
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [addItemDialogOpen, setAddItemDialogOpen] = useState(false);
  const [editItemDialogOpen, setEditItemDialogOpen] = useState(false);
  const [cuttingListDialogOpen, setCuttingListDialogOpen] = useState(false);
  const [materialOrderDialogOpen, setMaterialOrderDialogOpen] = useState(false);
  const [architraveDialogOpen, setArchitraveDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<WindowProject | null>(null);
  const [selectedItem, setSelectedItem] = useState<ProjectWindowItem | null>(null);
  const [selectedWindowItems, setSelectedWindowItems] = useState<number[]>([]);
  const [materialOrderData, setMaterialOrderData] = useState<any>(null);
  const [isLoadingMaterialOrder, setIsLoadingMaterialOrder] = useState(false);
  const [stockLengths, setStockLengths] = useState<number[]>([3000, 4000, 5000, 6000, 6500]);
  const [showCuttingPlanDetails, setShowCuttingPlanDetails] = useState(false);
  const [architraveSize, setArchitraveSize] = useState<number>(0);
  const [editingProjectId, setEditingProjectId] = useState<number | null>(null);
  const [editProjectDialogOpen, setEditProjectDialogOpen] = useState(false);
  
  // Form for creating new projects
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      clientName: "",
      clientContact: "",
      location: "",
      dueDate: "",
      status: "draft",
      notes: ""
    },
  });
  
  // Form for adding items to project
  const itemForm = useForm<ProjectItemFormValues>({
    resolver: zodResolver(projectItemSchema),
    defaultValues: {
      windowDesignId: 0,
      width: 0,
      height: 0,
      quantity: 1,
      windowType: "",
      notes: ""
    },
  });
  
  // Fetch all projects
  const { 
    data: projects = [], 
    isLoading: isLoadingProjects, 
    error: projectsError 
  } = useQuery({
    queryKey: ['/api/window-projects'],
    enabled: !!user,
  });
  
  // Fetch all window designs
  const { 
    data: windowDesigns = [], 
    isLoading: isLoadingDesigns 
  } = useQuery({
    queryKey: ['/api/window-designs'],
    enabled: !!user,
  });
  
  // Fetch project items when a project is selected
  const { 
    data: projectItems = [], 
    isLoading: isLoadingItems 
  } = useQuery({
    queryKey: ['/api/window-projects', selectedProject?.id, 'items'],
    queryFn: async () => {
      if (!selectedProject) return [];
      const res = await fetch(`/api/window-projects/${selectedProject.id}/items`);
      if (!res.ok) throw new Error('Failed to fetch project items');
      return res.json();
    },
    enabled: !!selectedProject,
  });
  
  // Set all items as selected when cutting list dialog opens
  useEffect(() => {
    if (cuttingListDialogOpen && projectItems.length > 0) {
      setSelectedWindowItems(projectItems.map(item => item.id));
    }
  }, [cuttingListDialogOpen, projectItems]);
  
  // Create new project mutation
  const createProjectMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      const res = await apiRequest("POST", "/api/window-projects", data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Project created",
        description: "Your project has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects'] });
      setNewProjectDialogOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error creating project",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update project mutation
  const updateProjectMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: FormValues }) => {
      const res = await apiRequest("PUT", `/api/window-projects/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Project updated",
        description: "Your project has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects'] });
      setEditProjectDialogOpen(false);
      setEditingProjectId(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error updating project",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/window-projects/${id}`);
    },
    onSuccess: () => {
      toast({
        title: "Project deleted",
        description: "The project has been deleted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects'] });
      setSelectedProject(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error deleting project",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Copy project mutation
  const copyProjectMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/window-projects/${id}/copy`);
      return await res.json();
    },
    onSuccess: (newProject) => {
      toast({
        title: "Project copied",
        description: "Your project has been copied successfully with all window designs and components.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects'] });
      
      // Set up the form with the copied project data for editing
      form.reset({
        name: newProject.name,
        description: newProject.description || '',
        clientName: newProject.clientName || '',
        clientContact: newProject.clientContact || '',
        location: newProject.location || '',
        dueDate: newProject.dueDate || '',
        status: newProject.status || 'pending',
        notes: newProject.notes || ''
      });
      
      // Store the project ID for the update operation
      setEditingProjectId(newProject.id);
      
      // Open the edit dialog
      setEditProjectDialogOpen(true);
    },
    onError: (error: Error) => {
      toast({
        title: "Error copying project",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Add item to project mutation
  const addItemMutation = useMutation({
    mutationFn: async (data: ProjectItemFormValues) => {
      if (!selectedProject) throw new Error("No project selected");
      const res = await apiRequest("POST", `/api/window-projects/${selectedProject.id}/items`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Item added",
        description: "The item has been added to the project successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects', selectedProject?.id, 'items'] });
      setAddItemDialogOpen(false);
      itemForm.reset({
        windowDesignId: 0,
        width: 0,
        height: 0,
        quantity: 1,
        windowType: "",
        notes: ""
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error adding item",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update item mutation
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: Partial<ProjectItemFormValues> }) => {
      const res = await apiRequest("PUT", `/api/project-window-items/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Item updated",
        description: "The item has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects', selectedProject?.id, 'items'] });
      setEditItemDialogOpen(false);
      setSelectedItem(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error updating item",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Delete item mutation
  const deleteItemMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/project-window-items/${id}`);
    },
    onSuccess: () => {
      toast({
        title: "Item removed",
        description: "The item has been removed from the project.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects', selectedProject?.id, 'items'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error removing item",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Reorder items mutation
  const reorderItemsMutation = useMutation({
    mutationFn: async ({ projectId, itemIds }: { projectId: number, itemIds: number[] }) => {
      const res = await apiRequest("POST", `/api/window-projects/${projectId}/reorder`, { itemIds });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/window-projects', selectedProject?.id, 'items'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error reordering items",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Function to load material order data
  const handleLoadMaterialOrder = async (projectId: number) => {
    setIsLoadingMaterialOrder(true);
    try {
      // Pass stock lengths and architrave size in the query params for optimization
      const stockLengthsStr = stockLengths.join(',');
      const res = await fetch(`/api/window-projects/${projectId}/material-order?stockLengths=${stockLengthsStr}&architraveSize=${architraveSize}`);
      if (!res.ok) {
        throw new Error('Failed to load material order data');
      }
      const data = await res.json();
      setMaterialOrderData(data);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load material order data",
        variant: "destructive",
      });
      setMaterialOrderDialogOpen(false);
    } finally {
      setIsLoadingMaterialOrder(false);
    }
  };

  // When a window design is selected, set default dimensions
  const handleWindowDesignChange = (designId: number) => {
    const design = windowDesigns.find(d => d.id === Number(designId));
    if (design) {
      // If design has default height/width set them in the form
      itemForm.setValue("windowDesignId", design.id);
    }
  };
  
  // Function to start editing an item
  const handleEditItem = (item: ProjectWindowItem) => {
    setSelectedItem(item);
    
    // Set form values
    itemForm.reset({
      windowDesignId: item.windowDesignId,
      width: item.width,
      height: item.height,
      quantity: item.quantity,
      windowType: item.windowType || "",
      notes: item.notes || ""
    });
    
    setEditItemDialogOpen(true);
  };
  
  // Function to confirm item deletion
  const handleDeleteItem = (id: number) => {
    if (confirm("Are you sure you want to remove this item from the project?")) {
      deleteItemMutation.mutate(id);
    }
  };
  
  // Function to generate individual window PDF
  const handleGenerateWindowPdf = (item: ProjectWindowItem) => {
    if (!item.windowDesignId) return;
    generateIndividualWindowPdf(item.windowDesignId, item.width, item.height, item.quantity);
  };
  
  // Handle project form submission
  const onSubmit = (data: FormValues) => {
    createProjectMutation.mutate(data);
  };
  
  // Handle project update form submission
  const onUpdateProject = (data: FormValues) => {
    if (editingProjectId) {
      updateProjectMutation.mutate({
        id: editingProjectId,
        data,
      });
    }
  };
  
  // Handle item form submission
  const onAddItem = (data: ProjectItemFormValues) => {
    addItemMutation.mutate(data);
  };
  
  // Handle item update form submission
  const onUpdateItem = (data: ProjectItemFormValues) => {
    if (!selectedItem) return;
    
    updateItemMutation.mutate({
      id: selectedItem.id,
      data,
    });
  };
  
  // Handle drag end for reordering items
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );
  
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = projectItems.findIndex(item => item.id === active.id);
      const newIndex = projectItems.findIndex(item => item.id === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newItems = arrayMove(projectItems, oldIndex, newIndex);
        const itemIds = newItems.map(item => item.id);
        
        if (selectedProject) {
          reorderItemsMutation.mutate({
            projectId: selectedProject.id,
            itemIds
          });
        }
      }
    }
  };
  
  // Generate Detailed Cutting Plan PDF
  const generateDetailedCuttingPlanPdf = () => {
    if (!selectedProject || !materialOrderData) return;
    
    // Create a PDF document in landscape mode to avoid text being cut off
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm"
    });
    
    // Add metadata
    doc.setProperties({
      title: `${selectedProject.name} - ${t('Detailed Cutting Plan')}`,
      subject: 'Cutting Plan',
      author: 'Aluminum Cutting Optimizer',
      creator: 'Aluminum Cutting Optimizer'
    });
    
    // Add title
    doc.setFontSize(18);
    doc.text(`${selectedProject.name} - ${t('DETAILED CUTTING PLAN')}`, 20, 20);
    
    // Add date
    const date = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.text(`${t('Date')}: ${date}`, 20, 30);
    
    let yPos = 45;
    
    // Loop through each profile type
    if (materialOrderData.materialOrder && Array.isArray(materialOrderData.materialOrder)) {
      materialOrderData.materialOrder.forEach((result: any, index: number) => {
        // Add header for each profile
        doc.setFontSize(14);
        doc.setFont("helvetica", "bold");
        doc.text(`${result?.profileName || "Profile"} - ${result?.stockLength || 0}mm`, 20, yPos);
        yPos += 10;
        
        // Add bar count
        doc.setFontSize(11);
        doc.setFont("helvetica", "normal");
        doc.text(`${t("Total bars")}: ${result?.totalBars || 0} - ${t("Waste")}: ${result?.wastePercentage ? result.wastePercentage.toFixed(1) : "0"}%`, 20, yPos);
        yPos += 15;
        
        // Draw each bar with cuts
        if (result?.bars && Array.isArray(result.bars)) {
          result.bars.forEach((bar: any, barIndex: number) => {
            // Check if we need a new page - using a lower threshold (180) to ensure there's enough space
            // for the bar visualization and cut list details that follow
            if (yPos > 180) {
              doc.addPage();
              yPos = 30;
            }
            
            // Draw bar header
            doc.setFont("helvetica", "bold");
            doc.text(`${t("Bar")} #${barIndex + 1} (${bar?.stockLength || 0}mm) - ${t("Usage")}: ${bar?.utilizationPercentage ? bar.utilizationPercentage.toFixed(1) : "0"}%`, 20, yPos);
            yPos += 7;
            
            // Draw bar as a rectangle
            const barStartX = 20;
            const barWidth = 170;
            const barHeight = 10;
            
            // Draw full bar
            doc.setDrawColor(200, 200, 200);
            doc.setFillColor(240, 240, 240);
            doc.rect(barStartX, yPos, barWidth, barHeight, 'FD');
            
            // Draw cuts in the bar
            if (bar?.cuts && Array.isArray(bar.cuts)) {
              bar.cuts.forEach((cut: any) => {
                const cutStartPercent = cut?.startPosition && bar?.stockLength ? cut.startPosition / bar.stockLength : 0;
                const cutLengthPercent = cut?.length && bar?.stockLength ? cut.length / bar.stockLength : 0;
                
                const cutStartX = barStartX + (barWidth * cutStartPercent);
                const cutWidth = barWidth * cutLengthPercent;
                
                // Draw the cut piece
                doc.setDrawColor(100, 100, 100);
                doc.setFillColor(120, 180, 220);
                doc.rect(cutStartX, yPos, cutWidth, barHeight, 'FD');
                
                // Add cut piece info - only if wide enough
                if (cutWidth > 20) {
                  doc.setFontSize(8);
                  doc.setTextColor(255, 255, 255);
                  doc.text(`${cut?.length || 0}`, cutStartX + 3, yPos + 6, { baseline: 'middle' });
                  doc.setTextColor(0, 0, 0);
                }
              });
            }
            
            // Draw waste segment
            if (bar?.wasteLength > 0 && bar?.stockLength && bar?.usedLength) {
              const wasteStartPercent = bar.usedLength / bar.stockLength;
              const wasteLengthPercent = bar.wasteLength / bar.stockLength;
              
              const wasteStartX = barStartX + (barWidth * wasteStartPercent);
              const wasteWidth = barWidth * wasteLengthPercent;
              
              // Draw the waste
              doc.setDrawColor(200, 100, 100);
              doc.setFillColor(240, 150, 150);
              doc.rect(wasteStartX, yPos, wasteWidth, barHeight, 'FD');
              
              // Add waste info
              if (wasteWidth > 20) {
                doc.setFontSize(8);
                doc.text(`${bar.wasteLength}`, wasteStartX + 3, yPos + 6, { baseline: 'middle' });
              }
            }
            
            yPos += barHeight + 5;
            
            // Add cut list details below the bar
            if (bar?.cuts && Array.isArray(bar.cuts)) {
              doc.setFontSize(9);
              doc.setFont("helvetica", "normal");
              
              bar.cuts.forEach((cut: any, cutIndex: number) => {
                // Check if we need a new page - using a lower threshold (200) to ensure there's enough space
                // for all cut details that follow
                if (yPos > 200) {
                  doc.addPage();
                  yPos = 30;
                }
                
                const source = cut?.source?.windowName 
                  ? `${cut.source.windowName} (${cut.source?.componentName || 'Unknown'})`
                  : cut?.description || 'Cut piece';
                  
                doc.text(`${cutIndex + 1}. ${cut?.length || 0}mm - ${source}`, 30, yPos);
                yPos += 5;
              });
              
              yPos += 8; // Add extra space after the cuts list
            }
          });
        }
        
        // Add page break between profiles
        if (index < materialOrderData.materialOrder.length - 1) {
          doc.addPage();
          yPos = 30;
        }
      });
    }
    
    // Save the PDF
    doc.save(`${selectedProject.name}-detailed-cutting-plan.pdf`);
    
    // Success notification
    toast({
      title: "Cutting plan generated",
      description: "Detailed cutting plan has been generated successfully.",
    });
  };

  // Generate Material Order PDF
  const generateMaterialOrderPdf = () => {
    if (!selectedProject || !materialOrderData) return;
    
    // Create a PDF document in landscape mode to avoid text being cut off
    const doc = new jsPDF({
      orientation: "landscape",
      unit: "mm"
    });
    
    // Add metadata
    doc.setProperties({
      title: `${selectedProject.name} - ${t('Material Order')}`,
      subject: 'Material Order Summary',
      author: 'Aluminum Cutting Optimizer',
      creator: 'Aluminum Cutting Optimizer'
    });
    
    // Add title
    doc.setFontSize(18);
    doc.text(`${selectedProject.name} - ${t('MATERIAL ORDER')}`, 20, 20);
    
    // Add date
    const date = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.text(`${t('Date')}: ${date}`, 20, 30);
    
    let yPos = 45;
    
    // Add header for the profiles list
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(t("Required Profiles"), 20, yPos);
    yPos += 15;
    
    // Add table header
    doc.setFontSize(11);
    doc.setFont("helvetica", "bold");
    doc.text(t("Profile"), 20, yPos);
    doc.text(t("Length"), 100, yPos);
    doc.text(t("Required Bars"), 150, yPos);
    yPos += 4;
    
    // Add horizontal line
    doc.line(20, yPos, 190, yPos);
    yPos += 10;
    
    // Reset font
    doc.setFont("helvetica", "normal");
    
    // Loop through each profile and add just the necessary information
    if (materialOrderData.materialOrder && Array.isArray(materialOrderData.materialOrder)) {
      materialOrderData.materialOrder.forEach((result: any, index: number) => {
        // Check if we need a new page - using a lower threshold (180) to ensure there's enough space
        // for the profile image and text that follows
        if (yPos > 180) {
          doc.addPage();
          yPos = 20;
          
          // Add table header on new page
          doc.setFontSize(11);
          doc.setFont("helvetica", "bold");
          doc.text(t("Profile"), 20, yPos);
          doc.text(t("Length"), 100, yPos);
          doc.text(t("Required Bars"), 150, yPos);
          yPos += 4;
          
          // Add horizontal line
          doc.line(20, yPos, 190, yPos);
          yPos += 10;
          
          // Reset font
          doc.setFont("helvetica", "normal");
        }
        
        // Add profile image if available
        const profileImg = result?.imageUrl;
        if (profileImg) {
          try {
            // Add profile image (max height 30mm, keeping aspect ratio)
            doc.addImage(profileImg, 'JPEG', 20, yPos - 5, 30, 30);
            doc.text(`${result?.profileName || t("Profile")}`, 55, yPos + 10);
          } catch (e) {
            // If image fails to load, just show text
            console.error('Error adding profile image to PDF:', e);
            doc.text(`${result?.profileName || t("Profile")}`, 20, yPos + 10);
          }
        } else {
          // No image, just show text
          doc.text(`${result?.profileName || t("Profile")}`, 20, yPos + 10);
        }
        
        // Add length and required bars information
        doc.text(`${result?.stockLength || 0} mm`, 100, yPos + 10);
        doc.text(`${result?.totalBars || 0}`, 150, yPos + 10);
        
        // Add space for next profile
        yPos += 40;
        
        // Add separator line
        doc.setDrawColor(200, 200, 200);
        doc.line(20, yPos - 10, 190, yPos - 10);
        doc.setDrawColor(0, 0, 0);
      });
    }
    
    // Save the PDF
    doc.save(`${selectedProject.name.replace(/\s+/g, '_')}_material_order.pdf`);
    
    toast({
      title: t("Material Order PDF Generated"),
      description: t("The material order has been exported to PDF successfully."),
    });
  };
  
  // Generate PDF for an individual window
  const generateIndividualWindowPdf = async (designId: number, width: number, height: number, quantity: number) => {
    // First, fetch all profiles
    const profilesRes = await fetch('/api/profiles');
    if (!profilesRes.ok) {
      toast({
        title: "Error",
        description: "Failed to fetch profiles data. Please try again.",
        variant: "destructive",
      });
      return;
    }
    const allProfiles = await profilesRes.json();
    
    // Fetch design details
    const design = windowDesigns.find(d => d.id === designId);
    if (!design) {
      toast({
        title: "Error",
        description: "Window design not found.",
        variant: "destructive",
      });
      return;
    }
    
    // Fetch components for the window design
    const componentsRes = await fetch(`/api/window-designs/${designId}/components`);
    if (!componentsRes.ok) {
      toast({
        title: "Error",
        description: "Failed to fetch component data. Please try again.",
        variant: "destructive",
      });
      return;
    }
    const components = await componentsRes.json();
    
    // Fetch glass specifications for the window design
    const glassRes = await fetch(`/api/window-designs/${designId}/glass-specifications`);
    if (!glassRes.ok) {
      toast({
        title: "Error",
        description: "Failed to fetch glass data. Please try again.",
        variant: "destructive",
      });
      return;
    }
    const glassSpecs = await glassRes.json();
    
    // Calculate all dimensions based on formulas
    const calculatedComponents = components.map(component => {
      // Get the profile for additional details from the fetched profiles
      const matchingProfile = allProfiles.find((p) => p.id === component.profileId);
      
      // Create a context with the window dimensions for formula evaluation
      const context = { 
        window_width: width, 
        window_height: height 
      };
      
      // Use formula evaluator to calculate dimensions
      const evalFormula = (formula: string) => {
        try {
          // Replace window_width/width and window_height/height with actual values
          let preparedFormula = formula
            .replace(/window_width/g, context.window_width.toString())
            .replace(/window_height/g, context.window_height.toString())
            .replace(/width/g, context.window_width.toString())
            .replace(/height/g, context.window_height.toString());
          
          // Simple eval for formulas
          // eslint-disable-next-line no-eval
          return eval(preparedFormula);
        } catch (error) {
          console.error("Formula evaluation error:", error);
          return 0;
        }
      };
      
      return {
        ...component,
        profile: matchingProfile,
        calculatedWidth: Math.round(evalFormula(component.widthFormula)),
        calculatedHeight: component.heightFormula ? Math.round(evalFormula(component.heightFormula)) : 0,
        calculatedQuantity: Math.round(evalFormula(component.quantityFormula || "1"))
      };
    });
    
    // Calculate glass dimensions
    const calculatedGlass = glassSpecs.map(glass => {
      const context = { 
        window_width: width, 
        window_height: height 
      };
      
      const evalFormula = (formula: string) => {
        try {
          // Replace window_width/width and window_height/height with actual values
          let preparedFormula = formula
            .replace(/window_width/g, context.window_width.toString())
            .replace(/window_height/g, context.window_height.toString())
            .replace(/width/g, context.window_width.toString())
            .replace(/height/g, context.window_height.toString());
          
          // eslint-disable-next-line no-eval
          return eval(preparedFormula);
        } catch (error) {
          console.error("Formula evaluation error:", error);
          return 0;
        }
      };
      
      return {
        ...glass,
        calculatedWidth: Math.round(evalFormula(glass.widthFormula)),
        calculatedHeight: Math.round(evalFormula(glass.heightFormula)),
        calculatedQuantity: Math.round(evalFormula(glass.quantityFormula || "1"))
      };
    });
    
    // Create the PDF
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(18);
    doc.text("WINDOW CUTTING LIST", 20, 20);
    
    // Add window details
    doc.setFontSize(14);
    doc.text(`${design.name} - ${width}×${height} mm`, 20, 30);
    
    let yPos = 40;
    
    // Add window design image if available
    if (design.imageUrl) {
      try {
        const IMAGE_SIZE = 50;
        doc.addImage(design.imageUrl, 'JPEG', 20, yPos, IMAGE_SIZE, IMAGE_SIZE);
        yPos += IMAGE_SIZE + 10;
      } catch (error) {
        console.error(`Error adding design image to PDF: ${error}`);
      }
    }
    
    // Start aluminum profile section
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text("ALUMINUM PROFILES", 20, yPos);
    yPos += 10;
    
    // Group aluminum components by profile type
    const profileGroups = {};
    
    // Process components
    calculatedComponents.forEach(component => {
      const profileId = component.profileId;
      const profileName = component.profile?.name || `Profile ID: ${profileId}`;
      
      // Get individual width and height
      const width = component.calculatedWidth || 0;
      const height = component.calculatedHeight || 0;
      
      if (width === 0 && height === 0) return; // Skip components without valid dimensions
      
      if (!profileGroups[profileId]) {
        profileGroups[profileId] = {
          name: profileName,
          imageUrl: component.profile?.imageUrl || null,
          items: []
        };
      }
      
      // Add the item to the group with separate width and height
      profileGroups[profileId].items.push({
        component: component.name,
        width: width,
        height: height,
        quantity: (component.calculatedQuantity || 1) * quantity
      });
    });
    
    // Print each profile group
    Object.values(profileGroups).forEach((group: any) => {
      // Check if we need a new page
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      // Profile name as header
      doc.setFontSize(12);
      doc.setFont("helvetica", "bold");
      
      // If profile has image, display it next to the profile name
      if (group.imageUrl) {
        try {
          // Add profile image
          const IMAGE_SIZE = 30;
          doc.addImage(group.imageUrl, 'JPEG', 20, yPos - 20, IMAGE_SIZE, IMAGE_SIZE);
          doc.text(group.name, 60, yPos);
        } catch (error) {
          console.error(`Error adding profile image to PDF: ${error}`);
          doc.text(group.name, 20, yPos);
        }
      } else {
        doc.text(group.name, 20, yPos);
      }
      
      yPos += 10;
      
      // Table header
      doc.setFontSize(10);
      // "Component" column header removed as requested
      doc.text("Width (mm)", 80, yPos);
      doc.text("W Qty", 115, yPos);
      doc.text("Height (mm)", 140, yPos);
      doc.text("H Qty", 175, yPos);
      yPos += 5;
      
      // Add horizontal line
      doc.line(20, yPos, 210, yPos);
      yPos += 8;
      
      // Reset font
      doc.setFont("helvetica", "normal");
      
      // Add items
      group.items.forEach(item => {
        // Check if we need a new page
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
          
          // Table header on new page
          doc.setFontSize(10);
          doc.setFont("helvetica", "bold");
          // "Component" column header removed as requested
          doc.text("Width (mm)", 80, yPos);
          doc.text("W Qty", 115, yPos);
          doc.text("Height (mm)", 140, yPos);
          doc.text("H Qty", 175, yPos);
          yPos += 5;
          
          // Add horizontal line
          doc.line(20, yPos, 210, yPos);
          yPos += 8;
          
          // Reset font
          doc.setFont("helvetica", "normal");
        }
        
        // Component name removed as requested
        
        // Separate width and height columns with their own qty
        const hasWidth = item.width && item.width > 0;
        const hasHeight = item.height && item.height > 0;
        
        // Width column
        if (hasWidth) {
          doc.text(item.width.toString(), 80, yPos);
          doc.text(quantity.toString(), 115, yPos);
        } else {
          doc.text("-", 80, yPos);
          doc.text("0", 115, yPos); 
        }
        
        // Height column
        if (hasHeight) {
          doc.text(item.height.toString(), 140, yPos);
          doc.text(quantity.toString(), 175, yPos);
        } else {
          doc.text("-", 140, yPos);
          doc.text("0", 175, yPos);
        }
        
        yPos += 8;
      });
      
      yPos += 5;
    });
    
    // Add glass specifications if any
    if (calculatedGlass.length > 0) {
      // Start on a new page for glass
      doc.addPage();
      yPos = 20;
      
      // Add project name above glass specs (if in a project)
      if (selectedProject) {
        doc.setFontSize(12);
        doc.setFont("helvetica", "bold");
        doc.text(`Project: ${selectedProject.name}`, 20, yPos);
        yPos += 10;
      }
      
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      doc.text("GLASS SPECIFICATIONS", 20, yPos);
      yPos += 10;
      
      // Table header
      doc.setFontSize(10);
      doc.text("Item", 20, yPos);
      doc.text("Width (mm)", 80, yPos);
      doc.text("Height (mm)", 120, yPos);
      doc.text("Quantity", 170, yPos);
      yPos += 5;
      
      // Add horizontal line
      doc.line(20, yPos, 210, yPos);
      yPos += 8;
      
      // Reset font
      doc.setFont("helvetica", "normal");
      
      // Group glass items by type/thickness
      const glassGroups = {};
      
      calculatedGlass.forEach(glass => {
        const key = `${glass.glassType}-${glass.thickness}`;
        if (!glassGroups[key]) {
          glassGroups[key] = {
            type: glass.glassType,
            thickness: glass.thickness,
            items: []
          };
        }
        
        glassGroups[key].items.push({
          name: glass.name,
          width: glass.calculatedWidth,
          height: glass.calculatedHeight,
          quantity: glass.calculatedQuantity * quantity
        });
      });
      
      // Print each glass group
      Object.values(glassGroups).forEach((group: any) => {
        // Check if we need a new page
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
          
          // Table header on new page
          doc.setFontSize(14);
          doc.setFont("helvetica", "bold");
          doc.text("GLASS SPECIFICATIONS", 20, yPos);
          yPos += 10;
          
          doc.setFontSize(10);
          doc.text("Item", 20, yPos);
          doc.text("Width (mm)", 80, yPos);
          doc.text("Height (mm)", 120, yPos);
          doc.text("Quantity", 170, yPos);
          yPos += 5;
          
          // Add horizontal line
          doc.line(20, yPos, 210, yPos);
          yPos += 8;
          
          // Reset font
          doc.setFont("helvetica", "normal");
        }
        
        // Group header - glass name and thickness (using name instead of type)
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        if (group.items && group.items.length > 0 && group.items[0].name) {
          // Use glass name instead of type but keep it concise (max 25 chars)
          const glassName = group.items[0].name.length > 25 
            ? group.items[0].name.substring(0, 25) + "..." 
            : group.items[0].name;
          doc.text(`${glassName} - ${group.thickness}mm`, 20, yPos);
        } else {
          // Fallback to type if name is not available
          doc.text(`${group.type} - ${group.thickness}mm`, 20, yPos);
        }
        yPos += 8;
        
        // Reset font
        doc.setFont("helvetica", "normal");
        
        let totalArea = 0;
        
        // Add items
        group.items.forEach(item => {
          // Check if we need a new page
          if (yPos > 270) {
            doc.addPage();
            yPos = 20;
            
            // Table header on new page
            doc.setFontSize(10);
            doc.setFont("helvetica", "bold");
            doc.text("Item", 20, yPos);
            doc.text("Width (mm)", 80, yPos);
            doc.text("Height (mm)", 120, yPos);
            doc.text("Quantity", 170, yPos);
            yPos += 5;
            
            // Add horizontal line
            doc.line(20, yPos, 210, yPos);
            yPos += 8;
            
            // Reset font
            doc.setFont("helvetica", "normal");
          }
          
          doc.text(item.name, 20, yPos);
          doc.text(item.width.toString(), 80, yPos);
          doc.text(item.height.toString(), 120, yPos);
          doc.text(item.quantity.toString(), 170, yPos);
          
          // Calculate total area
          const area = (item.width / 1000) * (item.height / 1000) * item.quantity;
          totalArea += area;
          
          yPos += 8;
        });
        
        // Add total area for this glass type
        doc.setFont("helvetica", "bold");
        doc.text("Total Area:", 80, yPos);
        doc.text(`${totalArea.toFixed(2)} m²`, 120, yPos);
        yPos += 10;
        
        // Reset font
        doc.setFont("helvetica", "normal");
      });
    }
    
    // Save the PDF
    doc.save(`${design.name}-${width}x${height}-cutting-list.pdf`);
    
    toast({
      title: "Success",
      description: "Window cutting list PDF has been generated.",
    });
  };
  
  // Generate comprehensive PDF with project details and cutting list
  const generatePdf = async (selectedItems?: number[]) => {
    if (!selectedProject) return;
    
    // Filter items if specific selections were made
    const itemsToProcess = selectedItems?.length 
      ? projectItems.filter(item => selectedItems.includes(item.id))
      : projectItems;
    
    // First, fetch all profiles
    const profilesRes = await fetch('/api/profiles');
    if (!profilesRes.ok) {
      toast({
        title: "Error",
        description: "Failed to fetch profiles data. Please try again.",
        variant: "destructive",
      });
      return;
    }
    const allProfiles = await profilesRes.json();
    
    // First, fetch all cutting list data for project items
    const cuttingListsData = await Promise.all(
      itemsToProcess.map(async (item) => {
        if (item.windowCuttingListId) {
          // If cutting list already exists, fetch it
          const res = await fetch(`/api/window-cutting-lists/${item.windowCuttingListId}`);
          if (res.ok) {
            return await res.json();
          }
        }
        
        // If no cutting list exists, generate one on the fly
        try {
          const design = windowDesigns.find(d => d.id === item.windowDesignId);
          if (!design) return null;
          
          // Fetch components for the window design
          const componentsRes = await fetch(`/api/window-designs/${design.id}/components`);
          if (!componentsRes.ok) return null;
          const components = await componentsRes.json();
          
          // Fetch glass specifications for the window design
          const glassRes = await fetch(`/api/window-designs/${design.id}/glass-specifications`);
          if (!glassRes.ok) return null;
          const glassSpecs = await glassRes.json();
          
          // Calculate all dimensions based on formulas
          const calculatedComponents = components.map(component => {
            // Get the profile for additional details from the fetched profiles
            const matchingProfile = allProfiles.find((p) => p.id === component.profileId);
            
            // Create a context with the window dimensions for formula evaluation
            const context = { 
              window_width: item.width, 
              window_height: item.height 
            };
            
            // Use formula evaluator to calculate dimensions
            // Simple implementation included - in real app, use the proper formula evaluator
            const evalFormula = (formula: string) => {
              try {
                // Replace window_width/width and window_height/height with actual values
                let preparedFormula = formula
                  .replace(/window_width/g, context.window_width.toString())
                  .replace(/window_height/g, context.window_height.toString())
                  .replace(/width/g, context.window_width.toString())
                  .replace(/height/g, context.window_height.toString());
                
                // Simple eval for formulas
                // eslint-disable-next-line no-eval
                return eval(preparedFormula);
              } catch (error) {
                console.error("Formula evaluation error:", error);
                return 0;
              }
            };
            
            return {
              ...component,
              profile: matchingProfile,
              calculatedWidth: Math.round(evalFormula(component.widthFormula)),
              calculatedHeight: component.heightFormula ? Math.round(evalFormula(component.heightFormula)) : 0,
              calculatedWidthQuantity: Math.round(evalFormula(component.widthQuantity || "0")),
              calculatedHeightQuantity: Math.round(evalFormula(component.heightQuantity || "0")),
              calculatedQuantity: Math.round(evalFormula(component.quantityFormula || "1"))
            };
          });
          
          // Calculate glass dimensions
          const calculatedGlass = glassSpecs.map(glass => {
            const context = { 
              window_width: item.width, 
              window_height: item.height 
            };
            
            const evalFormula = (formula: string) => {
              try {
                // Replace window_width/width and window_height/height with actual values
                let preparedFormula = formula
                  .replace(/window_width/g, context.window_width.toString())
                  .replace(/window_height/g, context.window_height.toString())
                  .replace(/width/g, context.window_width.toString())
                  .replace(/height/g, context.window_height.toString());
                
                // eslint-disable-next-line no-eval
                return eval(preparedFormula);
              } catch (error) {
                console.error("Formula evaluation error:", error);
                return 0;
              }
            };
            
            return {
              ...glass,
              calculatedWidth: Math.round(evalFormula(glass.widthFormula)),
              calculatedHeight: Math.round(evalFormula(glass.heightFormula)),
              calculatedQuantity: Math.round(evalFormula(glass.quantityFormula || "1"))
            };
          });
          
          return {
            id: `temp-${design.id}-${item.id}`,
            name: `${design.name} - ${item.width}×${item.height}`,
            width: item.width,
            height: item.height,
            components: calculatedComponents,
            glassItems: calculatedGlass,
            quantity: item.quantity,
            designId: design.id,
            designName: design.name,
            windowType: item.windowType
          };
        } catch (error) {
          console.error("Error generating cutting list:", error);
          return null;
        }
      })
    );
    
    // Filter out failed cutting list generations
    const validCuttingLists = cuttingListsData.filter(cl => cl !== null);
    
    // Create object to track designs and their images
    const designImages = {};
    
    // Get window design images
    validCuttingLists.forEach(list => {
      if (!list) return;
      
      // Find the design
      const design = windowDesigns.find(d => d.id === list.designId);
      if (design && design.imageUrl && !designImages[design.id]) {
        designImages[design.id] = {
          id: design.id,
          name: design.name,
          imageUrl: design.imageUrl
        };
      }
    });
    
    // Create the PDF
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(20);
    doc.text(selectedProject.name, 20, 20);
    
    // Add project details
    doc.setFontSize(12);
    let yPos = 35;
    
    if (selectedProject.description) {
      doc.text("Description:", 20, yPos);
      doc.text(selectedProject.description, 70, yPos);
      yPos += 10;
    }
    
    if (selectedProject.clientName) {
      doc.text("Client:", 20, yPos);
      doc.text(selectedProject.clientName, 70, yPos);
      yPos += 10;
    }
    
    if (selectedProject.clientContact) {
      doc.text("Contact:", 20, yPos);
      doc.text(selectedProject.clientContact, 70, yPos);
      yPos += 10;
    }
    
    if (selectedProject.location) {
      doc.text("Location:", 20, yPos);
      doc.text(selectedProject.location, 70, yPos);
      yPos += 10;
    }
    
    if (selectedProject.dueDate) {
      doc.text("Due Date:", 20, yPos);
      doc.text(selectedProject.dueDate.substring(0, 10), 70, yPos);
      yPos += 10;
    }
    
    // Add items table header
    yPos += 10;
    doc.setFontSize(14);
    doc.text("Window Items Summary:", 20, yPos);
    yPos += 10;
    
    // Table header
    doc.setFontSize(12);
    doc.setFont("helvetica", "bold");
    doc.text("Design", 20, yPos);
    doc.text("Type", 80, yPos);
    doc.text("Dimensions", 120, yPos);
    doc.text("Qty", 160, yPos);
    doc.text("Notes", 180, yPos);
    yPos += 5;
    
    // Add horizontal line
    doc.line(20, yPos, 210, yPos);
    yPos += 8;
    
    // Reset font
    doc.setFont("helvetica", "normal");
    
    // Add items
    itemsToProcess.forEach((item) => {
      const design = windowDesigns.find(d => d.id === item.windowDesignId);
      const designName = design ? design.name : 'Unknown Design';
      
      // Check if we need a new page
      if (yPos > 270) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.text(designName, 20, yPos);
      doc.text(item.windowType || "-", 80, yPos);
      doc.text(`${item.width} × ${item.height} mm`, 120, yPos);
      doc.text(item.quantity.toString(), 160, yPos);
      if (item.notes) {
        doc.text(item.notes, 180, yPos);
      }
      
      yPos += 10;
    });
    
    // Add total count
    yPos += 5;
    doc.line(20, yPos, 210, yPos);
    yPos += 8;
    
    const totalQuantity = itemsToProcess.reduce((sum, item) => sum + item.quantity, 0);
    doc.setFont("helvetica", "bold");
    doc.text(`Total Items: ${totalQuantity}`, 20, yPos);
    
    // Start aluminum cutting list on a new page
    doc.addPage();
    doc.setFontSize(18);
    doc.text("ALUMINUM CUTTING LIST", 20, 20);
    yPos = 40;
    
    // If we have window design images, add them to the PDF
    if (Object.keys(designImages).length > 0) {
      doc.setFontSize(14);
      doc.text("Window Designs:", 20, yPos);
      yPos += 10;
      
      let xPos = 20;
      const MAX_WIDTH = 50; // Maximum width for an image
      const IMAGE_ROW_HEIGHT = 60; // Height for the image row
      
      // Add window design images
      Object.values(designImages).forEach((design: any) => {
        try {
          if (design.imageUrl) {
            // If we're running out of space in the row, move to next row
            if (xPos > 150) {
              xPos = 20;
              yPos += IMAGE_ROW_HEIGHT;
            }
            
            // Add the image and caption with window size
            doc.addImage(design.imageUrl, 'JPEG', xPos, yPos, MAX_WIDTH, MAX_WIDTH);
            doc.setFontSize(8);
            // Find an item with this design to get dimensions
            const designItem = itemsToProcess.find(item => 
              windowDesigns.find(d => d.id === item.windowDesignId)?.id === design.id
            );
            
            const dimensions = designItem 
              ? `${design.name} (${designItem.width}×${designItem.height}mm)`
              : design.name;
            
            doc.text(dimensions, xPos, yPos + MAX_WIDTH + 5, { maxWidth: MAX_WIDTH, align: 'center' });
            
            // Move to the next position
            xPos += MAX_WIDTH + 10;
          }
        } catch (error) {
          console.error(`Error adding design image to PDF: ${error}`);
        }
      });
      
      // Move down after the images
      yPos += IMAGE_ROW_HEIGHT + 20;
    }
    
    // Group aluminum components by profile type
    const profileGroups = {};
    
    validCuttingLists.forEach(list => {
      if (!list) return;
      
      const multiplier = list.quantity || 1;
      
      list.components.forEach(component => {
        const profileId = component.profileId;
        const profileName = component.profile?.name || `Profile ID: ${profileId}`;
        
        // Get individual width and height
        const width = component.calculatedWidth || 0;
        const height = component.calculatedHeight || 0;
        
        if (width === 0 && height === 0) return; // Skip components without valid dimensions
        
        if (!profileGroups[profileId]) {
          profileGroups[profileId] = {
            name: profileName,
            imageUrl: component.profile?.imageUrl || null,
            items: []
          };
        }
        
        // Add the item to the group with separate width and height
        profileGroups[profileId].items.push({
          designName: list.designName,
          windowType: list.windowType || "-",
          componentName: component.name,
          width: width,
          height: height,
          quantity: (component.calculatedQuantity || 1) * multiplier,
          widthQuantity: (component.calculatedWidthQuantity || 0) * multiplier,
          heightQuantity: (component.calculatedHeightQuantity || 0) * multiplier,
          dimensions: `${list.width} × ${list.height}`,
          originalComponent: component // Include the full component reference for access to all properties
        });
      });
    });
    
    // Print each profile group
    Object.values(profileGroups).forEach((group: any) => {
      // Check if we need a new page
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      // Profile name as header
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      
      // If profile has image, display it next to the profile name
      if (group.imageUrl) {
        try {
          // Add profile image
          const IMAGE_SIZE = 30;
          doc.addImage(group.imageUrl, 'JPEG', 20, yPos - 20, IMAGE_SIZE, IMAGE_SIZE);
          doc.text(group.name, 60, yPos);
        } catch (error) {
          console.error(`Error adding profile image to PDF: ${error}`);
          doc.text(group.name, 20, yPos);
        }
      } else {
        doc.text(group.name, 20, yPos);
      }
      
      yPos += 10;
      
      // Table header
      doc.setFontSize(10);
      doc.text("Window Type", 20, yPos);
      // "Component" column header removed as requested
      doc.text("Width (mm)", 105, yPos);
      doc.text("W Qty", 130, yPos);
      doc.text("Height (mm)", 150, yPos);
      doc.text("H Qty", 175, yPos);
      yPos += 5;
      
      // Add horizontal line
      doc.line(20, yPos, 200, yPos);
      yPos += 8;
      
      // Reset font
      doc.setFont("helvetica", "normal");
      
      // Add items
      group.items.forEach(item => {
        // Check if we need a new page
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
          
          // Table header on new page
          doc.setFontSize(10);
          doc.setFont("helvetica", "bold");
          doc.text("Window Type", 20, yPos);
          // "Component" column header removed as requested
          doc.text("Width (mm)", 105, yPos);
          doc.text("W Qty", 130, yPos);
          doc.text("Height (mm)", 150, yPos);
          doc.text("H Qty", 175, yPos);
          yPos += 5;
          
          // Add horizontal line
          doc.line(20, yPos, 200, yPos);
          yPos += 8;
          
          // Reset font
          doc.setFont("helvetica", "normal");
        }
        
        doc.text(item.windowType || "-", 20, yPos);
        // Component name removed as requested
        
        // Separate width and height columns with their own qty
        const hasWidth = item.width && item.width > 0;
        const hasHeight = item.height && item.height > 0;
        
        // Width column
        if (hasWidth) {
          doc.text(item.width.toString(), 105, yPos);
          // Use the calculated width quantity if available, otherwise use a default
          const widthQty = item.widthQuantity !== undefined ? item.widthQuantity.toString() : (item.originalComponent?.calculatedWidthQuantity || "0").toString();
          doc.text(widthQty, 130, yPos);
        } else {
          doc.text("-", 105, yPos);
          doc.text("0", 130, yPos); 
        }
        
        // Height column
        if (hasHeight) {
          doc.text(item.height.toString(), 150, yPos);
          // Use the calculated height quantity if available, otherwise use a default
          const heightQty = item.heightQuantity !== undefined ? item.heightQuantity.toString() : (item.originalComponent?.calculatedHeightQuantity || "0").toString();
          doc.text(heightQty, 175, yPos);
        } else {
          doc.text("-", 150, yPos);
          doc.text("0", 175, yPos);
        }
        
        // Cutting angles display temporarily removed
        
        yPos += 8;
      });
      
      yPos += 5;
      doc.line(20, yPos, 210, yPos);
      yPos += 8;
      
      yPos += 20; // Add space between profiles
    });
    
    // Start glass list on a new page
    doc.addPage();
    doc.setFontSize(18);
    doc.text("GLASS CUTTING LIST", 20, 20);
    
    // Add project name below the title
    if (selectedProject) {
      doc.setFontSize(12);
      doc.text(`Project: ${selectedProject.name}`, 20, 30);
    }
    
    yPos = 40;
    
    // Group glass items by glass type
    const glassGroups = {};
    
    validCuttingLists.forEach(list => {
      if (!list) return;
      
      const multiplier = list.quantity || 1;
      
      list.glassItems.forEach(glass => {
        const glassType = glass.glassType;
        const width = glass.calculatedWidth || 0;
        const height = glass.calculatedHeight || 0;
        
        if (width === 0 || height === 0) return; // Skip glass without valid dimensions
        
        const glassKey = `${glassType}-${glass.thickness}`;
        
        if (!glassGroups[glassKey]) {
          glassGroups[glassKey] = {
            name: `${glassType} (${glass.thickness}mm)`,
            items: []
          };
        }
        
        // Add the item to the group
        glassGroups[glassKey].items.push({
          designName: list.designName,
          windowType: list.windowType || "-",
          name: glass.name,
          width,
          height,
          quantity: (glass.calculatedQuantity || 1) * multiplier,
          dimensions: `${list.width} × ${list.height}`
        });
      });
    });
    
    // Print each glass group
    Object.values(glassGroups).forEach((group: any) => {
      // Check if we need a new page
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      // Glass item as header - using glass name instead of type
      doc.setFontSize(14);
      doc.setFont("helvetica", "bold");
      
      // Extract the glass name from the first item in the group (if available)
      if (group.items && group.items.length > 0 && group.items[0].name) {
        // Use smaller font size for long glass names instead of truncating
        const glassName = group.items[0].name;
        if (glassName.length > 30) {
          doc.setFontSize(11); // Smaller font for long names
        }
        doc.text(`Glass Name: ${glassName}`, 20, yPos);
        doc.setFontSize(14); // Reset font size
      } else {
        // Fallback to glass type if name is not available
        doc.text(`Glass Type: ${group.name}`, 20, yPos);
      }
      yPos += 10;
      
      // Table header
      doc.setFontSize(10);
      doc.text("Window Type", 20, yPos);
      doc.text("Width (mm)", 85, yPos);
      doc.text("Height (mm)", 125, yPos);
      doc.text("Qty", 170, yPos);
      yPos += 5;
      
      // Add horizontal line
      doc.line(20, yPos, 210, yPos);
      yPos += 8;
      
      // Reset font
      doc.setFont("helvetica", "normal");
      
      // Add items
      group.items.forEach(item => {
        // Check if we need a new page
        if (yPos > 270) {
          doc.addPage();
          yPos = 20;
          
          // Table header on new page
          doc.setFontSize(10);
          doc.setFont("helvetica", "bold");
          doc.text("Window Type", 20, yPos);
          doc.text("Width (mm)", 85, yPos);
          doc.text("Height (mm)", 125, yPos);
          doc.text("Qty", 170, yPos);
          yPos += 5;
          
          // Add horizontal line
          doc.line(20, yPos, 210, yPos);
          yPos += 8;
          
          // Reset font
          doc.setFont("helvetica", "normal");
        }
        
        doc.text(item.windowType || "-", 20, yPos);
        doc.text(item.width.toString(), 85, yPos);
        doc.text(item.height.toString(), 125, yPos);
        doc.text(item.quantity.toString(), 170, yPos);
        
        yPos += 8;
      });
      
      yPos += 5;
      doc.line(20, yPos, 210, yPos);
      yPos += 8;
      
      // For projects PDF, we don't show totals (removed as requested)
      
      yPos += 20; // Add space between glass types
    });
    
    // Save the PDF
    doc.save(`${selectedProject.name}-complete-cutting-list.pdf`);
    
    toast({
      title: "Cutting List Generated",
      description: "Your complete cutting list with aluminum profiles and glass has been exported to PDF.",
    });
  };
  
  // Form handling functions have been moved to lines ~500
  
  if (isLoadingProjects) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }
  
  return (
    <div className="container py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("Back")}
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">{t("Window Projects")}</h1>
        </div>
        <Button onClick={() => setNewProjectDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t("New Project")}
        </Button>
      </div>
      
      {/* Project list or single project view */}
      {!selectedProject ? (
        // Projects list view
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {projects.length === 0 ? (
            <div className="col-span-full flex flex-col items-center justify-center gap-3 p-8 border border-dashed rounded-lg">
              <FileText className="h-10 w-10 text-muted-foreground" />
              <h3 className="text-xl font-medium">{t("No projects yet")}</h3>
              <p className="text-muted-foreground text-center max-w-xs">
                {t("Create a new project to organize your window designs and generate cutting lists.")}
              </p>
              <Button variant="outline" onClick={() => setNewProjectDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t("New Project")}
              </Button>
            </div>
          ) : (
            projects.map((project) => (
              <Card key={project.id} className="overflow-hidden flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle>{project.name}</CardTitle>
                  {project.description && (
                    <CardDescription>{project.description}</CardDescription>
                  )}
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    {project.clientName && (
                      <>
                        <div className="text-muted-foreground">{t("Client")}:</div>
                        <div>{project.clientName}</div>
                      </>
                    )}
                    {project.location && (
                      <>
                        <div className="text-muted-foreground">{t("Location")}:</div>
                        <div>{project.location}</div>
                      </>
                    )}
                    {project.dueDate && (
                      <>
                        <div className="text-muted-foreground">{t("Due Date")}:</div>
                        <div>{new Date(project.dueDate).toLocaleDateString()}</div>
                      </>
                    )}
                    <div className="text-muted-foreground">{t("Status")}:</div>
                    <div className="capitalize">{project.status}</div>
                  </div>
                </CardContent>
                <CardFooter className="flex gap-2">
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => setSelectedProject(project)}
                  >
                    {t("View")}
                  </Button>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="icon"
                          onClick={() => copyProjectMutation.mutate(project.id)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"/><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/></svg>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {t("Copy Project")}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Button 
                    variant="destructive" 
                    size="icon"
                    onClick={() => {
                      if (confirm("Are you sure you want to delete this project?")) {
                        deleteProjectMutation.mutate(project.id);
                      }
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      ) : (
        // Single project view
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">{selectedProject.name}</h2>
              {selectedProject.description && (
                <p className="text-muted-foreground">{selectedProject.description}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline"
                onClick={() => setSelectedProject(null)}
              >
                {t("Back to Projects")}
              </Button>
              <Button
                variant="outline"
                onClick={() => setCuttingListDialogOpen(true)}
              >
                <FileOutput className="h-4 w-4 mr-2" />
                {t("Generate Cutting List")}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setArchitraveSize(0); // Reset architrave size
                  setArchitraveDialogOpen(true);
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                {t("Material Order")}
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Project details */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>{t("Project Details")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedProject.clientName && (
                  <div>
                    <Label>{t("Client")}</Label>
                    <p>{selectedProject.clientName}</p>
                  </div>
                )}
                {selectedProject.clientContact && (
                  <div>
                    <Label>{t("Contact")}</Label>
                    <p>{selectedProject.clientContact}</p>
                  </div>
                )}
                {selectedProject.location && (
                  <div>
                    <Label>{t("Location")}</Label>
                    <p>{selectedProject.location}</p>
                  </div>
                )}
                {selectedProject.dueDate && (
                  <div>
                    <Label>{t("Due Date")}</Label>
                    <p>{new Date(selectedProject.dueDate).toLocaleDateString()}</p>
                  </div>
                )}
                <div>
                  <Label>{t("Status")}</Label>
                  <p className="capitalize">{selectedProject.status}</p>
                </div>
                {selectedProject.notes && (
                  <div>
                    <Label>{t("Notes")}</Label>
                    <p className="whitespace-pre-wrap">{selectedProject.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Project items */}
            <Card className="md:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle>{t("Window Items")}</CardTitle>
                <Button onClick={() => setAddItemDialogOpen(true)}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  {t("Add Item")}
                </Button>
              </CardHeader>
              <CardContent>
                {isLoadingItems ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : projectItems.length === 0 ? (
                  <div className="flex flex-col items-center justify-center gap-3 p-8 border border-dashed rounded-lg">
                    <FileText className="h-10 w-10 text-muted-foreground" />
                    <h3 className="text-xl font-medium">{t("No items yet")}</h3>
                    <p className="text-muted-foreground text-center max-w-xs">
                      {t("Add window designs to this project to organize your work.")}
                    </p>
                    <Button variant="outline" onClick={() => setAddItemDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      {t("Add First Item")}
                    </Button>
                  </div>
                ) : (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                    modifiers={[restrictToVerticalAxis]}
                  >
                    <SortableContext
                      items={projectItems.map(item => item.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-2">
                        {projectItems.map((item) => {
                          const design = windowDesigns.find(d => d.id === item.windowDesignId);
                          return (
                            <SortableItem 
                              key={item.id} 
                              id={item.id} 
                              item={item} 
                              windowDesign={design}
                              onEdit={() => handleEditItem(item)}
                              onDelete={() => handleDeleteItem(item.id)}
                              onGeneratePdf={() => handleGenerateWindowPdf(item)}
                            />
                          );
                        })}
                      </div>
                    </SortableContext>
                  </DndContext>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      )}
      
      {/* New Project Dialog */}
      <Dialog open={newProjectDialogOpen} onOpenChange={setNewProjectDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Create New Project")}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter project name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field} 
                        placeholder="Brief description of the project"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Name</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Client name"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="clientContact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Contact</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Phone or email"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Project location"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Due Date</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="date"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field}

                        placeholder="Additional notes"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setNewProjectDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createProjectMutation.isPending}
                >
                  {createProjectMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Project
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Add Item Dialog */}
      <Dialog open={addItemDialogOpen} onOpenChange={setAddItemDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Add Window Item")}</DialogTitle>
          </DialogHeader>
          <Form {...itemForm}>
            <form onSubmit={itemForm.handleSubmit(onAddItem)} className="space-y-4">
              <FormField
                control={itemForm.control}
                name="windowDesignId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Window Design *</FormLabel>
                    <Select
                      onValueChange={(value) => handleWindowDesignChange(parseInt(value))}
                      defaultValue={field.value ? field.value.toString() : ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select window design" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingDesigns ? (
                          <div className="flex justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </div>
                        ) : windowDesigns.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No window designs available
                          </div>
                        ) : (
                          windowDesigns.map((design) => (
                            <SelectItem key={design.id} value={design.id.toString()}>
                              {design.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={itemForm.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width (mm) *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="1"
                          placeholder="Width in mm"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={itemForm.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (mm) *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="1"
                          placeholder="Height in mm"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={itemForm.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity *</FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        type="number"
                        min="1"
                        placeholder="Quantity"
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={itemForm.control}
                name="windowType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Window Type</FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        placeholder="Window Type (e.g. Sliding, Casement, Fixed)"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={itemForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field} 
                        placeholder="Additional notes"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setAddItemDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={addItemMutation.isPending}
                >
                  {addItemMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Add Item
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Cutting List Dialog */}
      <Dialog open={cuttingListDialogOpen} onOpenChange={setCuttingListDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Generate Cutting List")}</DialogTitle>
            <DialogDescription>
              {t("Select windows to include in the cutting list")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="flex items-center justify-between">
              <Label>
                {t("Select Window Items")}
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  // Toggle all/none
                  if (selectedWindowItems.length === projectItems.length) {
                    setSelectedWindowItems([]);
                  } else {
                    setSelectedWindowItems(projectItems.map(item => item.id));
                  }
                }}
              >
                {selectedWindowItems.length === projectItems.length ? t("Select None") : t("Select All")}
              </Button>
            </div>
            <div className="border rounded-md p-2 max-h-[300px] overflow-y-auto">
              {projectItems.map(item => {
                const design = windowDesigns.find(d => d.id === item.windowDesignId);
                const isSelected = selectedWindowItems.includes(item.id);
                
                return (
                  <div key={item.id} className="flex items-center space-x-2 py-2 border-b last:border-b-0">
                    <Checkbox 
                      id={`window-${item.id}`}
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedWindowItems(prev => [...prev, item.id]);
                        } else {
                          setSelectedWindowItems(prev => prev.filter(id => id !== item.id));
                        }
                      }}
                    />
                    <Label htmlFor={`window-${item.id}`} className="flex-1 cursor-pointer">
                      <div>
                        <span className="font-medium">{design?.name}</span>
                        <span className="ml-2 text-muted-foreground">
                          {item.width} × {item.height} mm
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {t("Quantity")}: {item.quantity}
                      </div>
                    </Label>
                  </div>
                );
              })}
              {projectItems.length === 0 && (
                <div className="py-4 text-center text-muted-foreground">
                  {t("No window items in this project")}
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setCuttingListDialogOpen(false)}
            >
              {t("Cancel")}
            </Button>
            <Button 
              type="button"
              onClick={() => {
                generatePdf(selectedWindowItems);
                setCuttingListDialogOpen(false);
              }}
              disabled={selectedWindowItems.length === 0}
            >
              <FileOutput className="mr-2 h-4 w-4" />
              {t("Generate PDF")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Item Dialog */}
      <Dialog open={editItemDialogOpen} onOpenChange={setEditItemDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Edit Window Item")}</DialogTitle>
          </DialogHeader>
          <Form {...itemForm}>
            <form onSubmit={itemForm.handleSubmit(onUpdateItem)} className="space-y-4">
              <FormField
                control={itemForm.control}
                name="windowDesignId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Window Design *</FormLabel>
                    <Select
                      onValueChange={(value) => handleWindowDesignChange(parseInt(value))}
                      defaultValue={field.value ? field.value.toString() : ""}
                      value={field.value.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select window design" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {windowDesigns.map((design) => (
                          <SelectItem key={design.id} value={design.id.toString()}>
                            {design.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={itemForm.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width (mm) *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="1"
                          placeholder="Width in mm"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={itemForm.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (mm) *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="number"
                          min="1"
                          placeholder="Height in mm"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={itemForm.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity *</FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        type="number"
                        min="1"
                        placeholder="Quantity"
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={itemForm.control}
                name="windowType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Window Type</FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        placeholder="Window Type (e.g. Sliding, Casement, Fixed)"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={itemForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field} 
                        placeholder="Additional notes"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setEditItemDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateItemMutation.isPending}
                >
                  {updateItemMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Item
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Architrave Size Dialog */}
      <Dialog open={architraveDialogOpen} onOpenChange={setArchitraveDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Architrave Size")}</DialogTitle>
            <DialogDescription>
              {t("Enter architrave size to add to frame width and height")}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="architraveSize">{t("Architrave Size (mm)")}</Label>
              <Input
                id="architraveSize"
                type="number"
                min="0"
                max="200"
                value={architraveSize}
                onChange={(e) => setArchitraveSize(parseInt(e.target.value) || 0)}
                placeholder="Enter size in mm"
              />
              <p className="text-sm text-muted-foreground">
                {t("This value will be added to the width and height of frame components during optimization")}
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setArchitraveDialogOpen(false)}
            >
              {t("Cancel")}
            </Button>
            <Button
              onClick={() => {
                setArchitraveDialogOpen(false);
                setMaterialOrderDialogOpen(true);
                if (selectedProject) {
                  handleLoadMaterialOrder(selectedProject.id);
                }
              }}
            >
              {t("Continue to Material Order")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Material Order Dialog */}
      <Dialog open={materialOrderDialogOpen} onOpenChange={setMaterialOrderDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t("Material Order Optimization")}</DialogTitle>
            <DialogDescription>
              {t("Optimized cutting plan for all windows in this project")}
              {architraveSize > 0 && (
                <div className="mt-2 text-sm text-muted-foreground italic">
                  {t("Including {{size}}mm architrave on frame components ({{total}}mm total added to each dimension)", { 
                    size: architraveSize,
                    total: architraveSize * 2
                  })}
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {/* Stock length configuration */}
          <div className="mb-4 p-4 border rounded-md bg-muted/30">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">{t("Available Stock Lengths")}</h3>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => {
                  // Refresh order with current settings
                  if (selectedProject) {
                    handleLoadMaterialOrder(selectedProject.id);
                  }
                }}
                disabled={isLoadingMaterialOrder}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {t("Recalculate")}
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mb-2">
              {stockLengths.map((length, index) => (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {length} mm
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 rounded-full"
                    onClick={() => {
                      const newLengths = [...stockLengths];
                      newLengths.splice(index, 1);
                      setStockLengths(newLengths);
                    }}
                  >
                    <span className="sr-only">Remove</span>
                    ×
                  </Button>
                </Badge>
              ))}
              <form 
                onSubmit={(e) => {
                  e.preventDefault();
                  const input = e.currentTarget.elements.namedItem('newLength') as HTMLInputElement;
                  const value = parseInt(input.value);
                  if (!isNaN(value) && value > 0 && !stockLengths.includes(value)) {
                    setStockLengths([...stockLengths, value].sort((a, b) => a - b));
                    input.value = '';
                  }
                }}
                className="flex items-center gap-1"
              >
                <Input
                  name="newLength"
                  type="number"
                  min="500"
                  max="10000"
                  placeholder="Add length (mm)"
                  className="w-32 h-8 text-xs"
                />
                <Button type="submit" variant="outline" size="sm" className="h-8">
                  <Plus className="h-3 w-3" />
                </Button>
              </form>
            </div>
            <p className="text-xs text-muted-foreground">
              {t("The optimization algorithm will select the most efficient stock length from these options.")}
            </p>
          </div>
          
          {isLoadingMaterialOrder ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : materialOrderData && materialOrderData.materialOrder && materialOrderData.materialOrder.length > 0 ? (
            <div className="space-y-6">
              {/* Overall Statistics Card */}
              <Card className="overflow-hidden bg-muted/30">
                <CardHeader className="pb-3">
                  <CardTitle>{t("Material Utilization Overview")}</CardTitle>
                  <CardDescription>
                    {t("Overall material usage statistics for the entire project")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {(() => {
                    // Calculate overall statistics
                    let totalMaterial = 0;
                    let totalWaste = 0;
                    let totalCutPieces = 0;
                    
                    if (materialOrderData?.materialOrder && Array.isArray(materialOrderData.materialOrder)) {
                      materialOrderData.materialOrder.forEach((result: any) => {
                        if (result && typeof result.totalBars === 'number' && typeof result.stockLength === 'number') {
                          const barsLength = result.totalBars * result.stockLength;
                          const wasteLength = barsLength * ((result.wastePercentage || 0) / 100);
                          
                          totalMaterial += barsLength;
                          totalWaste += wasteLength;
                          totalCutPieces += (result.cutPieces && Array.isArray(result.cutPieces)) ? result.cutPieces.length : 0;
                        }
                      });
                    }
                    
                    const overallWastePercentage = totalMaterial > 0 ? (totalWaste / totalMaterial) * 100 : 0;
                    const overallUsagePercentage = 100 - overallWastePercentage;
                    
                    return (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                          <div className="border rounded-md p-3 bg-background">
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t("Total Material Used")}</div>
                            <div className="text-2xl font-bold">{(totalMaterial / 1000).toFixed(2)} m</div>
                            <div className="text-xs text-muted-foreground">{totalMaterial.toFixed(0)} mm</div>
                          </div>
                          <div className="border rounded-md p-3 bg-background">
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t("Material Utilization")}</div>
                            <div className="text-2xl font-bold text-primary">{overallUsagePercentage.toFixed(1)}%</div>
                            <div className="text-xs text-muted-foreground">{t("Efficiently utilized")}</div>
                          </div>
                          <div className="border rounded-md p-3 bg-background">
                            <div className="text-sm font-medium text-muted-foreground mb-1">{t("Material Wastage")}</div>
                            <div className="text-2xl font-bold text-destructive">{overallWastePercentage.toFixed(1)}%</div>
                            <div className="text-xs text-muted-foreground">{(totalWaste / 1000).toFixed(2)} m waste</div>
                          </div>
                        </div>
                        
                        <div className="w-full bg-muted rounded-full h-2.5">
                          <div className="bg-primary h-2.5 rounded-full" style={{ width: `${overallUsagePercentage}%` }}></div>
                        </div>
                        
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>{t("Total Items")}: {totalCutPieces}</span>
                          <span>{t("Total Stock Bars")}: {materialOrderData?.materialOrder && Array.isArray(materialOrderData.materialOrder) ? 
                            materialOrderData.materialOrder.reduce((acc: number, curr: any) => acc + (curr.totalBars || 0), 0) : 0}
                          </span>
                        </div>
                      </div>
                    );
                  })()}
                </CardContent>
              </Card>
              
              {/* Material Order Display - Toggle between simplified and detailed view */}
              {!showCuttingPlanDetails ? (
                // Simplified material order view (default)
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {materialOrderData?.materialOrder && Array.isArray(materialOrderData.materialOrder) && 
                    materialOrderData.materialOrder.map((result: any, resultIndex: number) => (
                    <Card key={resultIndex} className="overflow-hidden flex flex-col h-full">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle>{result?.profileName || "Profile"}</CardTitle>
                          <Badge variant="secondary" className="ml-2">
                            {result?.stockLength || 0}mm
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="flex flex-1 items-center">
                        <div className="flex items-center w-full gap-4">
                          {/* Profile Image */}
                          <div className="flex-shrink-0 w-24 h-24 bg-muted/30 rounded-md flex items-center justify-center overflow-hidden">
                            {result?.imageUrl ? (
                              <img 
                                src={result.imageUrl} 
                                alt={result.profileName || "Profile"} 
                                className="max-w-full max-h-full object-contain"
                              />
                            ) : (
                              <FileText className="h-12 w-12 text-muted-foreground/40" />
                            )}
                          </div>
                          
                          {/* Profile Info */}
                          <div className="flex-1">
                            <div className="flex flex-col gap-2">
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                                <span className="font-medium">{t("Length")}: {result?.stockLength || 0}mm</span>
                              </div>
                              <div className="flex items-center">
                                <Calculator className="h-4 w-4 mr-2 text-muted-foreground" />
                                <span className="font-medium">{t("Required Bars")}: {result?.totalBars || 0}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                // Detailed cutting plan view (shown when "View Cutting Plan" is clicked)
                <div className="space-y-6">
                  {materialOrderData?.materialOrder && Array.isArray(materialOrderData.materialOrder) && 
                    materialOrderData.materialOrder.map((result: any, resultIndex: number) => (
                    <Card key={resultIndex} className="overflow-hidden">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle>{result?.profileName || "Profile"}</CardTitle>
                          <Badge variant="secondary" className="ml-2">
                            {t("Optimized")}: {result?.stockLength || 0}mm
                          </Badge>
                        </div>
                        <CardDescription>
                          {t("Total bars")}: {result?.totalBars || 0} | {t("Waste")}: {result?.wastePercentage ? result.wastePercentage.toFixed(1) : "0"}%
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {result?.bars && Array.isArray(result.bars) && result.bars.map((bar: any, barIndex: number) => (
                            <div key={barIndex} className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <div className="font-medium">
                                  {t("Bar")} #{barIndex + 1} ({bar?.stockLength || 0}mm) - {t("Usage")}: {bar?.utilizationPercentage ? bar.utilizationPercentage.toFixed(1) : "0"}%
                                </div>
                                <div className="text-muted-foreground">
                                  {t("Used")}: {bar?.usedLength || 0}mm | {t("Waste")}: {bar?.wasteLength || 0}mm
                                </div>
                              </div>
                              <div className="relative h-6 w-full bg-muted rounded-sm overflow-hidden">
                                {bar?.cuts && Array.isArray(bar.cuts) && bar.cuts.map((cut: any, cutIndex: number) => {
                                  const cutPercentage = cut?.length && bar?.stockLength ? (cut.length / bar.stockLength) * 100 : 0;
                                  return (
                                    <div
                                      key={cutIndex}
                                      className="absolute top-0 h-full bg-primary/80 border-r border-primary-foreground flex items-center justify-center text-xs text-primary-foreground"
                                      style={{
                                        left: `${cut?.startPosition && bar?.stockLength ? (cut.startPosition / bar.stockLength) * 100 : 0}%`,
                                        width: `${cutPercentage}%`,
                                      }}
                                      title={`${cut?.length || 0}mm ${cut?.source?.windowName ? `- ${cut.source.windowName} (${cut.source?.componentName || 'Unknown'})` : `- ${cut?.description || 'Cut piece'}`}`}
                                    >
                                      {cutPercentage > 5 ? `${cut?.length || 0}` : ""}
                                    </div>
                                  );
                                })}
                                {/* Waste segment */}
                                {bar?.wasteLength > 0 && bar?.stockLength && bar?.usedLength && (
                                  <div
                                    className="absolute top-0 h-full bg-destructive/30 flex items-center justify-center text-xs"
                                    style={{
                                      left: `${(bar.usedLength / bar.stockLength) * 100}%`,
                                      width: `${(bar.wasteLength / bar.stockLength) * 100}%`,
                                    }}
                                    title={`${t("Waste")}: ${bar.wasteLength}mm`}
                                  >
                                    {bar.wasteLength > (bar.stockLength * 0.05) ? `${bar.wasteLength}` : ""}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex justify-between text-sm font-medium">
                            <div>{t("Average utilization")}:</div>
                            <div>{result?.averageUtilization ? result.averageUtilization.toFixed(1) : "0"}%</div>
                          </div>
                          <div className="flex justify-between text-sm">
                            <div>{t("Total material length")}:</div>
                            <div>{result?.totalMaterialLength || 0}mm</div>
                          </div>
                          <div className="flex justify-between text-sm">
                            <div>{t("Total waste")}:</div>
                            <div>{result?.totalWaste || 0}mm ({result?.wastePercentage ? result.wastePercentage.toFixed(1) : "0"}%)</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center gap-3 p-8 border border-dashed rounded-lg">
              <FileText className="h-10 w-10 text-muted-foreground" />
              <h3 className="text-xl font-medium">{t("No cutting data available")}</h3>
              <p className="text-muted-foreground text-center">
                {t("To generate a material order, you need to first create cutting lists for your windows and assign them to project items.")}
              </p>
              <div className="flex justify-center mt-4">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/window-calculator">
                    <Calculator className="h-4 w-4 mr-2" />
                    {t("Go to Window Design")}
                  </Link>
                </Button>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2 items-center">
            <Button 
              variant="outline" 
              onClick={() => setMaterialOrderDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              {t("Close")}
            </Button>
            
            {materialOrderData?.materialOrder && Array.isArray(materialOrderData.materialOrder) && materialOrderData.materialOrder.length > 0 && (
              <>
                <Button 
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => {
                    // Toggle a state to show the detailed cutting plan
                    setShowCuttingPlanDetails(!showCuttingPlanDetails);
                  }}
                >
                  {showCuttingPlanDetails ? (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      {t("Hide Cutting Plan")}
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      {t("View Cutting Plan")}
                    </>
                  )}
                </Button>
                
                <Button 
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => {
                    // Generate detailed cutting plan PDF
                    generateDetailedCuttingPlanPdf();
                  }}
                >
                  <Printer className="h-4 w-4 mr-2" />
                  {t("Print Cutting Plan")}
                </Button>
                
                <Button 
                  className="w-full sm:w-auto"
                  onClick={() => {
                    // Generate simplified material order PDF
                    generateMaterialOrderPdf();
                  }}
                >
                  <FileOutput className="h-4 w-4 mr-2" />
                  {t("Export Material Order")}
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Project Dialog */}
      <Dialog open={editProjectDialogOpen} onOpenChange={setEditProjectDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Edit Project")}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onUpdateProject)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter project name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field} 
                        placeholder="Brief description of the project"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Name</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Client name"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="clientContact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Contact</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Phone or email"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Project location"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Due Date</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="date"
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field}
                        placeholder="Additional notes"
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setEditProjectDialogOpen(false);
                    setEditingProjectId(null);
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateProjectMutation.isPending}
                >
                  {updateProjectMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Project
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}