import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Globe } from "lucide-react";
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from "react";

export default function LanguageSwitcher() {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(
    i18n.language || localStorage.getItem('language') || 'en'
  );
  
  // Update currentLanguage when i18n.language changes
  useEffect(() => {
    setCurrentLanguage(i18n.language || localStorage.getItem('language') || 'en');
  }, [i18n.language]);

  const changeLanguage = (lng: string) => {
    console.log('Changing language to:', lng);
    
    // Store language preference before reload
    localStorage.setItem('language', lng);
    
    // Set the document direction based on the language
    document.documentElement.lang = lng;
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
    
    // Change i18n language - we'll do this first before showing feedback
    i18n.changeLanguage(lng).then(() => {
      console.log('Language changed successfully to', lng);
      
      // Set state after i18n has been updated
      setCurrentLanguage(lng);
      
      // Log the current state of i18n
      console.log('Current i18n state:', {
        language: i18n.language,
        languages: i18n.languages,
        options: i18n.options
      });
      
      // Show a brief message indicating language change
      const message = document.createElement('div');
      message.style.position = 'fixed';
      message.style.top = '50%';
      message.style.left = '50%';
      message.style.transform = 'translate(-50%, -50%)';
      message.style.background = 'rgba(0, 0, 0, 0.8)';
      message.style.color = 'white';
      message.style.padding = '20px';
      message.style.borderRadius = '8px';
      message.style.zIndex = '9999';
      message.textContent = lng === 'ar' ? 'تغيير اللغة إلى العربية...' : 'Changing language to English...';
      document.body.appendChild(message);
      
      // Force refresh after a small delay to apply RTL/LTR changes properly
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }).catch(err => {
      console.error('Error changing language:', err);
    });
  };

  // Get translations
  const { t } = useTranslation();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
          <Globe className="h-5 w-5" />
          <span className="sr-only">{t('language.changeLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="language-menu">
        <DropdownMenuItem 
          onClick={() => changeLanguage('en')}
          className={currentLanguage === 'en' ? 'bg-primary/10' : ''}
        >
          {t('language.english')}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => changeLanguage('ar')}
          className={currentLanguage === 'ar' ? 'bg-primary/10' : ''}
        >
          {t('language.arabic')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}