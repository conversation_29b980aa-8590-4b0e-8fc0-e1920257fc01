2025-07-06 06:39:37,880 - semgrep.run_scan - DEBUG - semgrep version 1.4.0
2025-07-06 06:39:37,884 - semgrep.git - DEBUG - Failed to get project url from 'git ls-remote': Command failed with exit code: 128
-----
Command failed with output:
fatal: No remote configured to list refs from.


Failed to run 'git ls-remote --get-url'. Possible reasons:

- the git binary is not available
- the current working directory is not a git repository
- the baseline commit is not a parent of the current commit
    (if you are running through semgrep-app, check if you are setting `SEMGREP_BRANCH` or `SEMGREP_BASELINE_COMMIT` properly)
- the current working directory is not marked as safe
    (fix with `git config --global --add safe.directory $(pwd)`)

Try running the command yourself to debug the issue.
2025-07-06 06:39:37,885 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-07-06 06:39:37,887 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-07-06 06:39:37,890 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-kttck25a.rules
2025-07-06 06:39:38,429 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-07-06 06:39:38,430 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-07-06 06:39:38,430 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-07-06 06:39:38,430 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.5449621677398682
2025-07-06 06:39:38,517 - semgrep.run_scan - VERBOSE - running 715 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-07-06 06:39:38,517 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-07-06 06:39:38,519 - semgrep.run_scan - VERBOSE - Rules:
2025-07-06 06:39:38,519 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-07-06 06:39:39,125 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-07-06 06:39:39,285 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-07-06 06:39:39,285 - semgrep.core_runner - DEBUG - /tmp/_MEIJ8TdYo/semgrep/bin/opengrep-core -json -rules /tmp/tmpkfaodkwp.json -j 8 -targets /tmp/tmpddrs0tdl -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
2025-07-06 06:39:56,558 - semgrep.core_runner - DEBUG - --- semgrep-core stderr ---
[00.06][[34mINFO[0m]: Executed as: /tmp/_MEIJ8TdYo/semgrep/bin/opengrep-core -json -rules /tmp/tmpkfaodkwp.json -j 8 -targets /tmp/tmpddrs0tdl -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
[00.06][[34mINFO[0m]: Version: 1.4.0
[00.06][[34mINFO[0m]: Parsing rules in /tmp/tmpkfaodkwp.json
[00.45][[34mINFO[0m]: scan: processing 410 files (skipping 0), with 457 rules (skipping 0 )
[02.24][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/accessories-manager.tsx func: ???]
[0m[02.46][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/accessories-manager.tsx func: ???]
[0m[02.76][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/OptimizationResults.tsx func: OptimizationResults:24742]
[0m[03.87][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/CutListManager.tsx func: CutListManager:50014]
[0m[05.21][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/GlassCutListManager.tsx func: GlassCutListManager:81764]
[0m[05.24][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/GlassCutListManager.tsx func: GlassCutListManager:81764]
[0m[05.63][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/pages/ai-window-analysis.tsx func: AIWindowAnalysisPage:72684]
[0m[06.09][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/GlassOptimizationResults.tsx func: GlassOptimizationResults:95961]
[0m[06.22][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/GlassOptimizationResults.tsx func: GlassOptimizationResults:95961]
[0m[06.57][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[06.86][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/components/GlassOptimizationResults.tsx func: GlassOptimizationResults:95961]
[0m[07.16][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[07.85][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[08.43][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[09.65][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[09.98][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.sqli.node-postgres-sqli file: server/routes.ts func: registerRoutes:118544]
[0m[10.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/window-calculator.tsx func: WindowCalculatorPage:98020]
[0m[10.45][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[10.68][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: server/routes.ts func: registerRoutes:118544]
[0m[10.88][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.path-traversal.path-join-resolve-traversal file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[11.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes.ts func: registerRoutes:118544]
[0m[11.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes.ts func: registerRoutes:118544]
[0m[11.03][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: server/routes.ts func: registerRoutes:118544]
[0m[11.32][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[11.42][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: server/routes.ts func: registerRoutes:118544]
[0m[11.56][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[12.07][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: server/routes.ts func: registerRoutes:118544]
[0m[12.20][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[12.52][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[12.52][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[12.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.code-string-concat file: server/routes.ts func: registerRoutes:118544]
[0m[12.81][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: server/routes.ts func: registerRoutes:118544]
[0m[12.87][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.remote-property-injection file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[13.30][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes.ts func: registerRoutes:118544]
[0m[13.94][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes.ts func: registerRoutes:118544]
[0m[14.25][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.remote-property-injection file: server/routes.ts func: registerRoutes:118544]
[0m[14.29][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function file: client/src/pages/window-projects.tsx func: WindowProjectsPage:111739]
[0m[14.79][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: server/routes.ts func: registerRoutes:118544]
[0m[15.23][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-path-join-resolve-traversal file: server/routes.ts func: registerRoutes:118544]
[0m[15.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes.ts func: registerRoutes:118544]
[0m[16.04][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes.ts func: registerRoutes:118544]
[0m[16.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes.ts func: registerRoutes:118544]
[0m[16.69][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes.ts func: registerRoutes:118544]
[0m--- end semgrep-core stderr ---
2025-07-06 06:39:56,630 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/aluminum-cutting-optimizer.html'), 'config..semgrep.vendored-rules.html.security.audit.insecure-document-method') match_id = 1c457e78bca64675b522f355d99f9f57316e1ca1f176e0e65e436a0f21f3b64cc42ab6cf4fca3055d21db2ab6aecc2cf3b374a34ed2c4b5e373eb93d4d5aea29_0
2025-07-06 06:39:56,636 - semgrep.rule_match - DEBUG - match_key = ('\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n     $X $X. ... .$V = "..." $X. ... .innerHTML = ... $X. ... .outerHTML = ... javascript <script ...>\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n    </script>\n', PosixPath('attached_assets/aluminum-cutting-optimizer.html'), 'config..semgrep.vendored-rules.html.security.audit.insecure-document-method') match_id = 2bc367d96dd78aa50366780ded42ab0302216abc771b5b75d8af13b2100e5b3a73c9cc5f91410062857a32098e7a860d6c23afbcfb108ad4c9d1ef3665986682_0
2025-07-06 06:39:56,643 - semgrep.rule_match - DEBUG - match_key = ('\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n     $X $X. ... .$V = "..." $X. ... .innerHTML = ... $X. ... .outerHTML = ... javascript <script ...>\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n    </script>\n', PosixPath('attached_assets/aluminum-cutting-optimizer.html'), 'config..semgrep.vendored-rules.html.security.audit.insecure-document-method') match_id = 2bc367d96dd78aa50366780ded42ab0302216abc771b5b75d8af13b2100e5b3a73c9cc5f91410062857a32098e7a860d6c23afbcfb108ad4c9d1ef3665986682_0
2025-07-06 06:39:56,649 - semgrep.rule_match - DEBUG - match_key = ('\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n     $X $X. ... .$V = "..." $X. ... .innerHTML = ... $X. ... .outerHTML = ... javascript <script ...>\n        // Set current year in footer\n        document.getElementById(\'currentYear\').textContent = new Date().getFullYear();\n\n        // Function to add a new Excel-like row\n        function addExcelRow() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rowCount = tableBody.rows.length;\n            const newRow = document.createElement(\'tr\');\n\n            newRow.innerHTML = `\n                <td class="row-number">${rowCount + 1}</td>\n                <td><input type="number" class="cut-length" value="" step="1" min="1" required></td>\n                <td><input type="number" class="cut-quantity" value="1" step="1" min="1" required></td>\n                <td><input type="text" class="cut-description" placeholder="Optional description"></td>\n                <td class="action-cell">\n                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                </td>\n            `;\n\n            tableBody.appendChild(newRow);\n\n            // Focus on the length input of the new row\n            const lengthInput = newRow.querySelector(\'.cut-length\');\n            lengthInput.focus();\n\n            // Update row numbers\n            updateRowNumbers();\n\n            // Add keyboard navigation\n            setupKeyboardNavigation(newRow);\n        }\n\n        // Function to add multiple rows at once\n        function addMultipleRows() {\n            const count = prompt(\'How many rows would you like to add?\', \'5\');\n            const numRows = parseInt(count);\n\n            if (!isNaN(numRows) && numRows > 0) {\n                for (let i = 0; i < numRows; i++) {\n                    addExcelRow();\n                }\n            }\n        }\n\n        // Function to remove an Excel row\n        function removeExcelRow(button) {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            if (tableBody.rows.length > 1) {\n                const row = button.closest(\'tr\');\n                row.remove();\n\n                // Update row numbers\n                updateRowNumbers();\n            } else {\n                alert(\'You must have at least one cut item.\');\n            }\n        }\n\n        // Function to update row numbers\n        function updateRowNumbers() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                rows[i].cells[0].textContent = i + 1;\n            }\n        }\n\n        // Function to clear the Excel table\n        function clearExcelTable() {\n            if (confirm(\'Are you sure you want to clear all rows?\')) {\n                const tableBody = document.getElementById(\'excelTableBody\');\n                tableBody.innerHTML = \'\';\n                addExcelRow(); // Add one empty row\n            }\n        }\n\n        // Function to export table data to CSV\n        function exportToCsv() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n            let csvContent = \'Length,Quantity,Description\\n\';\n\n            for (let i = 0; i < rows.length; i++) {\n                const length = rows[i].querySelector(\'.cut-length\').value || \'\';\n                const quantity = rows[i].querySelector(\'.cut-quantity\').value || \'\';\n                const description = rows[i].querySelector(\'.cut-description\').value || \'\';\n\n                // Escape commas and quotes in the description\n                const escapedDescription = description.replace(/"/g, \'""\');\n\n                csvContent += `${length},${quantity},"${escapedDescription}"\\n`;\n            }\n\n            // Create a download link\n            const encodedUri = \'data:text/csv;charset=utf-8,\' + encodeURIComponent(csvContent);\n            const link = document.createElement(\'a\');\n            link.setAttribute(\'href\', encodedUri);\n            link.setAttribute(\'download\', \'aluminum_cut_list.csv\');\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n\n        // Function to import data from CSV\n        function importFromCsv() {\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.csv\';\n\n            fileInput.onchange = function(e) {\n                const file = e.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    const contents = e.target.result;\n                    const lines = contents.split(\'\\n\');\n\n                    // Check if there\'s a header row\n                    let startRow = 0;\n                    if (lines[0].toLowerCase().includes(\'length\') &&\n                        lines[0].toLowerCase().includes(\'quantity\')) {\n                        startRow = 1; // Skip header row\n                    }\n\n                    // Clear existing rows\n                    clearExcelTable();\n                    const tableBody = document.getElementById(\'excelTableBody\');\n                    tableBody.innerHTML = \'\';\n\n                    // Parse CSV and add rows\n                    for (let i = startRow; i < lines.length; i++) {\n                        if (!lines[i].trim()) continue; // Skip empty lines\n\n                        // Handle CSV parsing (including quoted fields with commas)\n                        const values = parseCSVLine(lines[i]);\n\n                        if (values.length >= 2) {\n                            const length = values[0];\n                            const quantity = values[1];\n                            const description = values[2] || \'\';\n\n                            const newRow = document.createElement(\'tr\');\n                            newRow.innerHTML = `\n                                <td class="row-number">${i - startRow + 1}</td>\n                                <td><input type="number" class="cut-length" value="${length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${description}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n                    }\n\n                    // If no rows were added, add one empty row\n                    if (tableBody.rows.length === 0) {\n                        addExcelRow();\n                    }\n\n                    // Update row numbers\n                    updateRowNumbers();\n\n                    alert(\'CSV data imported successfully!\');\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Helper function to parse CSV line (handles quoted fields)\n        function parseCSVLine(line) {\n            const result = [];\n            let startPos = 0;\n            let inQuotes = false;\n\n            for (let i = 0; i < line.length; i++) {\n                if (line[i] === \'"\') {\n                    inQuotes = !inQuotes;\n                } else if (line[i] === \',\' && !inQuotes) {\n                    result.push(line.substring(startPos, i).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n                    startPos = i + 1;\n                }\n            }\n\n            // Add the last field\n            result.push(line.substring(startPos).replace(/^"|"$/g, \'\').replace(/""/g, \'"\'));\n\n            return result;\n        }\n\n        // Setup keyboard navigation for Excel-like experience\n        function setupKeyboardNavigation(row) {\n            const inputs = row.querySelectorAll(\'input\');\n\n            for (let i = 0; i < inputs.length; i++) {\n                inputs[i].addEventListener(\'keydown\', function(e) {\n                    const currentRow = this.closest(\'tr\');\n                    const currentCell = this.closest(\'td\');\n                    const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);\n\n                    // Tab key - move to next cell or next row\n                    if (e.key === \'Tab\' && !e.shiftKey) {\n                        if (i === inputs.length - 1) {\n                            // Last cell in row, add a new row if this is the last row\n                            const tableBody = document.getElementById(\'excelTableBody\');\n                            if (currentRow === tableBody.rows[tableBody.rows.length - 1]) {\n                                e.preventDefault();\n                                addExcelRow();\n                            }\n                        }\n                    }\n\n                    // Enter key - move to same cell in next row or add new row\n                    if (e.key === \'Enter\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            // Move to next row, same cell\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        } else {\n                            // Add new row and focus on same cell\n                            addExcelRow();\n                        }\n                    }\n\n                    // Arrow down - move to cell below\n                    if (e.key === \'ArrowDown\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex < tableBody.rows.length - 1) {\n                            const nextRow = tableBody.rows[rowIndex + 1];\n                            const nextInput = nextRow.cells[cellIndex].querySelector(\'input\');\n                            if (nextInput) nextInput.focus();\n                        }\n                    }\n\n                    // Arrow up - move to cell above\n                    if (e.key === \'ArrowUp\') {\n                        e.preventDefault();\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        const rowIndex = Array.from(tableBody.rows).indexOf(currentRow);\n\n                        if (rowIndex > 0) {\n                            const prevRow = tableBody.rows[rowIndex - 1];\n                            const prevInput = prevRow.cells[cellIndex].querySelector(\'input\');\n                            if (prevInput) prevInput.focus();\n                        }\n                    }\n                });\n            }\n        }\n\n        // Initialize keyboard navigation for existing rows\n        document.addEventListener(\'DOMContentLoaded\', function() {\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                setupKeyboardNavigation(rows[i]);\n            }\n        });\n\n        // Form submission handler\n        document.getElementById(\'optimizationForm\').addEventListener(\'submit\', function(e) {\n            e.preventDefault();\n\n            // Get form values\n            const barLength = parseFloat(document.getElementById(\'barLength\').value);\n            const kerf = parseFloat(document.getElementById(\'kerf\').value);\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = parseFloat(lengthInput.value);\n                const quantity = parseInt(quantityInput.value);\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Validate inputs\n            if (barLength <= 0) {\n                alert(\'Bar length must be greater than 0.\');\n                return;\n            }\n\n            if (kerf <= 0) {\n                alert(\'Kerf must be greater than 0.\');\n                return;\n            }\n\n            if (cutItems.length === 0) {\n                alert(\'You must add at least one cut item.\');\n                return;\n            }\n\n            // Check if any cut length is greater than bar length\n            for (const item of cutItems) {\n                if (item.length > barLength) {\n                    alert(`Cut length ${item.length}mm is greater than bar length ${barLength}mm.`);\n                    return;\n                }\n            }\n\n            // Calculate optimization\n            const result = calculateCuttingOptimization(barLength, kerf, cutItems);\n\n            // Display results\n            displayResults(result, barLength, cutItems);\n        });\n\n        // Function to calculate cutting optimization\n        function calculateCuttingOptimization(barLength, kerf, cutItems) {\n            // Create a flat array of all pieces needed\n            const pieces = [];\n            for (const item of cutItems) {\n                for (let i = 0; i < item.quantity; i++) {\n                    pieces.push({\n                        length: item.length,\n                        originalLength: item.length // Keep track of original length for display\n                    });\n                }\n            }\n\n            // Sort pieces in descending order (largest first)\n            pieces.sort((a, b) => b.length - a.length);\n\n            // Try different algorithms and pick the best result\n            const ffdResult = firstFitDecreasing(pieces, barLength, kerf);\n            const bfdResult = bestFitDecreasing(pieces, barLength, kerf);\n\n            // Compare results and return the best one\n            if (ffdResult.totalBars < bfdResult.totalBars ||\n                (ffdResult.totalBars === bfdResult.totalBars && ffdResult.totalWaste < bfdResult.totalWaste)) {\n                return ffdResult;\n            } else {\n                return bfdResult;\n            }\n        }\n\n        // First-Fit Decreasing algorithm\n        function firstFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let placed = false;\n\n                // Try to place the piece in an existing bar\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    // Check if the piece fits (including kerf)\n                    if (piece.length + kerf <= remainingSpace) {\n                        bars[i].cuts.push(piece.originalLength);\n                        bars[i].usedLength += piece.length + kerf;\n                        placed = true;\n                        break;\n                    }\n                }\n\n                // If the piece couldn\'t be placed in any existing bar, create a new bar\n                if (!placed) {\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Best-Fit Decreasing algorithm\n        function bestFitDecreasing(pieces, barLength, kerf) {\n            // Make a deep copy of pieces to avoid modifying the original\n            const piecesCopy = JSON.parse(JSON.stringify(pieces));\n\n            // Initialize bars\n            const bars = [];\n\n            for (const piece of piecesCopy) {\n                let bestBarIndex = -1;\n                let bestRemainingSpace = barLength + 1; // Initialize with a value larger than possible\n\n                // Find the bar with the smallest remaining space that can still fit the piece\n                for (let i = 0; i < bars.length; i++) {\n                    const remainingSpace = barLength - bars[i].usedLength;\n\n                    if (piece.length + kerf <= remainingSpace && remainingSpace < bestRemainingSpace) {\n                        bestBarIndex = i;\n                        bestRemainingSpace = remainingSpace;\n                    }\n                }\n\n                // If a suitable bar was found, place the piece there\n                if (bestBarIndex !== -1) {\n                    bars[bestBarIndex].cuts.push(piece.originalLength);\n                    bars[bestBarIndex].usedLength += piece.length + kerf;\n                } else {\n                    // Otherwise, create a new bar\n                    bars.push({\n                        cuts: [piece.originalLength],\n                        usedLength: piece.length + kerf\n                    });\n                }\n            }\n\n            return calculateResults(bars, barLength, kerf);\n        }\n\n        // Calculate waste and utilization metrics\n        function calculateResults(bars, barLength, kerf) {\n            let totalUsedLength = 0;\n            let totalWaste = 0;\n\n            for (const bar of bars) {\n                // Subtract one kerf from the used length (no kerf after the last cut)\n                bar.usedLength -= kerf;\n                totalUsedLength += bar.usedLength;\n\n                // Calculate waste for this bar\n                bar.waste = barLength - bar.usedLength;\n                totalWaste += bar.waste;\n            }\n\n            const totalMaterial = bars.length * barLength;\n            const materialUtilization = (totalUsedLength / totalMaterial) * 100;\n            const wastePercentage = (totalWaste / totalMaterial) * 100;\n\n            return {\n                bars,\n                totalBars: bars.length,\n                totalUsedLength,\n                totalWaste,\n                materialUtilization,\n                wastePercentage\n            };\n        }\n\n        // Function to display results\n        function displayResults(result, barLength, cutItems) {\n            // Update summary values\n            document.getElementById(\'totalBars\').textContent = result.totalBars;\n            document.getElementById(\'materialUtilization\').textContent = `${result.materialUtilization.toFixed(1)}%`;\n            document.getElementById(\'totalWaste\').textContent = `${result.totalWaste.toFixed(0)} mm`;\n            document.getElementById(\'wastePercentage\').textContent = `${result.wastePercentage.toFixed(1)}%`;\n\n            // Update cut list summary\n            const cutListSummary = document.getElementById(\'cutListSummary\');\n            cutListSummary.innerHTML = \'\';\n\n            for (const item of cutItems) {\n                const row = document.createElement(\'tr\');\n                row.innerHTML = `\n                    <td>${item.length} mm</td>\n                    <td>${item.quantity}</td>\n                    <td>${item.length * item.quantity} mm</td>\n                    <td>${item.description || \'\'}</td>\n                `;\n                cutListSummary.appendChild(row);\n            }\n\n            // Update cutting plans\n            const cuttingPlans = document.getElementById(\'cuttingPlans\');\n            cuttingPlans.innerHTML = \'\';\n\n            for (let i = 0; i < result.bars.length; i++) {\n                const bar = result.bars[i];\n                const barDiv = document.createElement(\'div\');\n                barDiv.className = \'cutting-plan\';\n\n                // Create bar title\n                const barTitle = document.createElement(\'div\');\n                barTitle.className = \'cutting-plan-title\';\n                barTitle.textContent = `Bar ${i + 1} - Utilization: ${((barLength - bar.waste) / barLength * 100).toFixed(1)}%`;\n                barDiv.appendChild(barTitle);\n\n                // Create bar visualization\n                const barVisualization = document.createElement(\'div\');\n                barVisualization.className = \'bar-visualization\';\n\n                // Add cut pieces to visualization\n                let currentPosition = 0;\n                for (const cutLength of bar.cuts) {\n                    const cutPiece = document.createElement(\'div\');\n                    cutPiece.className = \'cut-piece\';\n                    cutPiece.style.left = `${(currentPosition / barLength) * 100}%`;\n                    cutPiece.style.width = `${(cutLength / barLength) * 100}%`;\n                    cutPiece.textContent = `${cutLength}mm`;\n                    barVisualization.appendChild(cutPiece);\n\n                    currentPosition += cutLength + kerf;\n                }\n\n                // Add scrap piece to visualization if there is waste\n                if (bar.waste > 0) {\n                    const scrapPiece = document.createElement(\'div\');\n                    scrapPiece.className = \'scrap-piece\';\n                    scrapPiece.style.width = `${(bar.waste / barLength) * 100}%`;\n                    scrapPiece.textContent = `${bar.waste.toFixed(0)}mm`;\n                    barVisualization.appendChild(scrapPiece);\n                }\n\n                barDiv.appendChild(barVisualization);\n\n                // Add cut list for this bar\n                const cutList = document.createElement(\'div\');\n                cutList.className = \'bar-cut-list\';\n                cutList.textContent = `Cuts: ${bar.cuts.join(\'mm, \')}mm`;\n                barDiv.appendChild(cutList);\n\n                cuttingPlans.appendChild(barDiv);\n            }\n\n            // Show results container\n            document.getElementById(\'resultsContainer\').style.display = \'block\';\n\n            // Scroll to results\n            document.getElementById(\'resultsContainer\').scrollIntoView({ behavior: \'smooth\' });\n        }\n\n        // Function to save cut list to local storage\n        function saveCutList() {\n            const barLength = document.getElementById(\'barLength\').value;\n            const kerf = document.getElementById(\'kerf\').value;\n\n            // Get cut list items from Excel table\n            const cutItems = [];\n            const tableBody = document.getElementById(\'excelTableBody\');\n            const rows = tableBody.rows;\n\n            for (let i = 0; i < rows.length; i++) {\n                const lengthInput = rows[i].querySelector(\'.cut-length\');\n                const quantityInput = rows[i].querySelector(\'.cut-quantity\');\n                const descriptionInput = rows[i].querySelector(\'.cut-description\');\n\n                const length = lengthInput.value;\n                const quantity = quantityInput.value;\n                const description = descriptionInput.value;\n\n                if (length && quantity) {\n                    cutItems.push({\n                        length,\n                        quantity,\n                        description\n                    });\n                }\n            }\n\n            // Create save data object\n            const saveData = {\n                barLength,\n                kerf,\n                cutItems,\n                timestamp: new Date().toISOString()\n            };\n\n            // Generate a filename based on date\n            const filename = `aluminum_cut_list_${new Date().toISOString().slice(0, 10)}.json`;\n\n            // Create a download link\n            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(saveData));\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n\n        // Function to load cut list from file\n        function loadCutList() {\n            // Create a file input element\n            const fileInput = document.createElement(\'input\');\n            fileInput.type = \'file\';\n            fileInput.accept = \'.json\';\n\n            fileInput.onchange = function(event) {\n                const file = event.target.files[0];\n                if (!file) return;\n\n                const reader = new FileReader();\n                reader.onload = function(e) {\n                    try {\n                        const saveData = JSON.parse(e.target.result);\n\n                        // Set bar length and kerf\n                        document.getElementById(\'barLength\').value = saveData.barLength;\n                        document.getElementById(\'kerf\').value = saveData.kerf;\n\n                        // Clear existing table rows\n                        const tableBody = document.getElementById(\'excelTableBody\');\n                        tableBody.innerHTML = \'\';\n\n                        // Add cut items from saved data\n                        for (let i = 0; i < saveData.cutItems.length; i++) {\n                            const item = saveData.cutItems[i];\n                            const newRow = document.createElement(\'tr\');\n\n                            newRow.innerHTML = `\n                                <td class="row-number">${i + 1}</td>\n                                <td><input type="number" class="cut-length" value="${item.length}" step="1" min="1" required></td>\n                                <td><input type="number" class="cut-quantity" value="${item.quantity}" step="1" min="1" required></td>\n                                <td><input type="text" class="cut-description" value="${item.description || \'\'}" placeholder="Optional description"></td>\n                                <td class="action-cell">\n                                    <button type="button" class="remove-button" onclick="removeExcelRow(this)">Remove</button>\n                                </td>\n                            `;\n\n                            tableBody.appendChild(newRow);\n                            setupKeyboardNavigation(newRow);\n                        }\n\n                        // Update row numbers\n                        updateRowNumbers();\n\n                        alert(\'Cut list loaded successfully!\');\n                    } catch (error) {\n                        alert(\'Error loading file: \' + error.message);\n                    }\n                };\n                reader.readAsText(file);\n            };\n\n            fileInput.click();\n        }\n\n        // Function to save optimization results\n        function saveResults() {\n            // Get the results container\n            const resultsContainer = document.getElementById(\'resultsContainer\');\n\n            // Create a text representation of the results\n            let resultsText = "ALUMINUM CUTTING OPTIMIZATION RESULTS\\n";\n            resultsText += "=================================\\n\\n";\n\n            // Add summary information\n            resultsText += `Total Bars Needed: ${document.getElementById(\'totalBars\').textContent}\\n`;\n            resultsText += `Material Utilization: ${document.getElementById(\'materialUtilization\').textContent}\\n`;\n            resultsText += `Total Waste: ${document.getElementById(\'totalWaste\').textContent}\\n`;\n            resultsText += `Waste Percentage: ${document.getElementById(\'wastePercentage\').textContent}\\n\\n`;\n\n            // Add cut list summary\n            resultsText += "CUT LIST SUMMARY:\\n";\n            resultsText += "----------------\\n";\n            const cutListRows = document.querySelectorAll(\'#cutListSummary tr\');\n            for (const row of cutListRows) {\n                const cells = row.querySelectorAll(\'td\');\n                if (cells.length === 3) {\n                    resultsText += `${cells[0].textContent} x ${cells[1].textContent} = ${cells[2].textContent}\\n`;\n                }\n            }\n            resultsText += "\\n";\n\n            // Add cutting plans\n            resultsText += "CUTTING PLANS:\\n";\n            resultsText += "-------------\\n";\n            const cuttingPlans = document.querySelectorAll(\'.cutting-plan\');\n            for (let i = 0; i < cuttingPlans.length; i++) {\n                const plan = cuttingPlans[i];\n                const title = plan.querySelector(\'.cutting-plan-title\').textContent;\n                const cutList = plan.querySelector(\'.bar-cut-list\').textContent;\n\n                resultsText += `${title}\\n${cutList}\\n\\n`;\n            }\n\n            // Generate a filename based on date\n            const filename = `aluminum_cutting_results_${new Date().toISOString().slice(0, 10)}.txt`;\n\n            // Create a download link\n            const dataStr = "data:text/plain;charset=utf-8," + encodeURIComponent(resultsText);\n            const downloadAnchorNode = document.createElement(\'a\');\n            downloadAnchorNode.setAttribute("href", dataStr);\n            downloadAnchorNode.setAttribute("download", filename);\n            document.body.appendChild(downloadAnchorNode);\n            downloadAnchorNode.click();\n            downloadAnchorNode.remove();\n        }\n    </script>\n', PosixPath('attached_assets/aluminum-cutting-optimizer.html'), 'config..semgrep.vendored-rules.html.security.audit.insecure-document-method') match_id = 2bc367d96dd78aa50366780ded42ab0302216abc771b5b75d8af13b2100e5b3a73c9cc5f91410062857a32098e7a860d6c23afbcfb108ad4c9d1ef3665986682_0
2025-07-06 06:39:56,650 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 8b080898a6f1e095811925e1cbeb8287291a8cafd9911f95ca093d5265ce1c022497f3003bb5ef0f7c403a26818a36678cf5e9f43eb684c4d55f1b228a59cd38_0
2025-07-06 06:39:56,650 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('attached_assets/package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 6678b24eeb17a924c132da542d3c65afaabe9289d790717b3e3eeb3cb62720ca1ff7a2e542b14a385efa8bd442c9bcc1bda6b3f2dbef5629893efe9901563069_0
2025-07-06 06:39:56,650 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('attached_assets/package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 6678b24eeb17a924c132da542d3c65afaabe9289d790717b3e3eeb3cb62720ca1ff7a2e542b14a385efa8bd442c9bcc1bda6b3f2dbef5629893efe9901563069_0
2025-07-06 06:39:56,651 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('attached_assets/package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 6678b24eeb17a924c132da542d3c65afaabe9289d790717b3e3eeb3cb62720ca1ff7a2e542b14a385efa8bd442c9bcc1bda6b3f2dbef5629893efe9901563069_0
2025-07-06 06:39:56,651 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('attached_assets/vite.config.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-dynamic-method') match_id = 370deb9af50ce4a654b18830aa25db2b1fae554160e1d1748a5dc1f95467eb2287c25a918f04ce21925dfa28f6b3ab19e34e07094c7095ab1e39a42dd3c5d598_0
2025-07-06 06:39:56,652 - semgrep.rule_match - DEBUG - match_key = ('"..."\n (endpoint: float)\n endpoint mockApiHandlers[endpoint](...) handler = mockApiHandlers[endpoint]\n...\nhandler(...)\n $SMTH.forEach(...)\n $SMTH.map(...)\n $SMTH.reduce(...)\n $SMTH.reduceRight(...)\n for (...) {...}\n if (<... mockApiHandlers.hasOwnProperty(...) ...>) {\n  ...\n}\n...\n', PosixPath('attached_assets/vite.config.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-dynamic-method') match_id = b520992a147259315f50e2b5a676082560e0f522e2f4e7de0601201edf392c7019c134c82619f78d5f376a902a445fbf1abc0d49c1870683272a1e6d673b2a91_0
2025-07-06 06:39:56,652 - semgrep.rule_match - DEBUG - match_key = ('"..."\n (endpoint: float)\n endpoint mockApiHandlers[endpoint](...) handler = mockApiHandlers[endpoint]\n...\nhandler(...)\n $SMTH.forEach(...)\n $SMTH.map(...)\n $SMTH.reduce(...)\n $SMTH.reduceRight(...)\n for (...) {...}\n if (<... mockApiHandlers.hasOwnProperty(...) ...>) {\n  ...\n}\n...\n', PosixPath('attached_assets/vite.config.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-dynamic-method') match_id = b520992a147259315f50e2b5a676082560e0f522e2f4e7de0601201edf392c7019c134c82619f78d5f376a902a445fbf1abc0d49c1870683272a1e6d673b2a91_0
2025-07-06 06:39:56,653 - semgrep.rule_match - DEBUG - match_key = ('"..."\n (endpoint: float)\n endpoint mockApiHandlers[endpoint](...) handler = mockApiHandlers[endpoint]\n...\nhandler(...)\n $SMTH.forEach(...)\n $SMTH.map(...)\n $SMTH.reduce(...)\n $SMTH.reduceRight(...)\n for (...) {...}\n if (<... mockApiHandlers.hasOwnProperty(...) ...>) {\n  ...\n}\n...\n', PosixPath('attached_assets/vite.config.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-dynamic-method') match_id = b520992a147259315f50e2b5a676082560e0f522e2f4e7de0601201edf392c7019c134c82619f78d5f376a902a445fbf1abc0d49c1870683272a1e6d673b2a91_0
2025-07-06 06:39:56,653 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/lib/aluminumDxfExporter.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 63284c890299ccfe9c2fe2f7097b394ebe078aabedfa283a70c4ccf36c110f4ff03681bb6b33b23bb24c862ec8ed400b99fa2ebe848454a5fd6d963f509dd07a_0
2025-07-06 06:39:56,654 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/lib/aluminumDxfExporter.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = f146987d240a27ef270222e840ccc16973ee507556f80c0d8a45b1c8538fbf38a8d7ab8b873ce08c3f62dd9facc3ef5e7b2ba98690eecc67f6d389fbbfd3d803_0
2025-07-06 06:39:56,654 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/lib/aluminumDxfExporter.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = f146987d240a27ef270222e840ccc16973ee507556f80c0d8a45b1c8538fbf38a8d7ab8b873ce08c3f62dd9facc3ef5e7b2ba98690eecc67f6d389fbbfd3d803_0
2025-07-06 06:39:56,654 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/lib/aluminumDxfExporter.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = f146987d240a27ef270222e840ccc16973ee507556f80c0d8a45b1c8538fbf38a8d7ab8b873ce08c3f62dd9facc3ef5e7b2ba98690eecc67f6d389fbbfd3d803_0
2025-07-06 06:39:56,655 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/lib/formulaEvaluator.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-regexp') match_id = 85b6fe2ecda0a6e6c572bb3130eadff4bdf4df70e325ffb5f0906048f526b879178d9a2214d880030f394b2ac81581666aada86b90c24dcaca7d1ad5420924b7_0
2025-07-06 06:39:56,655 - semgrep.rule_match - DEBUG - match_key = ('variables function ... (...,variables,...) {...}\n RegExp("...", ...) RegExp(variables, ...) new RegExp(variables, ...) RegExp(/.../, ...) new RegExp("...", ...) new RegExp(/.../, ...)', PosixPath('client/src/lib/formulaEvaluator.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-regexp') match_id = 7f230b9a0ee21f730937cc03cc8461c2b77666557f12135bb82d88a399af94212ee0d5cd8780df22f4ca2daae7cf5aa933c711b725afceab44b25e7aac495e0d_0
2025-07-06 06:39:56,656 - semgrep.rule_match - DEBUG - match_key = ('variables function ... (...,variables,...) {...}\n RegExp("...", ...) RegExp(variables, ...) new RegExp(variables, ...) RegExp(/.../, ...) new RegExp("...", ...) new RegExp(/.../, ...)', PosixPath('client/src/lib/formulaEvaluator.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-regexp') match_id = 7f230b9a0ee21f730937cc03cc8461c2b77666557f12135bb82d88a399af94212ee0d5cd8780df22f4ca2daae7cf5aa933c711b725afceab44b25e7aac495e0d_0
2025-07-06 06:39:56,656 - semgrep.rule_match - DEBUG - match_key = ('variables function ... (...,variables,...) {...}\n RegExp("...", ...) RegExp(variables, ...) new RegExp(variables, ...) RegExp(/.../, ...) new RegExp("...", ...) new RegExp(/.../, ...)', PosixPath('client/src/lib/formulaEvaluator.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-regexp') match_id = 7f230b9a0ee21f730937cc03cc8461c2b77666557f12135bb82d88a399af94212ee0d5cd8780df22f4ca2daae7cf5aa933c711b725afceab44b25e7aac495e0d_0
2025-07-06 06:39:56,656 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = 453fbf9a393bd27e320ca901e09be642516c29f22faac93fb1c7decfa2f8a4d18810e3e9b1533fee2ef40c14dc561a2de11257e935a7ee59948c1985c6869534_0
2025-07-06 06:39:56,657 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_0
2025-07-06 06:39:56,657 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_0
2025-07-06 06:39:56,658 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_0
2025-07-06 06:39:56,658 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = 453fbf9a393bd27e320ca901e09be642516c29f22faac93fb1c7decfa2f8a4d18810e3e9b1533fee2ef40c14dc561a2de11257e935a7ee59948c1985c6869534_0
2025-07-06 06:39:56,659 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_0
2025-07-06 06:39:56,660 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_0
2025-07-06 06:39:56,660 - semgrep.rule_match - DEBUG - match_key = ('"..." + $VALUE\n `...${$VALUE}`\n data.pdfDataUri data.pdfDataUri location.href = data.pdfDataUri location.replace(data.pdfDataUri) this.location.href = data.pdfDataUri this.location.replace(data.pdfDataUri) this.window.location.href = data.pdfDataUri this.window.location.replace(data.pdfDataUri) window.location.href = data.pdfDataUri window.location.replace(data.pdfDataUri) data function ... (..., data, ...) { ... }\n', PosixPath('client/src/pages/billing-page.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.js-open-redirect-from-function') match_id = fa00a1430f0787f259b14d1c08bf43bf14de5b22c130a1d702a574b1607877080fdf4997335e3d42231d9b30fc3d94627482e1ae834085a988674cf60c597fab_1
2025-07-06 06:39:56,660 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 50714787d0eb65dde86a3618155c57368936fcc6eef477f4e2880ce1d335d8d9dda323a1a76457420c239aabc74b34e3be768150b1d02b0cc2b570262fbf844e_0
2025-07-06 06:39:56,661 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,661 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,662 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,663 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 50714787d0eb65dde86a3618155c57368936fcc6eef477f4e2880ce1d335d8d9dda323a1a76457420c239aabc74b34e3be768150b1d02b0cc2b570262fbf844e_0
2025-07-06 06:39:56,663 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,664 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,664 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_1
2025-07-06 06:39:56,665 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 50714787d0eb65dde86a3618155c57368936fcc6eef477f4e2880ce1d335d8d9dda323a1a76457420c239aabc74b34e3be768150b1d02b0cc2b570262fbf844e_0
2025-07-06 06:39:56,666 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,666 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,667 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_2
2025-07-06 06:39:56,668 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 50714787d0eb65dde86a3618155c57368936fcc6eef477f4e2880ce1d335d8d9dda323a1a76457420c239aabc74b34e3be768150b1d02b0cc2b570262fbf844e_0
2025-07-06 06:39:56,669 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,669 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_0
2025-07-06 06:39:56,670 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('client/src/pages/window-projects.tsx'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = fa2ef3129b0f91f81abefbd6ba8fbdca61896c26d856524db68dd38e9a8c3b901ce498b15a364f10e03839d8c876c1f389b9ad34bc3413ca0b84131719cb314e_3
2025-07-06 06:39:56,671 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = a4e4b5dce58f34c9a1496689f4ee2efa0e67ebc4efa58d98fa1c3681eda9b6c475bc40ced4bc29c5d7b65d46ec30497fcaa721bd15ad973945b92cdfd84d35a6_0
2025-07-06 06:39:56,671 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 28eb374c3c740c790f5827e8a2209da8325485de4b8e9178600cafd6259681cb5e8fa9ebdb5fb1cf1d426e79a2585b7c333a923524b34abd54d5c492254b88bf_0
2025-07-06 06:39:56,671 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 28eb374c3c740c790f5827e8a2209da8325485de4b8e9178600cafd6259681cb5e8fa9ebdb5fb1cf1d426e79a2585b7c333a923524b34abd54d5c492254b88bf_0
2025-07-06 06:39:56,672 - semgrep.rule_match - DEBUG - match_key = ('"_shrinkwrap": {\n  ...\n} "dependencies": {\n  ...\n}\n "devDependencies": {\n  ...\n}\n "optionalDependencies": {\n  ...\n}\n "peerDependencies": {\n  ...\n}\n "vite"\\s*:\\s*"(latest|>=\\s*[0-6]\\.\\d+\\.\\d+)" "vite"\\s*:\\s*"[\\^~]?4\\.([0-4]\\.\\d+|5\\.[0-9])" "vite"\\s*:\\s*"[\\^~]?5\\.([0-3]\\.\\d+|4\\.(1[0-4]|[0-9]))" "vite"\\s*:\\s*"[\\^~]?6\\.0\\.(1[01]|[0-9])" "vite"\\s*:\\s*"[\\^~]?6\\.1\\.[01]" "vite"\\s*:\\s*"[\\^~]?6\\.2\\.[0-2]" "vite"\\s*:\\s*"\\^([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])" "vite"\\s*:\\s*"~([0-4]\\.\\d+\\.\\d+|5\\.[0-3]\\.\\d+|5\\.4\\.(1[0-4]|[0-9])|6\\.0\\.(1[01]|[0-9])|6\\.1\\.[01]|6\\.2\\.[0-2])"', PosixPath('package.json'), 'config..semgrep.replit-rules.javascript.vite.security.audit.vite-vulnerability-check') match_id = 28eb374c3c740c790f5827e8a2209da8325485de4b8e9178600cafd6259681cb5e8fa9ebdb5fb1cf1d426e79a2585b7c333a923524b34abd54d5c492254b88bf_0
2025-07-06 06:39:56,673 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-07-06 06:39:56,673 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ca2d0b660c5254f14cf97a7acd54885ed73cc6472b5585ca8950f7b680b4226a97218308ae464b9ae7860d2a10f534db408984929caa75f37dbd1d925a244e34_0
2025-07-06 06:39:56,674 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ca2d0b660c5254f14cf97a7acd54885ed73cc6472b5585ca8950f7b680b4226a97218308ae464b9ae7860d2a10f534db408984929caa75f37dbd1d925a244e34_0
2025-07-06 06:39:56,675 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Monthly Plan</strong>! We\'re excited to have you on board.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Monthly</p>\n          <p><strong>Price:</strong> $12.00/month</p>\n          <p><strong>Billing Cycle:</strong> Monthly</p>\n          <p>Your subscription will automatically renew each month until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ca2d0b660c5254f14cf97a7acd54885ed73cc6472b5585ca8950f7b680b4226a97218308ae464b9ae7860d2a10f534db408984929caa75f37dbd1d925a244e34_0
2025-07-06 06:39:56,675 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-07-06 06:39:56,676 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ead50ed9b3d89a3d54a89b5cdb0b9de87292d5625130fd85179a9c08bb626efede5a339eeb42fcd6fb40722fbb4a335bfce31ec4432c97259f5b213622ab0cfe_0
2025-07-06 06:39:56,677 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ead50ed9b3d89a3d54a89b5cdb0b9de87292d5625130fd85179a9c08bb626efede5a339eeb42fcd6fb40722fbb4a335bfce31ec4432c97259f5b213622ab0cfe_0
2025-07-06 06:39:56,678 - semgrep.rule_match - DEBUG - match_key = (',</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n     .*</?[a-zA-Z] `,</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    ${username}...`\n `...${username},</p>\n          <p>Thank you for subscribing to our <strong>Pro Annual Plan</strong>! We\'re excited to have you on board for the entire year.</p>\n          <p>You now have full access to all the premium features:</p>\n          <ul style="padding-left: 20px;">\n            <li>Unlimited cut piece quantities</li>\n            <li>Advanced optimization algorithms for minimal waste</li>\n            <li>Priority customer support</li>\n            <li>Advanced reporting tools</li>\n          </ul>\n        </div>\n        \n        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">\n          <h3 style="margin-top: 0; color: #3366cc;">Your Subscription Details</h3>\n          <p><strong>Plan:</strong> Pro Annual</p>\n          <p><strong>Price:</strong> $130.00/year (you saved $14 compared to monthly billing)</p>\n          <p><strong>Billing Cycle:</strong> Annual</p>\n          <p>Your subscription will automatically renew each year until canceled.</p>\n        </div>\n        \n        <div style="margin-bottom: 30px;">\n          <p>If you have any questions or need assistance, please don\'t hesitate to contact our support team.</p>\n          <p>Happy cutting!</p>\n          <p>The Aluminum Cutting Optimizer Team</p>\n        </div>\n      </div>\n    `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ead50ed9b3d89a3d54a89b5cdb0b9de87292d5625130fd85179a9c08bb626efede5a339eeb42fcd6fb40722fbb4a335bfce31ec4432c97259f5b213622ab0cfe_0
2025-07-06 06:39:56,678 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.jquery.security.audit.prohibit-jquery-html') match_id = 7c6dac1118cc55ec860a8eea35ef8c5443525fb31a4104281aca751ef4f6efeab5f86c461f87898aed321f49bfe1496a16d0fa489e56d024b18920cdea10e4cb_0
2025-07-06 06:39:56,679 - semgrep.rule_match - DEBUG - match_key = ('template.html("...",...)\n template.html(...)\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.jquery.security.audit.prohibit-jquery-html') match_id = 34dc7bd57345dbf3208d058f642a8e574b5f6d6a522b16ed849f0aba4e5e78312531855ed5452488b8f87b3346e00803628e4640c112aa536ecac3d3ba4d97e3_0
2025-07-06 06:39:56,679 - semgrep.rule_match - DEBUG - match_key = ('template.html("...",...)\n template.html(...)\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.jquery.security.audit.prohibit-jquery-html') match_id = 34dc7bd57345dbf3208d058f642a8e574b5f6d6a522b16ed849f0aba4e5e78312531855ed5452488b8f87b3346e00803628e4640c112aa536ecac3d3ba4d97e3_0
2025-07-06 06:39:56,679 - semgrep.rule_match - DEBUG - match_key = ('template.html("...",...)\n template.html(...)\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.jquery.security.audit.prohibit-jquery-html') match_id = 34dc7bd57345dbf3208d058f642a8e574b5f6d6a522b16ed849f0aba4e5e78312531855ed5452488b8f87b3346e00803628e4640c112aa536ecac3d3ba4d97e3_0
2025-07-06 06:39:56,680 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 252d5e9e3e03042b085042d3b73c68eb69c19f4051b0e0598394b2319328aec1c19bc319257e0271964c432f360d6d25c2037663d24d0ece653155f447f28f20_0
2025-07-06 06:39:56,681 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n             .*</?[a-zA-Z] `</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            ${temporaryPassword}...`\n `...${temporaryPassword}</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 494d83882f910643663da39253a4813101ce6aefe7fa83ccf0c63429dfb19b036404c193d6cc174599f5397c65dfb86fe312b7629327207cbc05cb6e36b6d46e_0
2025-07-06 06:39:56,681 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n             .*</?[a-zA-Z] `</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            ${temporaryPassword}...`\n `...${temporaryPassword}</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 494d83882f910643663da39253a4813101ce6aefe7fa83ccf0c63429dfb19b036404c193d6cc174599f5397c65dfb86fe312b7629327207cbc05cb6e36b6d46e_0
2025-07-06 06:39:56,682 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n             .*</?[a-zA-Z] `</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            ${temporaryPassword}...`\n `...${temporaryPassword}</p>\n              <p>You will be prompted to change your password after logging in.</p>\n              <p>If you did not request this password reset, please contact support immediately.</p>\n            `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 494d83882f910643663da39253a4813101ce6aefe7fa83ccf0c63429dfb19b036404c193d6cc174599f5397c65dfb86fe312b7629327207cbc05cb6e36b6d46e_0
2025-07-06 06:39:56,682 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format') match_id = ba677f2bb05e298e2eb279156b48995fbaf4066dbff4c420ba72dd2c858ef78a6555d4366e8cf5fb6479e556b06b731591ed5273734dc6ef3ebafb0cf50f8f1a_0
2025-07-06 06:39:56,683 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format') match_id = ba677f2bb05e298e2eb279156b48995fbaf4066dbff4c420ba72dd2c858ef78a6555d4366e8cf5fb6479e556b06b731591ed5273734dc6ef3ebafb0cf50f8f1a_0
2025-07-06 06:39:56,683 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format') match_id = ba677f2bb05e298e2eb279156b48995fbaf4066dbff4c420ba72dd2c858ef78a6555d4366e8cf5fb6479e556b06b731591ed5273734dc6ef3ebafb0cf50f8f1a_0
2025-07-06 06:39:56,684 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format') match_id = ba677f2bb05e298e2eb279156b48995fbaf4066dbff4c420ba72dd2c858ef78a6555d4366e8cf5fb6479e556b06b731591ed5273734dc6ef3ebafb0cf50f8f1a_0
2025-07-06 06:39:56,685 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = ed85b2bcc1effb0cb857c259cc3e9438cf0415cfe969cef457739e99a99f6d27ab981d4d7e564d009b459df96f4285ccb9fed1ebb2aaa2168a692f69d01f4646_0
2025-07-06 06:39:56,685 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,686 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,687 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,688 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-07-06 06:39:56,688 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating width formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating width formula for component component.name:`,e,...)\n console.error(``Error evaluating width formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 10a9a13335c953c9dc495f66d2922ae00087e56277976be186d468cb7f8af00334034d26194ca50ac57ac656db71edb52179ad2611534367c326cfccb2315beb_0
2025-07-06 06:39:56,689 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating width formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating width formula for component component.name:`,e,...)\n console.error(``Error evaluating width formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 10a9a13335c953c9dc495f66d2922ae00087e56277976be186d468cb7f8af00334034d26194ca50ac57ac656db71edb52179ad2611534367c326cfccb2315beb_0
2025-07-06 06:39:56,690 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating width formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating width formula for component component.name:`,e,...)\n console.error(``Error evaluating width formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 10a9a13335c953c9dc495f66d2922ae00087e56277976be186d468cb7f8af00334034d26194ca50ac57ac656db71edb52179ad2611534367c326cfccb2315beb_0
2025-07-06 06:39:56,690 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = ed85b2bcc1effb0cb857c259cc3e9438cf0415cfe969cef457739e99a99f6d27ab981d4d7e564d009b459df96f4285ccb9fed1ebb2aaa2168a692f69d01f4646_0
2025-07-06 06:39:56,691 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,692 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,692 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_1
2025-07-06 06:39:56,693 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-07-06 06:39:56,694 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error processing fixed height for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error processing fixed height for component component.name:`,e,...)\n console.error(``Error processing fixed height for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ab61a12d57b3160bfe09db27b376b3a6a77180bacf07c296bc36df902dc15c3520fb010476dcbff601442371b5afe7318b55fd432decd0091e96b676d6fd4a64_0
2025-07-06 06:39:56,695 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error processing fixed height for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error processing fixed height for component component.name:`,e,...)\n console.error(``Error processing fixed height for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ab61a12d57b3160bfe09db27b376b3a6a77180bacf07c296bc36df902dc15c3520fb010476dcbff601442371b5afe7318b55fd432decd0091e96b676d6fd4a64_0
2025-07-06 06:39:56,695 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error processing fixed height for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error processing fixed height for component component.name:`,e,...)\n console.error(``Error processing fixed height for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ab61a12d57b3160bfe09db27b376b3a6a77180bacf07c296bc36df902dc15c3520fb010476dcbff601442371b5afe7318b55fd432decd0091e96b676d6fd4a64_0
2025-07-06 06:39:56,696 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = ed85b2bcc1effb0cb857c259cc3e9438cf0415cfe969cef457739e99a99f6d27ab981d4d7e564d009b459df96f4285ccb9fed1ebb2aaa2168a692f69d01f4646_0
2025-07-06 06:39:56,697 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,697 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_0
2025-07-06 06:39:56,698 - semgrep.rule_match - DEBUG - match_key = ('eval("...") eval(...)', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.browser.security.eval-detected') match_id = 46033f57ce63a57fc88532e5dc7a6c231dc309f903fd14249181291ad1cd90b05f9e39b2210670deab83d31836565ee5bc84bf0af39b3781af36f742e01be661_2
2025-07-06 06:39:56,699 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-07-06 06:39:56,700 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating height formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating height formula for component component.name:`,e,...)\n console.error(``Error evaluating height formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f2ce471e3d90e4bd7503516251a1e2a74ab674cca4aa21fab564eff896d2558d34fd877f0c49e4af4089bfb72218ada4befc8a9bc631a4cbdc54f4b2c3653004_0
2025-07-06 06:39:56,700 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating height formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating height formula for component component.name:`,e,...)\n console.error(``Error evaluating height formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f2ce471e3d90e4bd7503516251a1e2a74ab674cca4aa21fab564eff896d2558d34fd877f0c49e4af4089bfb72218ada4befc8a9bc631a4cbdc54f4b2c3653004_0
2025-07-06 06:39:56,701 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error evaluating height formula for component component.name:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error evaluating height formula for component component.name:`,e,...)\n console.error(``Error evaluating height formula for component component.name:`,e,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = f2ce471e3d90e4bd7503516251a1e2a74ab674cca4aa21fab564eff896d2558d34fd877f0c49e4af4089bfb72218ada4befc8a9bc631a4cbdc54f4b2c3653004_0
2025-07-06 06:39:56,702 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/stripe.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = ce10db48ea1d824f8d53f16fec91d9a17e80e4081b4c4fa21fd4009c74f3847db71c726deeab19718ce6995d4279159a018028028ab39d6fdb338f1456b8ce29_0
2025-07-06 06:39:56,702 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error creating period subscription:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error creating period subscription:`,error,...)\n console.error(``Error creating period subscription:`,error,...)\n', PosixPath('server/stripe.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 94bc5954f388a622253fe017c5381847d3fb4451e8f5d10a8cefa3134ed448bb52142ae311bd2d7ab83275da9a0cc18d90572bf493d3d4ca9eea77bc5a370b1a_0
2025-07-06 06:39:56,702 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error creating period subscription:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error creating period subscription:`,error,...)\n console.error(``Error creating period subscription:`,error,...)\n', PosixPath('server/stripe.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 94bc5954f388a622253fe017c5381847d3fb4451e8f5d10a8cefa3134ed448bb52142ae311bd2d7ab83275da9a0cc18d90572bf493d3d4ca9eea77bc5a370b1a_0
2025-07-06 06:39:56,703 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error creating period subscription:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error creating period subscription:`,error,...)\n console.error(``Error creating period subscription:`,error,...)\n', PosixPath('server/stripe.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 94bc5954f388a622253fe017c5381847d3fb4451e8f5d10a8cefa3134ed448bb52142ae311bd2d7ab83275da9a0cc18d90572bf493d3d4ca9eea77bc5a370b1a_0
2025-07-06 06:39:56,704 - semgrep.core_runner - DEBUG - semgrep ran in 0:00:17.579490 on 142 files
2025-07-06 06:39:56,705 - semgrep.core_runner - DEBUG - findings summary: 16 warning, 2 error, 6 info
2025-07-06 06:39:56,707 - semgrep.app.auth - DEBUG - Getting API token from settings file
2025-07-06 06:39:56,708 - semgrep.app.auth - DEBUG - No API token found in settings file
2025-07-06 06:39:56,805 - semgrep.output - VERBOSE - 
========================================
Files skipped:
========================================

  Always skipped by Opengrep:

   • <none>

  Skipped by .gitignore:
  (Disable by passing --no-git-ignore)

   • <all files not listed by `git ls-files` were skipped>

  Skipped by .semgrepignore:
  - https://semgrep.dev/docs/ignoring-files-folders-code/#understand-semgrep-defaults

   • <none>

  Skipped by --include patterns:

   • <none>

  Skipped by --exclude patterns:

   • <none>

  Files skipped due to insufficient read permissions:

   • <none>

  Skipped by limiting to files smaller than 1000000 bytes:
  (Adjust with the --max-target-bytes flag)

   • <none>

  Partially analyzed due to parsing or internal Opengrep errors

   • attached_assets/index-static.html (1 lines skipped)
   • tailwind.config.ts (1 lines skipped)

2025-07-06 06:39:56,806 - semgrep.output - INFO - Some files were skipped or only partially analyzed.
  Scan was limited to files tracked by git.
  Partially scanned: 2 files only partially analyzed due to parsing or internal Opengrep errors

Ran 438 rules on 142 files: 24 findings.
2025-07-06 06:39:56,806 - semgrep.app.version - DEBUG - Version cache does not exist
2025-07-06 06:39:56,818 - semgrep.metrics - VERBOSE - Not sending pseudonymous metrics since metrics are configured to OFF and registry usage is False
