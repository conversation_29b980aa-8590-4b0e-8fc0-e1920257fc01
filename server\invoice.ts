import { Invoice } from "@shared/schema";
import { jsPDF } from "jspdf";
import fs from "fs";
import path from "path";

// Define the format for invoice data including user information
interface InvoiceData extends Invoice {
  user: {
    name: string;
    companyName?: string | null;
    email?: string | null;
    country?: string | null;
  };
}

/**
 * Generates an invoice PDF for a user
 * @param invoiceData The invoice data to use
 * @returns The PDF file as a base64 string
 */
export async function generateInvoicePdf(invoiceData: InvoiceData): Promise<string> {
  // Create a new PDF document
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4"
  });
  
  // Set up document properties
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 20;
  const contentWidth = pageWidth - 2 * margin;
  
  // Add Window Craft Pro logo
  try {
    // Try different paths to find the logo
    const possiblePaths = [
      path.join(process.cwd(), "public", "Logo.png"),
      path.join(process.cwd(), "attached_assets", "Logo.png")
    ];
    
    let logoBase64 = "";
    let logoFound = false;
    
    for (const logoPath of possiblePaths) {
      if (fs.existsSync(logoPath)) {
        logoBase64 = fs.readFileSync(logoPath, { encoding: "base64" });
        // Increased height from 15 to 20 as requested
        doc.addImage("data:image/png;base64," + logoBase64, "PNG", margin, margin, 40, 20);
        logoFound = true;
        break;
      }
    }
    
    if (!logoFound) {
      console.warn("Logo not found in any of the expected paths, using embedded backup logo");
      
      // Embedded base64 logo (first portion for space reasons)
      const fallbackLogoBase64 = "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**************************+jq0MZ3+XsRGEVXTjOII8sn9I+DbtMgxRfSuHLRKPkzIa9DkDOmBUqI/mX27p7sj9mXo2LgLeCLFiqRYfQkehHH4Qxgac14ymAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAaDwWAwGAwGg8FgMBgMBoPBYDAYDAbjdPNfTFEEVrjRUQcAAAAASUVORK5CYII=";
      
      try {
        doc.addImage("data:image/png;base64," + fallbackLogoBase64, "PNG", margin, margin, 40, 20);
      } catch(error) {
        console.error("Error adding embedded logo to PDF:", error);
        // Fallback to text if embedded logo fails too
        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.setTextColor(30, 30, 30);
        doc.text("Window Craft Pro", margin, margin + 10);
        doc.setFontSize(10);
        doc.setFont("helvetica", "normal");
        doc.text("PRECISION DESIGN, SMARTER OPTIMIZATION", margin, margin + 15);
      }
    }
  } catch (error) {
    console.error("Error adding logo to PDF:", error);
  }
  
  // Set font styles
  doc.setFont("helvetica", "bold");
  doc.setFontSize(22);
  doc.setTextColor(30, 30, 30);
  
  // Add invoice title
  doc.text("INVOICE", pageWidth - margin, margin + 10, { align: "right" });
  
  // Add invoice details
  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  doc.setTextColor(80, 80, 80);
  
  const invoiceDetails = [
    `Invoice Number: ${invoiceData.invoiceNumber}`,
    `Date: ${new Date(invoiceData.issuedDate).toLocaleDateString()}`,
    `Due Date: ${invoiceData.dueDate ? new Date(invoiceData.dueDate).toLocaleDateString() : "N/A"}`,
    `Status: ${invoiceData.status.toUpperCase()}`
  ];
  
  let yPos = margin + 15;
  invoiceDetails.forEach(detail => {
    doc.text(detail, pageWidth - margin, yPos, { align: "right" });
    yPos += 5;
  });
  
  // Add company information
  doc.setFontSize(10);
  doc.setTextColor(80, 80, 80);
  yPos = margin + 25;
  
  const companyInfo = [
    "Window Craft Pro",
    "Mumbai, INDIA",
    "Email: <EMAIL>",
    "Tel: 0091 - 9892615588 & 00966 - *********"
  ];
  
  companyInfo.forEach(line => {
    doc.text(line, margin, yPos);
    yPos += 5;
  });
  
  // Add line separator
  yPos += 5;
  doc.setDrawColor(220, 220, 220);
  doc.line(margin, yPos, pageWidth - margin, yPos);
  
  // Add billing information section
  yPos += 10;
  doc.setFont("helvetica", "bold");
  doc.setFontSize(12);
  doc.setTextColor(30, 30, 30);
  doc.text("Billed To:", margin, yPos);
  
  // Customer information
  yPos += 7;
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.setTextColor(80, 80, 80);
  
  const customerInfo = [
    invoiceData.user.name,
    invoiceData.user.companyName || "",
    invoiceData.user.email || "",
    invoiceData.user.country || ""
  ].filter(Boolean); // Remove empty strings
  
  customerInfo.forEach(line => {
    doc.text(line, margin, yPos);
    yPos += 5;
  });
  
  // Add invoice description section
  yPos += 10;
  doc.setFont("helvetica", "bold");
  doc.setFontSize(12);
  doc.setTextColor(30, 30, 30);
  doc.text("Invoice Details", margin, yPos);
  
  // Table header
  yPos += 10;
  const columnWidths = [contentWidth * 0.5, contentWidth * 0.25, contentWidth * 0.25];
  const headers = ["Description", "Period", "Amount"];
  
  doc.setFillColor(240, 240, 240);
  doc.rect(margin, yPos - 5, contentWidth, 7, "F");
  
  let xPos = margin;
  headers.forEach((header, i) => {
    doc.text(header, xPos, yPos);
    xPos += columnWidths[i];
  });
  
  // Table content - invoice items
  yPos += 7;
  doc.setFont("helvetica", "normal");
  doc.setTextColor(80, 80, 80);
  
  // Handle invoice items if they exist
  if (invoiceData.items && Array.isArray(invoiceData.items)) {
    invoiceData.items.forEach(item => {
      xPos = margin;
      
      // Description column
      doc.text(item.description || invoiceData.description, xPos, yPos, { 
        maxWidth: columnWidths[0] - 5 
      });
      
      // Period column
      xPos += columnWidths[0];
      doc.text(item.period || invoiceData.subscriptionPeriod || "-", xPos, yPos);
      
      // Amount column
      xPos += columnWidths[1];
      const formattedAmount = typeof item.amount === 'number' 
        ? `$${item.amount.toFixed(2)}` 
        : `$${invoiceData.amount}`;
      doc.text(formattedAmount, xPos, yPos);
      
      yPos += 10;
    });
  } else {
    // If no items, just show the main invoice details
    xPos = margin;
    
    // Description column
    doc.text(invoiceData.description, xPos, yPos, { 
      maxWidth: columnWidths[0] - 5 
    });
    
    // Period column
    xPos += columnWidths[0];
    doc.text(invoiceData.subscriptionPeriod || "-", xPos, yPos);
    
    // Amount column
    xPos += columnWidths[1];
    doc.text(`$${invoiceData.amount}`, xPos, yPos);
    
    yPos += 10;
  }
  
  // Add line separator
  yPos += 5;
  doc.setDrawColor(220, 220, 220);
  doc.line(margin, yPos, pageWidth - margin, yPos);
  
  // Add total amount
  yPos += 10;
  doc.setFont("helvetica", "bold");
  doc.setTextColor(30, 30, 30);
  doc.text("Total:", pageWidth - margin - columnWidths[2], yPos);
  doc.text(`$${invoiceData.amount}`, pageWidth - margin, yPos, { align: "right" });
  
  // Add payment status
  yPos += 10;
  if (invoiceData.status === "paid") {
    doc.setTextColor(46, 125, 50); // Green color for paid
    doc.text("PAID", pageWidth - margin, yPos, { align: "right" });
  } else {
    doc.setTextColor(211, 47, 47); // Red color for unpaid
    doc.text("UNPAID", pageWidth - margin, yPos, { align: "right" });
  }
  
  // Payment information section removed as requested
  
  // Add footer with terms and conditions
  yPos = pageHeight - margin - 30;
  doc.setFontSize(9);
  doc.setTextColor(120, 120, 120);
  
  const termsText = [
    "Terms & Conditions",
    "1. Payment is due within 15 days of invoice date.",
    "2. Please include invoice number with your payment.",
    "3. For any questions about this invoice, <NAME_EMAIL>"
  ];
  
  termsText.forEach(line => {
    doc.text(line, margin, yPos);
    yPos += 5;
  });
  
  // Add thank you note
  yPos = pageHeight - margin - 5;
  doc.setFont("helvetica", "bold");
  doc.setTextColor(30, 30, 30);
  doc.text("Thank you for your business!", pageWidth / 2, yPos, { align: "center" });
  
  // Convert PDF to base64 string and add custom filename
  try {
    // Create a PDF filename based on invoice number
    const fileName = `Window-Craft-Pro-Invoice-${invoiceData.invoiceNumber.replace(/[\/\\:*?"<>|]/g, '-')}.pdf`;
    
    // Get PDF as data URI
    const pdfOutput = doc.output('datauristring');
    
    // Return both the data URI and suggested filename
    return pdfOutput;
  } catch (error) {
    console.error("Error generating PDF:", error);
    return doc.output('datauristring');
  }
}