import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { OptimizationResult, StockSettings, ProfileResult, CuttingPlan } from "@/lib/types";
import { RefreshCw, Download, Printer } from "lucide-react";
import { useState } from "react";
import jsPDF from "jspdf";

interface OptimizationResultsProps {
  result: OptimizationResult;
  stockSettings: StockSettings;
  onReoptimize: () => void;
}

// Function to generate a color for a profile type
function getProfileColor(profileType: string, opacity: number = 1): string {
  // Simple hash function to generate a consistent color for the same profile type
  let hash = 0;
  for (let i = 0; i < profileType.length; i++) {
    hash = profileType.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Generate HSL color with high saturation and medium lightness
  const h = Math.abs(hash) % 360;
  const s = 70; // High saturation
  const l = 60; // Medium lightness to ensure readability
  
  return `hsla(${h}, ${s}%, ${l}%, ${opacity})`;
}

export default function OptimizationResults({ 
  result,
  stockSettings,
  onReoptimize
}: OptimizationResultsProps) {
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };
  
  const downloadPdf = async () => {
    setIsGeneratingPdf(true);
    
    try {
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const margin = 10;
      
      // Add title
      doc.setFontSize(18);
      doc.text("Cutting Optimization Results", margin, 20);
      
      // Add date
      doc.setFontSize(10);
      doc.text(`Generated on: ${new Date().toLocaleString()}`, margin, 30);
      
      // Add stock settings
      doc.setFontSize(12);
      doc.text("Stock Settings:", margin, 40);
      doc.setFontSize(10);
      doc.text(`Stock Length: ${stockSettings.stockLength}mm`, margin + 5, 45);
      doc.text(`Kerf Width: ${stockSettings.kerf}mm`, margin + 5, 50);
      doc.text(`End Trim: ${stockSettings.endTrim}mm`, margin + 5, 55);
      
      // Add summary
      let y = 65;
      doc.setFontSize(12);
      doc.text("Optimization Summary:", margin, y);
      y += 8;
      
      // Calculate total material usage and waste
      const totalMaterial = result.profileResults.reduce((sum, profile) => 
        sum + (profile.stockPiecesCount * stockSettings.stockLength), 0);
      
      const totalWaste = result.profileResults.reduce((sum, profile) => 
        sum + profile.totalWaste, 0);
      
      const utilizationPercent = totalMaterial > 0 
        ? ((totalMaterial - totalWaste) / totalMaterial) * 100 
        : 0;
      
      const wastePercent = totalMaterial > 0 
        ? (totalWaste / totalMaterial) * 100 
        : 0;
      
      doc.setFontSize(10);
      doc.text(`Total Stock Pieces Required: ${result.totalStockPieces}`, margin + 5, y);
      y += 5;
      doc.text(`Total Material Utilization: ${utilizationPercent.toFixed(1)}%`, margin + 5, y);
      y += 5;
      doc.text(`Total Material Waste: ${wastePercent.toFixed(1)}% (${totalWaste.toFixed(0)}mm)`, margin + 5, y);
      y += 10;
      
      // Profile results
      result.profileResults.forEach(profileResult => {
        if (y > pageHeight - 30) {
          doc.addPage();
          y = 20;
        }
        
        doc.setFontSize(11);
        doc.text(`${profileResult.profileType} Profile:`, margin + 5, y);
        y += 5;
        
        doc.setFontSize(10);
        doc.text(`Stock Pieces: ${profileResult.stockPiecesCount}`, margin + 10, y);
        y += 5;
        doc.text(`Material Usage: ${formatPercentage(profileResult.materialUsagePercent)}`, margin + 10, y);
        y += 5;
        doc.text(`Total Waste: ${profileResult.totalWaste}mm`, margin + 10, y);
        y += 5;
        doc.text(`Total Cuts: ${profileResult.totalCuts}`, margin + 10, y);
        y += 10;
      });
      
      // Add cutting plans for each profile
      result.profileResults.forEach(profileResult => {
        if (y > pageHeight - 20) {
          doc.addPage();
          y = 20;
        }
        
        doc.setFontSize(12);
        doc.text(`${profileResult.profileType} Profile Cutting Plans:`, margin, y);
        y += 10;
        
        doc.setFontSize(9);
        
        profileResult.cuttingPlans.forEach((plan, planIndex) => {
          if (y > pageHeight - 20) {
            doc.addPage();
            y = 20;
          }
          
          doc.text(`Stock Piece #${planIndex + 1}:`, margin, y);
          y += 5;
          
          const tableWidth = pageWidth - (margin * 2);
          const cellPadding = 3;
          const colWidths = [35, 35, tableWidth - 140, 70];
          
          // Draw table headers
          doc.setFillColor(240, 240, 240);
          doc.rect(margin, y, tableWidth, 7, 'F');
          doc.setTextColor(0, 0, 0);
          doc.text("Cut Order", margin + cellPadding, y + 5);
          doc.text("Length", margin + colWidths[0] + cellPadding, y + 5);
          doc.text("Description", margin + colWidths[0] + colWidths[1] + cellPadding, y + 5);
          doc.text("Position", margin + colWidths[0] + colWidths[1] + colWidths[2] + cellPadding, y + 5);
          y += 7;
          
          // Draw table rows
          plan.cuts.forEach((cut, cutIndex) => {
            // Calculate position
            const previousCuts = plan.cuts.slice(0, cutIndex);
            const startPos = previousCuts.reduce((sum, c) => sum + c.length + stockSettings.kerf, 0);
            const endPos = startPos + cut.length;
            
            // Draw row
            doc.rect(margin, y, tableWidth, 7);
            doc.text(`${cutIndex + 1}`, margin + cellPadding, y + 5);
            doc.text(`${cut.length}mm`, margin + colWidths[0] + cellPadding, y + 5);
            doc.text(`${cut.description || 'No description'}`, margin + colWidths[0] + colWidths[1] + cellPadding, y + 5);
            doc.text(`${startPos}-${endPos}mm`, margin + colWidths[0] + colWidths[1] + colWidths[2] + cellPadding, y + 5);
            y += 7;
          });
          
          // Add material usage
          doc.text(`Material Usage: ${formatPercentage(1 - plan.waste / stockSettings.stockLength)} (${stockSettings.stockLength - plan.waste}mm used, ${plan.waste}mm waste)`, margin, y + 5);
          y += 15;
        });
      });
      
      doc.save('cutting-optimization-results.pdf');
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGeneratingPdf(false);
    }
  };
  
  const printCuttingPlan = () => {
    setIsPrinting(true);
    
    setTimeout(() => {
      try {
        window.print();
      } catch (error) {
        console.error('Error printing:', error);
      } finally {
        setIsPrinting(false);
      }
    }, 100);
  };
  
  // Render a single profile result card
  const renderProfileResultCard = (profileResult: ProfileResult) => {
    const bgColor = getProfileColor(profileResult.profileType, 0.1);
    const textColor = getProfileColor(profileResult.profileType, 1);
    
    return (
      <div 
        key={profileResult.profileType}
        className="p-4 rounded-lg border mb-4"
        style={{ backgroundColor: bgColor, borderColor: textColor }}
      >
        <h3 className="text-lg font-bold mb-3" style={{ color: textColor }}>
          {profileResult.profileType} Profile
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Stock Pieces</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {profileResult.stockPiecesCount}
            </p>
          </div>
          
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Material Usage</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {formatPercentage(profileResult.materialUsagePercent)}
            </p>
          </div>
          
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Waste</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {profileResult.totalWaste} mm
            </p>
          </div>
          
          <div className="bg-white p-3 rounded shadow-sm">
            <h4 className="text-neutral-600 text-xs font-medium mb-1">Cuts</h4>
            <p className="text-xl font-semibold" style={{ color: textColor }}>
              {profileResult.totalCuts}
            </p>
          </div>
        </div>
      </div>
    );
  };
  
  // Render cutting plans for a profile
  const renderProfileCuttingPlans = (profileResult: ProfileResult) => {
    const bgColor = getProfileColor(profileResult.profileType, 0.1);
    const textColor = getProfileColor(profileResult.profileType, 1);
    
    return (
      <div key={`plans-${profileResult.profileType}`} className="space-y-6 mb-6">
        <h3 
          className="text-lg font-medium p-2 rounded border-l-4"
          style={{ 
            backgroundColor: bgColor, 
            borderLeftColor: textColor,
            color: textColor
          }}
        >
          {profileResult.profileType} Profile Cutting Plans
        </h3>
        
        {profileResult.cuttingPlans.length === 0 ? (
          <p className="text-neutral-600 italic">No {profileResult.profileType} profile pieces to optimize.</p>
        ) : (
          profileResult.cuttingPlans.map((plan, planIndex) => (
            <div key={planIndex} className="space-y-2">
              <p className="font-medium text-neutral-900">Stock Piece #{planIndex + 1}</p>
              
              <div className="stock-visualization">
                {/* Cut Pieces */}
                {plan.cuts.map((cut, cutIndex) => {
                  // Calculate position and width as percentage of stock length
                  const previousCuts = plan.cuts.slice(0, cutIndex);
                  const previousLength = previousCuts.reduce((sum, c) => sum + c.length, 0);
                  const position = (previousLength / stockSettings.stockLength) * 100;
                  const width = (cut.length / stockSettings.stockLength) * 100;
                  
                  return (
                    <div 
                      key={cutIndex}
                      className="cut-piece" 
                      style={{ 
                        left: `${position}%`, 
                        width: `${width}%`,
                        backgroundColor: textColor
                      }}
                    >
                      <span className="text-white text-xs font-medium">
                        {cut.length}mm
                      </span>
                    </div>
                  );
                })}
                
                {/* Waste */}
                {plan.waste > 0 && (
                  <div 
                    className="waste-piece" 
                    style={{ width: `${(plan.waste / stockSettings.stockLength) * 100}%` }}
                  >
                    <span className="text-white text-xs font-medium">
                      {plan.waste}mm
                    </span>
                  </div>
                )}
              </div>
              
              <table className="min-w-full border-collapse">
                <thead>
                  <tr className="bg-neutral-100">
                    <th className="px-3 py-2 border border-neutral-300 text-left text-xs">Cut Order</th>
                    <th className="px-3 py-2 border border-neutral-300 text-left text-xs">Length</th>
                    <th className="px-3 py-2 border border-neutral-300 text-left text-xs">Description</th>
                    <th className="px-3 py-2 border border-neutral-300 text-left text-xs">Position</th>
                  </tr>
                </thead>
                <tbody>
                  {plan.cuts.map((cut, cutIndex) => {
                    // Calculate position
                    const previousCuts = plan.cuts.slice(0, cutIndex);
                    const startPos = previousCuts.reduce((sum, c) => sum + c.length + stockSettings.kerf, 0);
                    const endPos = startPos + cut.length;
                    
                    return (
                      <tr key={cutIndex}>
                        <td className="px-3 py-2 border border-neutral-300 text-sm">{cutIndex + 1}</td>
                        <td className="px-3 py-2 border border-neutral-300 text-sm">{cut.length}mm</td>
                        <td className="px-3 py-2 border border-neutral-300 text-sm">{cut.description || 'No description'}</td>
                        <td className="px-3 py-2 border border-neutral-300 text-sm">{startPos}-{endPos}mm</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              
              <div className="text-sm text-neutral-600">
                <span className="font-medium">Material Usage:</span> 
                {formatPercentage(1 - plan.waste / stockSettings.stockLength)} 
                ({stockSettings.stockLength - plan.waste}mm used, {plan.waste}mm waste)
              </div>
            </div>
          ))
        )}
      </div>
    );
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-primary">Optimization Results</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Results Summary */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-primary bg-opacity-10 p-4 rounded-lg text-center">
              <h3 className="text-primary text-sm font-bold mb-1">Total Stock Pieces</h3>
              <p className="text-primary text-3xl font-semibold">{result.totalStockPieces}</p>
            </div>
            
            {/* Calculate and show overall material utilization */}
            {(() => {
              // Calculate total used material and total waste across all profiles
              const totalMaterial = result.profileResults.reduce((sum, profile) => 
                sum + (profile.stockPiecesCount * stockSettings.stockLength), 0);
              
              const totalWaste = result.profileResults.reduce((sum, profile) => 
                sum + profile.totalWaste, 0);
              
              const utilizationPercent = totalMaterial > 0 
                ? ((totalMaterial - totalWaste) / totalMaterial) * 100 
                : 0;
              
              const wastePercent = totalMaterial > 0 
                ? (totalWaste / totalMaterial) * 100 
                : 0;
              
              return (
                <>
                  <div className="bg-green-100 p-4 rounded-lg text-center">
                    <h3 className="text-green-700 text-sm font-bold mb-1">Total Material Utilization</h3>
                    <p className="text-green-700 text-3xl font-semibold">{utilizationPercent.toFixed(1)}%</p>
                  </div>
                  
                  <div className="bg-red-100 p-4 rounded-lg text-center">
                    <h3 className="text-red-700 text-sm font-bold mb-1">Total Material Waste</h3>
                    <p className="text-red-700 text-3xl font-semibold">{wastePercent.toFixed(1)}%</p>
                    <p className="text-red-600 text-sm">{totalWaste.toFixed(0)}mm total</p>
                  </div>
                </>
              );
            })()}
          </div>
          
          {/* Profile Results Summary Cards */}
          <div className="space-y-4">
            {result.profileResults.map(profileResult => renderProfileResultCard(profileResult))}
          </div>
        </div>
        
        {/* Cutting Plans Visualization for each profile */}
        {result.profileResults.map(profileResult => renderProfileCuttingPlans(profileResult))}
        
        <div className="flex flex-wrap gap-4 mt-6">
          <Button
            onClick={downloadPdf}
            disabled={isGeneratingPdf}
            className="bg-neutral-600 text-white hover:bg-neutral-700 flex items-center"
          >
            <Download className="h-5 w-5 mr-2" />
            {isGeneratingPdf ? 'Generating PDF...' : 'Download PDF'}
          </Button>
          
          <Button
            onClick={printCuttingPlan}
            disabled={isPrinting}
            className="bg-neutral-600 text-white hover:bg-neutral-700 flex items-center"
          >
            <Printer className="h-5 w-5 mr-2" />
            {isPrinting ? 'Preparing...' : 'Print Cutting Plan'}
          </Button>
          
          <Button
            onClick={onReoptimize}
            variant="outline"
            className="flex items-center"
          >
            <RefreshCw className="h-5 w-5 mr-2" />
            Re-Optimize
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}