import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { CheckCircle, Crown } from "lucide-react";

const upgradeRequestSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(6, "Phone number must be at least 6 characters"),
  companyName: z.string().optional(),
  message: z.string().min(5, "Please provide some details about your request"),
});

type UpgradeRequestData = z.infer<typeof upgradeRequestSchema>;

export default function ProUpgradeRequestForm() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<UpgradeRequestData>({
    resolver: zodResolver(upgradeRequestSchema),
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      phone: "",
      companyName: "",
      message: "I'd like to upgrade to the Pro plan to optimize more aluminum cutting projects."
    }
  });

  const handleSubmit = async (data: UpgradeRequestData) => {
    setIsSubmitting(true);
    
    try {
      const response = await apiRequest("POST", "/api/upgrade-request", {
        ...data,
        userId: user?.id,
        username: user?.username
      });
      
      if (response.ok) {
        setIsSuccess(true);
        toast({
          title: "Request Sent",
          description: "Your upgrade request has been sent successfully. We'll contact you soon.",
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to send upgrade request");
      }
    } catch (error) {
      toast({
        title: "Request Failed",
        description: error instanceof Error ? error.message : "There was a problem sending your request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSuccess) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-primary">Request Submitted</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <CheckCircle className="w-16 h-16 mx-auto text-green-500 mb-4" />
          <p className="mb-4">
            Thank you for your interest in our Pro plan! Your request has been successfully submitted.
            We'll review your application and get back to you shortly via your provided contact details.
          </p>
          <Button onClick={() => setIsDialogOpen(false)}>Close</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button className="w-full bg-gradient-to-r from-amber-500 to-amber-700">
          <Crown className="w-4 h-4 mr-2" />
          Upgrade to Pro
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <Card className="border-0 shadow-none">
          <CardHeader>
            <CardTitle className="text-primary flex items-center gap-2">
              <Crown className="w-5 h-5 text-amber-500" />
              Upgrade to Pro Plan
            </CardTitle>
            <CardDescription>
              Get access to Window Calculator, Profile Manager, Accessories Manager, 
              and Window Projects features with a Pro subscription.
            </CardDescription>
            <div className="mt-2 pt-2 border-t text-sm">
              <div className="font-medium">Pro Plan Includes:</div>
              <ul className="pl-5 mt-1 list-disc text-muted-foreground space-y-1">
                <li>Access to all premium features</li>
                <li>Unlimited cut piece quantity</li>
                <li>Advanced material optimization</li>
                <li>Priority support</li>
              </ul>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input 
                  id="name"
                  type="text" 
                  placeholder="Your name"
                  {...form.register("name")}
                />
                {form.formState.errors.name && (
                  <p className="text-red-500 text-sm">{form.formState.errors.name.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input 
                  id="email"
                  type="email" 
                  placeholder="<EMAIL>"
                  {...form.register("email")}
                />
                {form.formState.errors.email && (
                  <p className="text-red-500 text-sm">{form.formState.errors.email.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input 
                  id="phone"
                  type="tel" 
                  placeholder="Your phone number"
                  {...form.register("phone")}
                />
                {form.formState.errors.phone && (
                  <p className="text-red-500 text-sm">{form.formState.errors.phone.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="companyName">Company Name (Optional)</Label>
                <Input 
                  id="companyName"
                  type="text" 
                  placeholder="Your company"
                  {...form.register("companyName")}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea 
                  id="message"
                  placeholder="Describe your requirements"
                  rows={3}
                  {...form.register("message")}
                />
                {form.formState.errors.message && (
                  <p className="text-red-500 text-sm">{form.formState.errors.message.message}</p>
                )}
              </div>
              
              <div className="flex justify-end space-x-2 pt-2">
                <Button variant="outline" type="button" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Sending..." : "Send Request"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}