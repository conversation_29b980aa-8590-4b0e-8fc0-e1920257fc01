import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import enCommon from '../locales/en/common.json';
import arCommon from '../locales/ar/common.json';

// For browser environments, safely access localStorage
let storedLanguage = 'en';
try {
  if (typeof window !== 'undefined' && window.localStorage) {
    storedLanguage = localStorage.getItem('language') || 'en';
  }
} catch (error) {
  console.error('Error accessing localStorage:', error);
}

// Initialize i18next
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        // Put translations directly at the root level
        translation: enCommon,
      },
      ar: {
        // Put translations directly at the root level
        translation: arCommon,
      },
    },
    lng: storedLanguage,
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Debug mode to see what's happening with translations
    debug: true,
  });

// Log available languages and namespaces for debugging
console.log('i18n configuration:', {
  languages: Object.keys(i18n.options.resources || {}),
  currentLanguage: i18n.language,
  availableNamespaces: i18n.options.ns,
  defaultNamespace: i18n.options.defaultNS
});

// Export a translation helper function for direct usage
export const t = (key: string) => i18n.t(key);

export default i18n;