import pg from 'pg';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();
const { Pool } = pg;

// Load database connection string from environment variables
const connectionString = process.env.DATABASE_URL;

// Create a connection pool
const pool = new Pool({
  connectionString
});

async function setupDatabase() {
  const client = await pool.connect();
  try {
    console.log('Connected to database');
    
    // Create invoices table
    console.log('Creating invoices table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS invoices (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        invoice_number TEXT NOT NULL UNIQUE,
        amount NUMERIC NOT NULL,
        currency TEXT NOT NULL DEFAULT 'USD',
        status TEXT NOT NULL DEFAULT 'paid',
        description TEXT NOT NULL,
        subscription_plan TEXT NOT NULL,
        subscription_period TEXT NOT NULL,
        issued_date TIMESTAMP NOT NULL DEFAULT NOW(),
        due_date TIMESTAMP,
        paid_date TIMESTAMP,
        payment_method TEXT NOT NULL DEFAULT 'credit_card',
        billing_address JSONB,
        items JSONB,
        stripe_invoice_id TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);
    console.log('Invoices table created or already exists');

    console.log('Database setup complete');
  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

setupDatabase();