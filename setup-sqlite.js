import { execSync } from 'child_process';
import { existsSync, writeFileSync } from 'fs';

console.log('🚀 Setting up SQLite database (No installation required)...\n');

// Create SQLite environment configuration
function createSQLiteEnv() {
  const sqliteEnv = `# Local SQLite Database Configuration (No installation required)
DATABASE_URL=file:./local_database.db
PGDATABASE=aluminum_optimizer
PGHOST=localhost
PGPORT=5432
PGUSER=admin
PGPASSWORD=admin123

# Backup - NeonDB Configuration (commented out)
# DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
# PGDATABASE=neondb
# PGHOST=ep-quiet-cake-a6kq63uf.us-west-2.aws.neon.tech
# PGPORT=5432
# PGUSER=neondb_owner
# PGPASSWORD=npg_ynP9Ot1pNkRA

# Stripe Configuration (placeholder values for development)
STRIPE_SECRET_KEY=sk_test_placeholder
STRIPE_PUBLISHABLE_KEY=pk_test_placeholder

# SendGrid Configuration (optional)
SENDGRID_API_KEY=placeholder

# OpenAI Configuration (optional)
OPENAI_API_KEY=placeholder

# Session Secret
SESSION_SECRET=your-session-secret-key-here`;

  writeFileSync('.env.sqlite', sqliteEnv);
  console.log('✅ Created .env.sqlite configuration');
}

// Update database configuration for SQLite
function updateDbConfig() {
  const sqliteDbConfig = `import dotenv from "dotenv";
dotenv.config();

// Check if we're using SQLite, local PostgreSQL or Neon
const isSQLite = process.env.DATABASE_URL?.startsWith('file:');
const isLocalDB = process.env.PGHOST === 'localhost' || process.env.DATABASE_URL?.includes('localhost');

if (isSQLite) {
  console.log('Using SQLite database');
} else if (isLocalDB) {
  console.log('Using local PostgreSQL database');
} else {
  console.log('Using Neon serverless database');
}

// Import the appropriate modules based on database type
let Pool, drizzle;
let schema;

if (isSQLite) {
  // Use SQLite for local development
  const { drizzle: sqliteDrizzle } = require('drizzle-orm/better-sqlite3');
  const Database = require('better-sqlite3');
  
  const sqlite = new Database('./local_database.db');
  schema = require("@shared/schema");
  
  // Export SQLite database
  export const db = sqliteDrizzle(sqlite, { schema });
  export const pool = null; // SQLite doesn't use connection pools
  
} else if (isLocalDB) {
  // Use standard PostgreSQL for local development
  const { Pool: PgPool } = require('pg');
  const { drizzle: pgDrizzle } = require('drizzle-orm/node-postgres');
  Pool = PgPool;
  drizzle = pgDrizzle;
  schema = require("@shared/schema");
} else {
  // Use Neon for remote database
  const { Pool: NeonPool, neonConfig } = require('@neondatabase/serverless');
  const { drizzle: neonDrizzle } = require('drizzle-orm/neon-serverless');
  const ws = require("ws");
  
  Pool = NeonPool;
  drizzle = neonDrizzle;
  schema = require("@shared/schema");
  
  neonConfig.webSocketConstructor = ws;
}

if (!isSQLite) {
  if (!process.env.DATABASE_URL) {
    throw new Error(
      "DATABASE_URL must be set. Did you forget to provision a database?",
    );
  }

  // Configure connection pool with better settings for reliability
  export const pool = new Pool({ 
    connectionString: process.env.DATABASE_URL,
    max: 10, // Maximum number of connections in the pool
    idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
    connectionTimeoutMillis: 10000, // Timeout for new connections after 10 seconds
  });

  // Add error handling for the pool
  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
  });

  // Create database instance based on the database type
  export const db = isLocalDB 
    ? drizzle(pool, { schema })
    : drizzle({ client: pool, schema });
}

// Helper function to retry database operations
export async function withRetry(
  operation,
  maxRetries = 3,
  delay = 1000
) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(\`Database operation failed (attempt \${attempt}/\${maxRetries}):\`, error);
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
}`;

  writeFileSync('server/db.sqlite.ts', sqliteDbConfig);
  console.log('✅ Created SQLite database configuration');
}

async function main() {
  try {
    console.log('📦 Installing SQLite dependencies...');
    execSync('npm install better-sqlite3 drizzle-orm', { stdio: 'inherit' });
    execSync('npm install --save-dev @types/better-sqlite3', { stdio: 'inherit' });
    
    console.log('⚙️  Creating SQLite configuration...');
    createSQLiteEnv();
    updateDbConfig();
    
    console.log('📋 Setting up database schema...');
    
    // Backup current .env and use SQLite env
    if (existsSync('.env')) {
      execSync('copy .env .env.backup', { stdio: 'inherit' });
      console.log('✅ Backed up current .env to .env.backup');
    }
    
    execSync('copy .env.sqlite .env', { stdio: 'inherit' });
    console.log('✅ Switched to SQLite configuration');
    
    // Backup current db.ts and use SQLite version
    execSync('copy server\\db.ts server\\db.backup.ts', { stdio: 'inherit' });
    execSync('copy server\\db.sqlite.ts server\\db.ts', { stdio: 'inherit' });
    console.log('✅ Switched to SQLite database configuration');
    
    // Run migrations
    execSync('npm run db:push', { stdio: 'inherit' });
    console.log('✅ Database schema created successfully!');
    
    console.log('\n🎯 SQLite database is ready!');
    console.log('📊 Database file: ./local_database.db');
    console.log('🔧 No additional software required!');
    
    console.log('\n📝 Next steps:');
    console.log('1. Run: node migrate-data-sqlite.js (to migrate data from Neon)');
    console.log('2. Run: npm run dev (to start the application)');
    
    console.log('\n💡 To switch back to PostgreSQL later:');
    console.log('1. copy .env.backup .env');
    console.log('2. copy server\\db.backup.ts server\\db.ts');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main();
