import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { 
  insertCutPieceSchema, 
  insertProjectSchema,
  insertProfileSchema,
  insertWindowDesignSchema,
  insertComponentSchema,
  insertGlassSpecificationSchema,
  insertWindowCuttingListSchema,
  insertWindowProjectSchema,
  insertProjectWindowItemSchema,
  insertAccessorySchema,
  insertComponentAccessorySchema,
  insertMachiningOperationSchema,
  insertCncToolSchema
} from "@shared/schema";
import { setupAuth } from "./auth";
import { hashPassword, comparePasswords, generateTemporaryPassword } from "./auth";
import { sendEmail, sendWelcomeEmail } from "./email";
import { createPaymentIntent, getOrCreateSubscription, handleWebhookEvent, stripe } from "./stripe";
import { analyzeWindowImage, convertAnalysisToWindowDesign } from "./openai";
import { generateInvoicePdf } from "./invoice";
import fs from "fs";
import path from "path";

// Implementation of the optimization algorithm to avoid module imports inside functions
function optimizeCutting(cutList: any[], options: any) {
  const { stockLengths = [6000], kerf = 3, endTrim = 5 } = options;
  
  // If stockLengths is a single number, convert it to an array
  const stockLengthsArray = Array.isArray(stockLengths) ? stockLengths : [stockLengths];
  
  // Make sure stockLengths are integers
  const validStockLengths = stockLengthsArray
    .map(length => parseInt(String(length)))
    .filter(length => !isNaN(length) && length > 0)
    .sort((a, b) => a - b);
  
  // If no valid stock lengths, use default
  if (validStockLengths.length === 0) {
    validStockLengths.push(6000);
  }
  
  console.log(`Optimizing for stock lengths: ${validStockLengths.join(', ')}mm`);
  
  // Function to optimize for a specific stock length
  function optimizeForStockLength(stockLength: number) {
    // Basic logic for cutting optimization
    const bars: any[] = [];
    let currentBar: any = { 
      remainingLength: stockLength - (2 * endTrim), 
      cuts: [],
      stockLength
    };
    bars.push(currentBar);
    
    // Sort cut list by length (descending)
    const sortedCutList = [...cutList].sort((a, b) => b.length - a.length);
    
    // Process each item in the cut list
    for (const item of sortedCutList) {
      const { length, quantity = 1, description = "", source = null } = item;
      
      // Process each piece based on quantity
      for (let i = 0; i < quantity; i++) {
        let placed = false;
        
        // Try to fit the piece in existing bars
        for (const bar of bars) {
          if (bar.remainingLength >= length + kerf) {
            bar.cuts.push({ 
              length, 
              description,
              source,
              startPosition: stockLength - bar.remainingLength
            });
            bar.remainingLength -= (length + kerf);
            placed = true;
            break;
          }
        }
        
        // If couldn't fit, create a new bar
        if (!placed) {
          const newBar = { 
            remainingLength: stockLength - (2 * endTrim) - length - kerf, 
            cuts: [{ 
              length, 
              description,
              source,
              startPosition: endTrim
            }],
            stockLength
          };
          bars.push(newBar);
        }
      }
    }
    
    // Calculate metrics
    const totalCuts = cutList.reduce((acc, item) => acc + (item.quantity || 1), 0);
    const totalPieceLength = cutList.reduce((acc, item) => acc + (item.length * (item.quantity || 1)), 0);
    const totalMaterialLength = bars.length * stockLength;
    
    // Calculate total waste correctly
    let totalWaste = 0;
    for (const bar of bars) {
      // Waste includes remaining length on each bar + the end trims
      totalWaste += bar.remainingLength;
    }
    
    // Add end trims to total waste calculation
    totalWaste += bars.length * (2 * endTrim);
    
    const wastePercentage = totalMaterialLength > 0 ? (totalWaste / totalMaterialLength) * 100 : 0;
    const averageUtilization = 100 - wastePercentage;
    
    // Calculate utilization and waste for each bar
    for (const bar of bars) {
      // Calculate used length (sum of all cut pieces + kerfs in between)
      let usedLength = 0;
      if (bar.cuts.length > 0) {
        // Sum of all cut lengths
        usedLength = bar.cuts.reduce((sum: number, cut: any) => sum + cut.length, 0);
        // Add kerfs between cuts
        usedLength += (bar.cuts.length - 1) * kerf;
      }
      
      const effectiveStockLength = stockLength - (2 * endTrim);
      const utilizationPercentage = effectiveStockLength > 0 ? (usedLength / effectiveStockLength) * 100 : 0;
      
      // Make sure each bar has its stock length
      bar.stockLength = stockLength;
      bar.utilizationPercentage = utilizationPercentage;
      bar.usedLength = usedLength;
      bar.wasteLength = bar.remainingLength;
    }
    
    // Log details for debugging
    console.log(`Stock length ${stockLength}mm: ${bars.length} bars, waste: ${wastePercentage.toFixed(2)}%`);
    
    return {
      stockLength,
      bars,
      totalPieces: totalCuts,
      totalBars: bars.length,
      totalPieceLength,
      totalMaterialLength,
      totalWaste,
      wastePercentage,
      averageUtilization
    };
  }
  
  // Optimize for each stock length
  const results = validStockLengths.map(stockLength => optimizeForStockLength(stockLength));
  
  // Find the solution with the minimum waste percentage
  const bestResult = results.reduce((best, current) => {
    // Log comparison for debugging
    console.log(`Comparing: ${current.stockLength}mm (${current.wastePercentage.toFixed(2)}%) vs ${best.stockLength}mm (${best.wastePercentage.toFixed(2)}%)`);
    return current.wastePercentage < best.wastePercentage ? current : best;
  }, results[0]);
  
  console.log(`Selected best stock length: ${bestResult.stockLength}mm with waste: ${bestResult.wastePercentage.toFixed(2)}%`);
  
  return bestResult;
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Setup authentication routes and middleware
  setupAuth(app);
  
  // User profile management endpoints
  app.put("/api/user/profile", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { name, email } = req.body;
      const updateData: Record<string, any> = {};
      
      if (name !== undefined) updateData.name = name;
      if (email !== undefined) updateData.email = email;
      
      const updatedUser = await storage.updateUser(req.user.id, updateData);
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Return updated user without password
      const { password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    } catch (error) {
      console.error("Error updating profile:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to update profile" });
    }
  });
  
  app.put("/api/user/password", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { currentPassword, newPassword } = req.body;
      
      // Get the current user with password
      const user = await storage.getUser(req.user.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Check if current password matches
      const isPasswordValid = await comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(400).json({ message: "Current password is incorrect" });
      }
      
      // Hash the new password
      const hashedPassword = await hashPassword(newPassword);
      
      // Update user with new password
      const updatedUser = await storage.updateUser(req.user.id, { 
        password: hashedPassword,
        passwordChanged: true // Mark that user has changed their password
      });
      
      if (!updatedUser) {
        return res.status(404).json({ message: "Failed to update password" });
      }
      
      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Error changing password:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to update password" });
    }
  });
  
  // Route for resetting temporary password (no verification of current password needed)
  app.put("/api/user/password/reset", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { newPassword } = req.body;
      
      // Get the current user
      const user = await storage.getUser(req.user.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Check if user actually needs to reset password
      if (user.passwordChanged) {
        return res.status(400).json({ message: "Password reset not required" });
      }
      
      // Hash the new password
      const hashedPassword = await hashPassword(newPassword);
      
      // Update user with new password and mark as changed
      const updatedUser = await storage.updateUser(req.user.id, { 
        password: hashedPassword,
        passwordChanged: true
      });
      
      if (!updatedUser) {
        return res.status(404).json({ message: "Failed to update password" });
      }
      
      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Error resetting password:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to reset password" });
    }
  });
  
  // Middleware to check if user is admin
  const isAdmin = (req: Request, res: Response, next: NextFunction) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: "Not authorized" });
    }
    
    next();
  };
  
  // Middleware to require pro subscription and check account status
  const requireProSubscription = (req: Request, res: Response, next: NextFunction) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // Check for Pro subscription
    if (req.user.subscriptionPlan !== 'pro') {
      return res.status(403).json({ 
        message: "Pro subscription required for this feature",
        currentPlan: req.user.subscriptionPlan,
        upgradeToPro: true
      });
    }
    
    // Check account status to block suspended accounts or those with payment issues
    if (req.user.accountStatus === 'suspended') {
      return res.status(403).json({
        message: "Your account has been suspended. Please contact support for assistance.",
        accountStatus: req.user.accountStatus,
        reason: req.user.statusNote || 'Account suspended due to payment issues or policy violation'
      });
    }
    
    // Payment issues - more lenient, just warning but still allowing access
    if (req.user.accountStatus === 'pending_payment') {
      console.log(`User ${req.user.id} has pending payment issues but was granted access to Pro feature`);
      // We could return a warning here, but for now we'll just let them through with a log
    }
    
    next();
  };
  
  // Middleware to require platinum subscription
  const requirePlatinumSubscription = (req: Request, res: Response, next: NextFunction) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // Admin users can access all features regardless of subscription tier
    if (req.user.role === 'admin') {
      return next();
    }
    
    // Check for Platinum subscription
    if (req.user.subscriptionPlan !== 'platinum') {
      return res.status(403).json({ 
        message: "Platinum subscription required for this feature",
        currentPlan: req.user.subscriptionPlan,
        upgradeToPlatinum: true
      });
    }
    
    // Check account status
    if (req.user.accountStatus === 'suspended') {
      return res.status(403).json({
        message: "Your account has been suspended. Please contact support for assistance.",
        accountStatus: req.user.accountStatus
      });
    }
    
    next();
  };
  
  // Middleware to enforce subscription limits
  const checkSubscriptionLimits = async (req: Request, res: Response, next: NextFunction) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // For actions that require subscription enforcement
    if (req.method === 'POST' && req.path === '/api/cut-pieces') {
      try {
        // Calculate total quantity in request
        const newQuantity = req.body.quantity || 1;
        
        // Skip check for pro users
        if (req.user.subscriptionPlan === 'pro') {
          return next();
        }
        
        // For free users, check the total quantity including existing cut pieces
        const existingCutPieces = await storage.getCutPieces(req.user.id);
        const totalExistingQuantity = existingCutPieces.reduce((sum, piece) => sum + piece.quantity, 0);
        
        if (totalExistingQuantity + newQuantity > req.user.maxQuantity) {
          return res.status(403).json({ 
            message: "Free plan quantity limit reached",
            currentQuantity: totalExistingQuantity,
            limit: req.user.maxQuantity,
            upgradeToPro: true
          });
        }
      } catch (error) {
        console.error("Error checking subscription limits:", error);
      }
    }
    
    next();
  };
  // API routes for cut pieces
  app.get("/api/cut-pieces", async (req: Request, res: Response) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      const cutPieces = await storage.getCutPieces(userId);
      res.json(cutPieces);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/cut-pieces", checkSubscriptionLimits, async (req: Request, res: Response) => {
    try {
      // Set userId from authenticated user if not provided 
      if (!req.body.userId && req.isAuthenticated()) {
        req.body.userId = req.user.id;
      }
      
      const validatedData = insertCutPieceSchema.parse(req.body);
      const cutPiece = await storage.createCutPiece(validatedData);
      res.status(201).json(cutPiece);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/cut-pieces/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertCutPieceSchema.partial().parse(req.body);
      const cutPiece = await storage.updateCutPiece(id, validatedData);
      if (!cutPiece) {
        return res.status(404).json({ message: "Cut piece not found" });
      }
      res.json(cutPiece);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/cut-pieces/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteCutPiece(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // API routes for projects
  app.get("/api/projects", async (req: Request, res: Response) => {
    try {
      // Ensure user can only see their own projects unless they're admin
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Only respect userId parameter for admins, for regular users always use their own id
      const userId = req.user.role === 'admin' && req.query.userId 
        ? parseInt(req.query.userId as string) 
        : req.user.id;
        
      const projects = await storage.getProjects(userId);
      res.json(projects);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/projects/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      const project = await storage.getProject(id);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }
      
      // Verify project belongs to the user or user is admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      res.json(project);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/projects", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Set userId to current user's ID
      req.body.userId = req.user.id;
      
      const validatedData = insertProjectSchema.parse(req.body);
      const project = await storage.createProject(validatedData);
      res.status(201).json(project);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/projects/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // First check if the project exists and belongs to the user
      const existingProject = await storage.getProject(id);
      if (!existingProject) {
        return res.status(404).json({ message: "Project not found" });
      }
      
      // Verify project belongs to the user or user is admin
      if (existingProject.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Prevent changing project owner
      if (req.body.userId && req.body.userId !== existingProject.userId && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Cannot change project owner" });
      }
      
      const validatedData = insertProjectSchema.partial().parse(req.body);
      const project = await storage.updateProject(id, validatedData);
      
      res.json(project);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/projects/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // Check if the project exists and belongs to the user
      const existingProject = await storage.getProject(id);
      if (!existingProject) {
        return res.status(404).json({ message: "Project not found" });
      }
      
      // Verify project belongs to the user or user is admin
      if (existingProject.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      await storage.deleteProject(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Admin routes
  
  // Get all users (admin only)
  app.get("/api/admin/users", isAdmin, async (req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Get user by ID (admin only)
  app.get("/api/admin/users/:id", isAdmin, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Update user subscription plan (admin only)
  app.put("/api/admin/users/:id/subscription", isAdmin, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const { 
        subscriptionPlan, 
        maxQuantity, 
        accountStatus, 
        statusNote 
      } = req.body;
      
      if (!subscriptionPlan || !['free', 'pro', 'platinum'].includes(subscriptionPlan)) {
        return res.status(400).json({ message: "Invalid subscription plan. Must be 'free', 'pro', or 'platinum'" });
      }
      
      // Validate account status if provided
      if (accountStatus && !['active', 'suspended', 'pending_payment'].includes(accountStatus)) {
        return res.status(400).json({ message: "Invalid account status. Must be 'active', 'suspended', or 'pending_payment'" });
      }
      
      // Set default max quantity based on subscription plan if not specified
      const updatedMaxQuantity = maxQuantity || (subscriptionPlan === 'pro' ? 999999 : 50);
      
      // Set subscription expiry for pro and platinum plans (1 year from now)
      const subscriptionExpiry = (subscriptionPlan === 'pro' || subscriptionPlan === 'platinum')
        ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() 
        : null;
      
      // If setting account status to 'pending_payment' or 'suspended', record the date
      const paymentIssueDate = (accountStatus === 'pending_payment' || accountStatus === 'suspended') 
        ? new Date().toISOString() 
        : undefined;
      
      const updatedUser = await storage.updateUser(id, {
        subscriptionPlan,
        maxQuantity: updatedMaxQuantity,
        subscriptionExpiry,
        ...(accountStatus && { accountStatus }),
        ...(statusNote && { statusNote }),
        ...(paymentIssueDate && { paymentIssueDate })
      });
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      try {
        // Get all invoices for this user
        const userInvoices = await storage.getInvoices(id);
        
        // Check if user account status is active or was updated to active
        // This handles both explicit changes to 'active' and cases where 'pending_payment' was removed
        const isUserActive = (accountStatus === 'active' || 
          (accountStatus === undefined && updatedUser.accountStatus === 'active'));
        
        // Check if user account status was changed to pending_payment
        const isPendingPayment = accountStatus === 'pending_payment';
        
        // Update invoice status based on account status
        if (isUserActive) {
          // If account is active, update all pending invoices to paid
          const updatePromises = userInvoices
            .filter(invoice => invoice.status === 'pending')
            .map(invoice => 
              storage.updateInvoice(invoice.id, { 
                status: 'paid', 
                paidDate: new Date() 
              })
            );
          
          if (updatePromises.length > 0) {
            await Promise.all(updatePromises);
            console.log(`Updated ${updatePromises.length} pending invoices to paid for user ${id}`);
          }
        } else if (isPendingPayment) {
          // If account status changed to pending_payment, mark paid invoices as pending 
          // This typically happens when payment fails or subscription expires
          const updatePromises = userInvoices
            .filter(invoice => invoice.status === 'paid') 
            .map(invoice => 
              storage.updateInvoice(invoice.id, { 
                status: 'pending',
                // Reset paid date if we're changing to pending
                paidDate: null 
              })
            );
          
          if (updatePromises.length > 0) {
            await Promise.all(updatePromises);
            console.log(`Updated ${updatePromises.length} paid invoices to pending for user ${id} due to payment issue`);
          }
        }
      } catch (invoiceError) {
        console.error("Error updating user invoices:", invoiceError);
        // Continue with the response even if invoice update fails
      }
      
      res.json(updatedUser);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Delete user (admin only)
  app.delete("/api/admin/users/:id", isAdmin, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      
      // Prevent admin from deleting themselves
      if (id === req.user.id) {
        return res.status(400).json({ message: "Cannot delete your own account" });
      }
      
      // Check if user exists
      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Check if trying to delete another admin
      if (user.role === 'admin') {
        return res.status(403).json({ message: "Cannot delete admin users" });
      }
      
      // Perform cascading delete of all user data and the user account
      await storage.deleteUser(id);
      
      res.status(200).json({ 
        message: "User and all associated data successfully deleted",
        username: user.username
      });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Reset user password (admin only)
  app.post("/api/admin/users/:id/reset-password", isAdmin, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      
      // Check if user exists
      const user = await storage.getUser(id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Generate a temporary password
      const temporaryPassword = generateTemporaryPassword(12);
      
      // Hash the temporary password
      const hashedPassword = await hashPassword(temporaryPassword);
      
      // Update user with new password
      const updatedUser = await storage.updateUser(id, { 
        password: hashedPassword,
        // Set a flag to indicate the user needs to change their password
        passwordChanged: false
      });
      
      if (!updatedUser) {
        return res.status(500).json({ message: "Failed to reset password" });
      }
      
      // Send email with temporary password if user has email
      if (user.email) {
        try {
          await sendEmail({
            to: user.email,
            from: '<EMAIL>',
            subject: 'Your Password Has Been Reset',
            html: `
              <h1>Password Reset</h1>
              <p>Hello ${user.name || user.username},</p>
              <p>Your password has been reset by an administrator. Please use the following temporary password to log in:</p>
              <p style="font-weight: bold; font-size: 1.2em; background-color: #f5f5f5; padding: 10px; border-radius: 5px;">${temporaryPassword}</p>
              <p>You will be prompted to change your password after logging in.</p>
              <p>If you did not request this password reset, please contact support immediately.</p>
            `
          });
          console.log(`Password reset email sent to ${user.email}`);
        } catch (emailError) {
          console.error("Failed to send password reset email:", emailError);
          // Continue even if email fails - we'll return the temp password to admin
        }
      }
      
      // Return success with the temporary password for the admin to communicate to the user
      res.status(200).json({ 
        message: "Password reset successful",
        username: user.username,
        temporaryPassword,
        emailSent: !!user.email
      });
    } catch (error) {
      console.error("Error resetting password:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Subscription info endpoint (for the current user)
  app.get("/api/subscription", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { 
        subscriptionPlan, 
        maxQuantity, 
        subscriptionExpiry, 
        accountStatus, 
        paymentIssueDate,
        statusNote 
      } = req.user;
      
      const cutPieces = await storage.getCutPieces(req.user.id);
      const totalQuantity = cutPieces.reduce((sum, piece) => sum + piece.quantity, 0);
      
      res.json({
        subscriptionPlan,
        maxQuantity,
        subscriptionExpiry,
        totalQuantity,
        remainingQuantity: maxQuantity - totalQuantity,
        isProUser: subscriptionPlan === 'pro',
        isPlatinumUser: subscriptionPlan === 'platinum',
        accountStatus: accountStatus || 'active',
        ...(paymentIssueDate && { paymentIssueDate }),
        ...(statusNote && { statusNote }),
        hasPaymentIssue: accountStatus === 'pending_payment' || accountStatus === 'suspended',
        isSuspended: accountStatus === 'suspended'
      });
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Endpoint to handle Pro plan upgrade requests
  app.post("/api/upgrade-request", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Extract request data
      const { name, email, phone, companyName, message } = req.body;
      
      // Create email content
      const emailContent = `
        Pro Plan Upgrade Request:
        
        User ID: ${req.user.id}
        Username: ${req.user.username}
        Name: ${name}
        Email: ${email}
        Phone: ${phone}
        Company: ${companyName || 'Not provided'}
        
        Message:
        ${message}
        
        Current Subscription: ${req.user.subscriptionPlan}
        Requested at: ${new Date().toISOString()}
      `;
      
      // Send email notification using SendGrid
      await sendEmail({
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: `Pro Plan Upgrade Request from ${req.user.username}`,
        text: emailContent
      });
      
      // Log the request for tracking
      console.log(`Upgrade request received from user ${req.user.id} (${req.user.username})`);
      
      res.status(200).json({ message: "Upgrade request submitted successfully" });
    } catch (error) {
      console.error("Error processing upgrade request:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to process upgrade request" 
      });
    }
  });
  
  // Stripe payment endpoints
  
  // Create a payment intent for one-time payment
  app.post("/api/create-payment-intent", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { amount } = req.body;
      if (!amount || amount < 1) {
        return res.status(400).json({ message: "Invalid payment amount" });
      }
      
      const paymentIntent = await createPaymentIntent(amount, req.user.id);
      res.json(paymentIntent);
    } catch (error) {
      console.error("Error creating payment intent:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to create payment intent" 
      });
    }
  });
  
  // Create or get subscription for Pro plan
  app.post("/api/get-or-create-subscription", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Extract custom amount and period from request body if provided
      const { amount, period } = req.body;
      
      // Validate subscription period
      if (period && !['monthly', 'annual'].includes(period)) {
        return res.status(400).json({ message: "Invalid subscription period. Must be 'monthly' or 'annual'" });
      }
      
      // Validate subscription amount
      if (amount && (typeof amount !== 'number' || amount <= 0)) {
        return res.status(400).json({ message: "Invalid amount. Must be a positive number" });
      }
      
      console.log(`Subscription request for user ${req.user.id}: ${period || 'annual'} plan at $${amount || (period === 'monthly' ? 12 : 130)}`);
      
      const result = await getOrCreateSubscription(req.user.id, { 
        amount: amount ? Number(amount) : undefined,
        period: period as 'monthly' | 'annual' | undefined
      });
      
      res.json(result);
    } catch (error) {
      console.error("Error creating subscription:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to create subscription" 
      });
    }
  });
  
  // Manual payment verification and upgrade
  app.post("/api/complete-payment", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const { paymentIntentId } = req.body;
      if (!paymentIntentId) {
        return res.status(400).json({ message: "Missing payment intent ID" });
      }
      
      // Verify payment intent status
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      // Log payment attempt for fraud monitoring
      console.log(`Payment verification for user ${req.user.id}: Intent ${paymentIntentId} status: ${paymentIntent.status}`);
      
      if (paymentIntent.status === 'succeeded') {
        // In a production app, we should also verify:
        // 1. The amount matches our expected price (either monthly or annual)
        const validAmounts = [
          Math.round(12 * 100),  // $12 monthly
          Math.round(130 * 100)  // $130 annual
        ];
        
        if (!validAmounts.includes(paymentIntent.amount)) {
          console.error(`Payment amount mismatch: Expected either $12 or $130, got ${paymentIntent.amount/100}`);
          return res.status(400).json({
            success: false,
            message: "Payment amount does not match expected price"
          });
        }
        
        // Set subscription period based on amount
        const subscriptionPeriod = paymentIntent.amount === Math.round(12 * 100) ? 'monthly' : 'annual';
        const subscriptionExpiry = new Date();
        
        // Set expiry date based on subscription period
        if (subscriptionPeriod === 'monthly') {
          subscriptionExpiry.setMonth(subscriptionExpiry.getMonth() + 1);
        } else {
          subscriptionExpiry.setFullYear(subscriptionExpiry.getFullYear() + 1);
        }
        
        // 2. The currency is correct
        if (paymentIntent.currency !== 'usd') {
          console.error(`Payment currency mismatch: Expected usd, got ${paymentIntent.currency}`);
          return res.status(400).json({
            success: false,
            message: "Payment currency is not valid"
          });
        }
        
        // 3. In production, you might want to verify that this payment hasn't been used before
        // by storing used payment intent IDs in your database
        
        // Update user to Pro plan with correct expiry date
        const user = await storage.updateUser(req.user.id, {
          subscriptionPlan: 'pro',
          maxQuantity: 999999,
          subscriptionExpiry: subscriptionExpiry.toISOString()
        });
        
        console.log(`User ${req.user.id} upgraded to Pro plan via manual completion`);
        
        // Send a welcome email to the user
        await sendWelcomeEmail(req.user, subscriptionPeriod);
        console.log(`Welcome email sent to ${req.user.email} for ${subscriptionPeriod} subscription`);
        
        res.status(200).json({
          success: true,
          message: "Payment verified and account upgraded to Pro plan",
          user
        });
      } else if (paymentIntent.status === 'requires_payment_method' || 
                 paymentIntent.status === 'requires_confirmation' ||
                 paymentIntent.status === 'requires_action') {
        // Payment needs additional action
        res.status(402).json({
          success: false,
          message: `Payment requires additional action: ${paymentIntent.status}`,
          actionRequired: true
        });
      } else if (paymentIntent.status === 'canceled') {
        // Payment was canceled
        res.status(400).json({
          success: false,
          message: "Payment was canceled"
        });
      } else {
        // Other status (failed, processing, etc.)
        res.status(400).json({
          success: false,
          message: `Payment not completed. Status: ${paymentIntent.status}`
        });
      }
    } catch (error) {
      console.error("Error completing payment:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to complete payment" 
      });
    }
  });
  
  // Webhook endpoint for handling Stripe events
  app.post("/api/stripe-webhook", async (req: Request, res: Response) => {
    const signature = req.headers["stripe-signature"] as string;
    
    try {
      // Verify webhook signature
      // const event = stripe.webhooks.constructEvent(
      //   req.body,
      //   signature,
      //   process.env.STRIPE_WEBHOOK_SECRET!
      // );
      
      // For demo purposes, we're skipping signature verification
      const event = req.body;
      
      console.log("Received Stripe event:", event.type);
      
      // Handle the event
      const result = await handleWebhookEvent(event);
      
      // Immediately update user to Pro plan if payment_intent.succeeded event
      // This is for testing - in production, this would happen via the webhook
      if (event.type === 'payment_intent.succeeded' && event.data?.object?.customer) {
        const customerId = event.data.object.customer;
        // Find user by Stripe customer ID
        const users = await storage.getAllUsers();
        const user = users.find(u => u.stripeCustomerId === customerId);
        
        if (user) {
          // Get payment amount to determine subscription period
          const paymentAmount = event.data.object.amount;
          const isMonthly = paymentAmount === Math.round(12 * 100);
          
          // Set proper expiry date
          const subscriptionExpiry = new Date();
          if (isMonthly) {
            subscriptionExpiry.setMonth(subscriptionExpiry.getMonth() + 1); // Monthly
            console.log(`Manual webhook handling: Upgrading user ${user.id} to Pro plan (Monthly)`);
          } else {
            subscriptionExpiry.setFullYear(subscriptionExpiry.getFullYear() + 1); // Annual
            console.log(`Manual webhook handling: Upgrading user ${user.id} to Pro plan (Annual)`);
          }
          
          await storage.updateUser(user.id, {
            subscriptionPlan: 'pro',
            maxQuantity: 999999,
            subscriptionExpiry: subscriptionExpiry.toISOString()
          });
          
          // Send welcome email
          const period = isMonthly ? 'monthly' : 'annual';
          await sendWelcomeEmail(user, period);
          console.log(`Welcome email sent to ${user.email} for ${period} subscription`);
        }
      }
      
      res.json(result);
    } catch (error) {
      console.error("Error handling Stripe webhook:", error);
      res.status(400).json({ 
        message: error instanceof Error ? error.message : "Failed to process webhook" 
      });
    }
  });

  // Window Calculator Feature - API Routes
  
  // Profile routes
  app.get("/api/profiles", requireProSubscription, async (req: Request, res: Response) => {
    try {
      // Authentication and subscription check handled by requireProSubscription middleware
      
      // Only admins can query profiles by userId, regular users get their own
      const userId = req.user.role === 'admin' && req.query.userId 
        ? parseInt(req.query.userId as string) 
        : req.user.id;
        
      const category = req.query.category as string | undefined;
      const profiles = await storage.getProfiles(userId, category);
      res.json(profiles);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/profiles/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      const profile = await storage.getProfile(id);
      
      if (!profile) {
        return res.status(404).json({ message: "Profile not found" });
      }
      
      // Verify profile belongs to the user or user is admin
      if (profile.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      res.json(profile);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/profiles", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Always set userId to the current user's ID
      req.body.userId = req.user.id;
      
      const profile = await storage.createProfile(req.body);
      res.status(201).json(profile);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/profiles/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      
      // First check if the profile exists and belongs to the user
      const existingProfile = await storage.getProfile(id);
      if (!existingProfile) {
        return res.status(404).json({ message: "Profile not found" });
      }
      
      // Verify profile belongs to the user or user is admin
      if (existingProfile.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Prevent changing profile owner unless you're admin
      if (req.body.userId && req.body.userId !== existingProfile.userId && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Cannot change profile owner" });
      }
      
      const profile = await storage.updateProfile(id, req.body);
      res.json(profile);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/profiles/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      
      // Check if the profile exists and belongs to the user
      const existingProfile = await storage.getProfile(id);
      if (!existingProfile) {
        return res.status(404).json({ message: "Profile not found" });
      }
      
      // Verify profile belongs to the user or user is admin
      if (existingProfile.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      await storage.deleteProfile(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // Window Design routes
  app.get("/api/window-designs", requireProSubscription, async (req: Request, res: Response) => {
    try {
      // Authentication and subscription check handled by requireProSubscription middleware
      
      // Only admins can query window designs by userId, regular users get their own
      const userId = req.user.role === 'admin' && req.query.userId 
        ? parseInt(req.query.userId as string) 
        : req.user.id;
        
      const category = req.query.category as string | undefined;
      const designs = await storage.getWindowDesigns(userId, category);
      res.json(designs);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/window-designs/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      const design = await storage.getWindowDesign(id);
      
      if (!design) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      // Verify design belongs to the user or user is admin
      if (design.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      res.json(design);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/window-designs", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Always set userId to current user's ID
      req.body.userId = req.user.id;
      
      const design = await storage.createWindowDesign(req.body);
      res.status(201).json(design);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/window-designs/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      
      // First check if the design exists and belongs to the user
      const existingDesign = await storage.getWindowDesign(id);
      if (!existingDesign) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      // Verify design belongs to the user or user is admin
      if (existingDesign.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Prevent changing design owner unless you're admin
      if (req.body.userId && req.body.userId !== existingDesign.userId && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Cannot change design owner" });
      }
      
      const design = await storage.updateWindowDesign(id, req.body);
      res.json(design);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/window-designs/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      
      // Check if the design exists and belongs to the user
      const existingDesign = await storage.getWindowDesign(id);
      if (!existingDesign) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      // Verify design belongs to the user or user is admin
      if (existingDesign.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      await storage.deleteWindowDesign(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // Component routes
  app.get("/api/window-designs/:windowDesignId/components", async (req: Request, res: Response) => {
    try {
      const windowDesignId = parseInt(req.params.windowDesignId);
      const components = await storage.getComponentsByWindowDesign(windowDesignId);
      res.json(components);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/components", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const component = await storage.createComponent(req.body);
      res.status(201).json(component);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/components/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const component = await storage.updateComponent(id, req.body);
      if (!component) {
        return res.status(404).json({ message: "Component not found" });
      }
      res.json(component);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/components/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      await storage.deleteComponent(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // Glass Specification routes
  app.get("/api/window-designs/:windowDesignId/glass-specifications", async (req: Request, res: Response) => {
    try {
      const windowDesignId = parseInt(req.params.windowDesignId);
      const specs = await storage.getGlassSpecificationsByWindowDesign(windowDesignId);
      res.json(specs);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/glass-specifications", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const spec = await storage.createGlassSpecification(req.body);
      res.status(201).json(spec);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/glass-specifications/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const spec = await storage.updateGlassSpecification(id, req.body);
      if (!spec) {
        return res.status(404).json({ message: "Glass specification not found" });
      }
      res.json(spec);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/glass-specifications/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      await storage.deleteGlassSpecification(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // Window Cutting List routes
  app.get("/api/window-cutting-lists", async (req: Request, res: Response) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      const lists = await storage.getWindowCuttingLists(userId);
      res.json(lists);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/window-cutting-lists/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const list = await storage.getWindowCuttingList(id);
      if (!list) {
        return res.status(404).json({ message: "Window cutting list not found" });
      }
      res.json(list);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/window-cutting-lists", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Set userId from authenticated user if not provided
      if (!req.body.userId) {
        req.body.userId = req.user.id;
      }
      
      const list = await storage.createWindowCuttingList(req.body);
      res.status(201).json(list);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/window-cutting-lists/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const list = await storage.updateWindowCuttingList(id, req.body);
      if (!list) {
        return res.status(404).json({ message: "Window cutting list not found" });
      }
      res.json(list);
    } catch (error) {
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/window-cutting-lists/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      await storage.deleteWindowCuttingList(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // Window Projects API routes
  app.get("/api/window-projects", requireProSubscription, async (req: Request, res: Response) => {
    try {
      // Authentication and subscription check handled by requireProSubscription middleware
      
      // Admin users can access all projects with a userId query param
      if (req.user.role === 'admin' && req.query.userId) {
        const targetUserId = parseInt(req.query.userId as string);
        const projects = await storage.getWindowProjects(targetUserId);
        return res.json(projects);
      }
      
      // Regular users can only access their own projects
      const projects = await storage.getWindowProjects(req.user.id);
      return res.json(projects);
    } catch (error) {
      console.error("Error fetching window projects:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/window-projects/:id", async (req: Request, res: Response) => {
    try {
      // Authentication check
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const id = parseInt(req.params.id);
      const project = await storage.getWindowProject(id);
      
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Authorization check - only allow access to own projects unless admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to access this project" });
      }
      
      res.json(project);
    } catch (error) {
      console.error("Error fetching window project:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/window-projects", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Add user ID if not provided
      if (!req.body.userId) {
        req.body.userId = req.user.id;
      }
      
      const validatedData = insertWindowProjectSchema.parse(req.body);
      const project = await storage.createWindowProject(validatedData);
      res.status(201).json(project);
    } catch (error) {
      console.error("Error creating window project:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/window-projects/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const project = await storage.getWindowProject(id);
      
      // Check if project exists
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Check if user owns this project or is admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to update this project" });
      }
      
      const validatedData = insertWindowProjectSchema.partial().parse(req.body);
      const updatedProject = await storage.updateWindowProject(id, validatedData);
      res.json(updatedProject);
    } catch (error) {
      console.error("Error updating window project:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/window-projects/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const project = await storage.getWindowProject(id);
      
      // Check if project exists
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Check if user owns this project or is admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to delete this project" });
      }
      
      await storage.deleteWindowProject(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting window project:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Project Window Items API routes
  app.get("/api/window-projects/:projectId/items", async (req: Request, res: Response) => {
    try {
      // Authentication check
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const projectId = parseInt(req.params.projectId);
      
      // First verify the user has access to this project
      const project = await storage.getWindowProject(projectId);
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Authorization check - only allow access to own projects unless admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to access items for this project" });
      }
      
      // If authorized, get the project items
      const items = await storage.getProjectWindowItems(projectId);
      res.json(items);
    } catch (error) {
      console.error("Error fetching project window items:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Generate material order for a project
  app.get("/api/window-projects/:projectId/material-order", async (req: Request, res: Response) => {
    try {
      // Authentication check
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const projectId = parseInt(req.params.projectId);
      
      // First verify the user has access to this project
      const project = await storage.getWindowProject(projectId);
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Authorization check - only allow access to own projects unless admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to access material order for this project" });
      }
      
      console.log(`Starting material order calculation for project ${projectId}`);
      
      // Get stock lengths from query params if provided
      const stockLengthsParam = req.query.stockLengths as string;
      let customStockLengths = [3000, 4000, 5000, 6000, 6500]; // Default stock lengths
      
      // Get architrave size from query params if provided
      const architraveSizeParam = req.query.architraveSize as string;
      let architraveSize = 0; // Default to 0 (no architrave)
      
      if (architraveSizeParam) {
        try {
          const parsedSize = parseInt(architraveSizeParam.trim());
          if (!isNaN(parsedSize) && parsedSize >= 0 && parsedSize <= 200) { // Reasonable limits for architrave size
            architraveSize = parsedSize;
            console.log(`Using architrave size: ${architraveSize}mm`);
          }
        } catch (parseError) {
          console.error("Error parsing architrave size:", parseError);
          // Use default if parsing fails
        }
      }
      
      if (stockLengthsParam) {
        try {
          // Parse and validate the stock lengths from the query parameter
          const parsedLengths = stockLengthsParam.split(',')
            .map(len => parseInt(len.trim()))
            .filter(len => !isNaN(len) && len > 0 && len <= 12000); // Filter out invalid values
          
          // Only use custom lengths if we have at least one valid length
          if (parsedLengths.length > 0) {
            // Sort lengths for better display and algorithm efficiency
            customStockLengths = parsedLengths.sort((a, b) => a - b);
          }
          
          console.log(`Using custom stock lengths: ${customStockLengths.join(', ')}mm`);
        } catch (parseError) {
          console.error("Error parsing stock lengths:", parseError);
          // Use defaults if parsing fails
        }
      }
      
      // Step 1: Get all window items in the project
      const projectItems = await storage.getProjectWindowItems(projectId);
      console.log(`Found ${projectItems.length} project items`);
      
      // Step 2: Create a map to store all components grouped by profile ID
      const profileComponentsMap = new Map();
      
      // Step 3: Process all project items directly
      await Promise.all(projectItems.map(async (projectItem) => {
        // Make sure project item has a window design ID
        if (!projectItem.windowDesignId) {
          console.log(`Project item ${projectItem.id} has no window design ID`);
          return;
        }
        
        // Get window design for this item
        const windowDesign = await storage.getWindowDesign(projectItem.windowDesignId);
        if (!windowDesign) {
          console.log(`Window design ${projectItem.windowDesignId} not found`);
          return;
        }
        
        console.log(`Processing window design: ${windowDesign.name} with ID ${windowDesign.id}`);
        
        // Get components for this window design
        const components = await storage.getComponentsByWindowDesign(projectItem.windowDesignId);
        if (!components || components.length === 0) return;
        
        console.log(`Found ${components.length} components for window design ${projectItem.windowDesignId}`);
        
        // Process each component to get cutting dimensions
        for (const component of components) {
          const profileId = component.profileId;
          if (!profileId) continue;
          
          // Get the profile to verify it exists
          const profile = await storage.getProfile(profileId);
          if (!profile) continue;
          
          if (!profileComponentsMap.has(profileId)) {
            profileComponentsMap.set(profileId, []);
          }
          
          // Calculate dimensions based on formulas
          let widthValue = projectItem.width;
          let heightValue = projectItem.height;
          
          // Add architrave size to frame components only
          if (component.componentType === "frame" && architraveSize > 0) {
            console.log(`Adding architrave size ${architraveSize}mm to frame component ${component.name}`);
            // Architrave is added to both sides, so multiply by 2
            widthValue += (architraveSize * 2); 
            heightValue += (architraveSize * 2);
          }
          
          // Calculate component parts
          const componentParts = [];
          
          // Process width parts (based on formula or fixed value)
          if (component.widthFormula) {
            try {
              // Replace variables in formula
              const formula = component.widthFormula
                .replace(/\bwidth\b/gi, widthValue.toString())
                .replace(/\bheight\b/gi, heightValue.toString());
              
              // Evaluate formula
              const widthPartLength = eval(formula);
              
              if (!isNaN(widthPartLength) && widthPartLength > 0) {
                // Calculate quantity based on component settings
                const quantity = component.widthQuantity || 1;
                
                // Create entries for each item in the project quantity
                for (let i = 0; i < projectItem.quantity; i++) {
                  componentParts.push({
                    length: Math.round(widthPartLength), // Round to nearest mm
                    quantity: quantity,
                    description: `${component.name} (W) - ${windowDesign.name} ${projectItem.windowType || ''}${projectItem.quantity > 1 ? ` #${i+1}` : ''}`,
                    source: {
                      windowName: `${windowDesign.name} ${projectItem.windowType || ''}`,
                      componentName: component.name,
                      width: projectItem.width,
                      height: projectItem.height
                    }
                  });
                }
              }
            } catch (e) {
              console.error(`Error evaluating width formula for component ${component.name}:`, e);
            }
          }
          
          // Process height parts (based on fixed height or formula)
          if (component.isFixedHeight && component.fixedHeightValue) {
            try {
              // Try to evaluate the fixed height value as a formula
              let heightPartLength;
              
              // Replace variables in formula if it contains variable references
              const formula = component.fixedHeightValue
                .replace(/\bwidth\b/gi, widthValue.toString())
                .replace(/\bheight\b/gi, heightValue.toString());
              
              try {
                // Try to evaluate as a formula
                heightPartLength = eval(formula);
              } catch (e) {
                // If not a valid formula, try to parse as a number
                heightPartLength = parseFloat(component.fixedHeightValue);
                
                if (isNaN(heightPartLength)) {
                  console.error(`Error evaluating fixed height value "${component.fixedHeightValue}" for component ${component.name}:`, e);
                  continue; // Skip this component if we can't calculate a height
                }
              }
              
              if (!isNaN(heightPartLength) && heightPartLength > 0) {
                // Calculate quantity based on component settings
                const quantity = component.heightQuantity || 1;
                
                // Create entries for each item in the project quantity
                for (let i = 0; i < projectItem.quantity; i++) {
                  componentParts.push({
                    length: Math.round(heightPartLength), // Round to nearest mm
                    quantity: quantity,
                    description: `${component.name} (H-Fixed) - ${windowDesign.name} ${projectItem.windowType || ''}${projectItem.quantity > 1 ? ` #${i+1}` : ''}`,
                    source: {
                      windowName: `${windowDesign.name} ${projectItem.windowType || ''}`,
                      componentName: component.name,
                      width: projectItem.width,
                      height: projectItem.height
                    }
                  });
                }
              }
            } catch (e) {
              console.error(`Error processing fixed height for component ${component.name}:`, e);
            }
          } else if (component.heightFormula) {
            try {
              // Replace variables in formula
              const formula = component.heightFormula
                .replace(/\bwidth\b/gi, widthValue.toString())
                .replace(/\bheight\b/gi, heightValue.toString());
              
              // Evaluate formula
              const heightPartLength = eval(formula);
              
              if (!isNaN(heightPartLength) && heightPartLength > 0) {
                // Calculate quantity based on component settings
                const quantity = component.heightQuantity || 1;
                
                // Create entries for each item in the project quantity
                for (let i = 0; i < projectItem.quantity; i++) {
                  componentParts.push({
                    length: Math.round(heightPartLength), // Round to nearest mm
                    quantity: quantity,
                    description: `${component.name} (H) - ${windowDesign.name} ${projectItem.windowType || ''}${projectItem.quantity > 1 ? ` #${i+1}` : ''}`,
                    source: {
                      windowName: `${windowDesign.name} ${projectItem.windowType || ''}`,
                      componentName: component.name,
                      width: projectItem.width,
                      height: projectItem.height
                    }
                  });
                }
              }
            } catch (e) {
              console.error(`Error evaluating height formula for component ${component.name}:`, e);
            }
          }
          
          // Add the calculated parts to the map
          if (componentParts.length > 0) {
            profileComponentsMap.get(profileId).push(...componentParts);
          }
        }
      }));
      
      // Step 4: Get profile information for each profile ID
      const profileData = await Promise.all(
        Array.from(profileComponentsMap.keys()).map(profileId => 
          storage.getProfile(Number(profileId))
        )
      );
      
      // Create a map of profile IDs to profile information
      const profileInfoMap = new Map();
      profileData.forEach(profile => {
        if (profile) {
          profileInfoMap.set(profile.id, profile);
        }
      });
      
      // Step 5: For each profile, run optimization algorithm
      const materialOrder = await Promise.all(
        Array.from(profileComponentsMap.entries()).map(async ([profileId, components]) => {
          const profile = profileInfoMap.get(Number(profileId));
          
          if (!profile) return null;
          
          // Get stock length from profile or use default
          const stockLength = profile.stockLength || 6000; // Default 6 meters if not specified
          const kerf = 3; // 3mm saw blade width
          const endTrim = 5; // 5mm trim from each end of stock
          
          // Convert components to the format expected by the optimization algorithm
          const cutList = components.map((comp: any) => ({
            length: comp.length,
            quantity: comp.quantity || 1,
            description: comp.description,
            source: comp.source
          }));
          
          // Use the optimization function defined at top level
          // No need to import anything
          
          // Run optimization with custom or standard stock lengths
          const optimizationResult = optimizeCutting(cutList, {
            stockLengths: customStockLengths, // Use the custom stock lengths from the request
            kerf,
            endTrim
          });
          
          return {
            profileId: Number(profileId),
            profileName: profile.name,
            stockLength: optimizationResult.stockLength, // Use the optimized stock length value from result
            totalBars: optimizationResult.bars.length,
            bars: optimizationResult.bars,
            totalMaterialLength: optimizationResult.totalMaterialLength,
            totalWaste: optimizationResult.totalWaste,
            averageUtilization: optimizationResult.averageUtilization,
            wastePercentage: optimizationResult.wastePercentage,
            imageUrl: profile.imageUrl
          };
        })
      );
      
      // Step 6: We already have project information from authorization check
      // No need to fetch project again
      
      // Filter out any null results
      const filteredMaterialOrder = materialOrder.filter(item => item !== null);
      
      // Step 7: Return the combined results
      res.json({
        project,
        projectItems,
        materialOrder: filteredMaterialOrder,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error("Error generating material order:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // API endpoint to get project window items by window design ID
  app.get("/api/project-window-items/by-design/:windowDesignId", async (req: Request, res: Response) => {
    try {
      // Authentication check
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Authentication required" });
      }
      
      const windowDesignId = parseInt(req.params.windowDesignId);
      
      // First verify the user has access to this window design
      const windowDesign = await storage.getWindowDesign(windowDesignId);
      if (!windowDesign) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      // Authorization check - only allow access to own designs unless admin
      if (windowDesign.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to access items for this window design" });
      }
      
      const items = await storage.getProjectWindowItemsByDesignId(windowDesignId);
      res.json(items);
    } catch (error) {
      console.error("Error fetching project window items by design ID:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  app.post("/api/window-projects/:projectId/items", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const projectId = parseInt(req.params.projectId);
      const project = await storage.getWindowProject(projectId);
      
      // Check if project exists
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Check if user owns this project or is admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to add items to this project" });
      }
      
      // Add projectId to the request body
      req.body.projectId = projectId;
      
      const validatedData = insertProjectWindowItemSchema.parse(req.body);
      const item = await storage.createProjectWindowItem(validatedData);
      res.status(201).json(item);
    } catch (error) {
      console.error("Error creating project window item:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  app.put("/api/project-window-items/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const item = await storage.getProjectWindowItem(id);
      
      // Check if item exists
      if (!item) {
        return res.status(404).json({ message: "Project window item not found" });
      }
      
      // Get the project to check ownership
      const project = await storage.getWindowProject(item.projectId);
      
      // Check if user owns this project or is admin
      if (project && project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to update this item" });
      }
      
      const validatedData = insertProjectWindowItemSchema.partial().parse(req.body);
      const updatedItem = await storage.updateProjectWindowItem(id, validatedData);
      res.json(updatedItem);
    } catch (error) {
      console.error("Error updating project window item:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  app.delete("/api/project-window-items/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const id = parseInt(req.params.id);
      const item = await storage.getProjectWindowItem(id);
      
      // Check if item exists
      if (!item) {
        return res.status(404).json({ message: "Project window item not found" });
      }
      
      // Get the project to check ownership
      const project = await storage.getWindowProject(item.projectId);
      
      // Check if user owns this project or is admin
      if (project && project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to delete this item" });
      }
      
      await storage.deleteProjectWindowItem(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting project window item:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Reorder project items
  app.post("/api/window-projects/:projectId/reorder", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const projectId = parseInt(req.params.projectId);
      const { itemIds } = req.body;
      
      if (!Array.isArray(itemIds) || itemIds.length === 0) {
        return res.status(400).json({ message: "Invalid item IDs. Must be a non-empty array." });
      }
      
      const project = await storage.getWindowProject(projectId);
      
      // Check if project exists
      if (!project) {
        return res.status(404).json({ message: "Window project not found" });
      }
      
      // Check if user owns this project or is admin
      if (project.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to reorder items in this project" });
      }
      
      await storage.reorderProjectWindowItems(projectId, itemIds);
      
      // Return updated items
      const updatedItems = await storage.getProjectWindowItems(projectId);
      res.json(updatedItems);
    } catch (error) {
      console.error("Error reordering project items:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });
  
  // Copy project with all its items
  app.post("/api/window-projects/:id/copy", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      // Check if the user has a pro subscription, as project management is a pro feature
      if (!req.user || !req.user.subscriptionPlan || req.user.subscriptionPlan !== 'pro') {
        // Check if the user is within free limits
        const projects = await storage.getWindowProjects(req.user?.id);
        if (projects.length >= 5) {
          return res.status(403).json({ 
            message: "You've reached the maximum number of projects allowed on the free plan. Please upgrade to Pro for unlimited projects." 
          });
        }
      }
      
      const projectId = parseInt(req.params.id);
      
      // Get the original project to check if it exists and belongs to the user
      const originalProject = await storage.getWindowProject(projectId);
      if (!originalProject) {
        return res.status(404).json({ message: "Project not found" });
      }
      
      // Check if user owns this project or is admin
      if (originalProject.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Not authorized to copy this project" });
      }
      
      // Copy the project with all its items - ensure correct method name casing
      const newProject = await storage.copyWindowProject(projectId, req.user.id);
      
      res.status(201).json(newProject);
    } catch (error) {
      console.error("Error copying project:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // API routes for accessories
  app.get("/api/accessories", requireProSubscription, async (req: Request, res: Response) => {
    try {
      // Authentication and subscription check handled by requireProSubscription middleware
      
      // Only admins can query accessories by userId, regular users get their own
      const userId = req.user.role === 'admin' && req.query.userId 
        ? parseInt(req.query.userId as string) 
        : req.user.id;
      
      const type = req.query.type as string | undefined;
      const subtype = req.query.subtype as string | undefined;
      
      const accessories = await storage.getAccessories(userId, type, subtype);
      res.json(accessories);
    } catch (error) {
      console.error("Error fetching accessories:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.get("/api/accessories/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      const accessory = await storage.getAccessory(id);
      
      if (!accessory) {
        return res.status(404).json({ message: "Accessory not found" });
      }
      
      // Verify accessory belongs to the user or user is admin
      if (accessory.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      res.json(accessory);
    } catch (error) {
      console.error("Error fetching accessory:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/accessories", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Always set userId to current user's ID
      req.body.userId = req.user.id;
      
      const validatedData = insertAccessorySchema.parse(req.body);
      const accessory = await storage.createAccessory(validatedData);
      res.status(201).json(accessory);
    } catch (error) {
      console.error("Error creating accessory:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/accessories/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // First check if the accessory exists and belongs to the user
      const existingAccessory = await storage.getAccessory(id);
      if (!existingAccessory) {
        return res.status(404).json({ message: "Accessory not found" });
      }
      
      // Verify accessory belongs to the user or user is admin
      if (existingAccessory.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      // Prevent changing accessory owner unless you're admin
      if (req.body.userId && req.body.userId !== existingAccessory.userId && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Cannot change accessory owner" });
      }
      
      const validatedData = insertAccessorySchema.partial().parse(req.body);
      const accessory = await storage.updateAccessory(id, validatedData);
      res.json(accessory);
    } catch (error) {
      console.error("Error updating accessory:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/accessories/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // First check if the accessory exists and belongs to the user
      const existingAccessory = await storage.getAccessory(id);
      if (!existingAccessory) {
        return res.status(404).json({ message: "Accessory not found" });
      }
      
      // Verify accessory belongs to the user or user is admin
      if (existingAccessory.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      await storage.deleteAccessory(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting accessory:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // API routes for component accessories
  app.get("/api/components/:componentId/accessories", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const componentId = parseInt(req.params.componentId);
      
      // First verify the component belongs to the user
      const component = await storage.getComponent(componentId);
      if (!component) {
        return res.status(404).json({ message: "Component not found" });
      }
      
      // Check if user has access to the window design this component belongs to
      const design = await storage.getWindowDesign(component.windowDesignId);
      if (!design) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      // Verify design belongs to the user or user is admin
      if (design.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const componentAccessories = await storage.getAccessoriesByComponent(componentId);
      res.json(componentAccessories);
    } catch (error) {
      console.error("Error fetching component accessories:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.post("/api/component-accessories", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Verify the component and accessory exist and belong to the user
      const component = await storage.getComponent(req.body.componentId);
      if (!component) {
        return res.status(404).json({ message: "Component not found" });
      }
      
      // Verify the user has access to the design
      const design = await storage.getWindowDesign(component.windowDesignId);
      if (!design) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      if (design.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied: You don't own this window design" });
      }
      
      // Check accessory ownership if relevant
      if (req.body.accessoryId) {
        const accessory = await storage.getAccessory(req.body.accessoryId);
        if (!accessory) {
          return res.status(404).json({ message: "Accessory not found" });
        }
        
        if (accessory.userId !== req.user.id && req.user.role !== 'admin') {
          return res.status(403).json({ message: "Access denied: You don't own this accessory" });
        }
      }
      
      const validatedData = insertComponentAccessorySchema.parse(req.body);
      const componentAccessory = await storage.createComponentAccessory(validatedData);
      res.status(201).json(componentAccessory);
    } catch (error) {
      console.error("Error creating component accessory:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/component-accessories/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // Get the existing component accessory
      const existingComponentAccessory = await storage.getComponentAccessory(id);
      if (!existingComponentAccessory) {
        return res.status(404).json({ message: "Component accessory not found" });
      }
      
      // Verify ownership of associated component (via window design)
      const component = await storage.getComponent(existingComponentAccessory.componentId);
      if (!component) {
        return res.status(404).json({ message: "Component not found" });
      }
      
      const design = await storage.getWindowDesign(component.windowDesignId);
      if (!design) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      if (design.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const validatedData = insertComponentAccessorySchema.partial().parse(req.body);
      const componentAccessory = await storage.updateComponentAccessory(id, validatedData);
      res.json(componentAccessory);
    } catch (error) {
      console.error("Error updating component accessory:", error);
      res.status(400).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.delete("/api/component-accessories/:id", async (req: Request, res: Response) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      
      // Get the existing component accessory
      const existingComponentAccessory = await storage.getComponentAccessory(id);
      if (!existingComponentAccessory) {
        return res.status(404).json({ message: "Component accessory not found" });
      }
      
      // Verify ownership of associated component (via window design)
      const component = await storage.getComponent(existingComponentAccessory.componentId);
      if (!component) {
        return res.status(404).json({ message: "Component not found" });
      }
      
      const design = await storage.getWindowDesign(component.windowDesignId);
      if (!design) {
        return res.status(404).json({ message: "Window design not found" });
      }
      
      if (design.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "Access denied" });
      }
      
      await storage.deleteComponentAccessory(id);
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting component accessory:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  // AI analysis endpoints (Platinum features)
  app.post("/api/ai/analyze-window", requirePlatinumSubscription, async (req: Request, res: Response) => {
    try {
      
      const { windowImage, width, height } = req.body;
      
      if (!windowImage || !width || !height) {
        return res.status(400).json({ 
          message: "Missing required parameters. Please provide windowImage, width, and height."
        });
      }
      
      // Analyze the window image using OpenAI
      const analysisResult = await analyzeWindowImage({
        windowImage,
        width: Number(width),
        height: Number(height)
      });
      
      // Return the analysis result
      res.json(analysisResult);
    } catch (error) {
      console.error("AI analysis error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to analyze window image"
      });
    }
  });
  
  // Save AI-generated window design
  app.post("/api/ai/save-design", requirePlatinumSubscription, async (req: Request, res: Response) => {
    try {
      
      const { analysisResult, windowImage, width, height, designName, includeImage } = req.body;
      
      if (!analysisResult || !width || !height) {
        return res.status(400).json({ 
          message: "Missing required parameters. Please provide analysisResult, width, and height."
        });
      }
      
      // Convert the AI analysis to a window design
      const designData = convertAnalysisToWindowDesign(
        analysisResult,
        includeImage ? windowImage : null,
        width,
        height
      );
      
      // Create the window design
      const windowDesignData = {
        name: designName || designData.designName,
        category: 'AI Generated',
        description: `AI-generated design for a ${width}mm × ${height}mm window`,
        imageUrl: includeImage ? windowImage : null,
        userId: req.user.id
      };
      
      const windowDesign = await storage.createWindowDesign(windowDesignData);
      
      // Create components
      for (const component of designData.components) {
        await storage.createComponent({
          ...component,
          windowDesignId: windowDesign.id
        });
      }
      
      // Create glass specifications
      for (const glass of designData.glass) {
        await storage.createGlassSpecification({
          ...glass,
          windowDesignId: windowDesign.id
        });
      }
      
      // Return the created design
      res.status(201).json({
        windowDesign,
        message: "AI-generated window design saved successfully"
      });
    } catch (error) {
      console.error("Error saving AI design:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to save AI-generated design"
      });
    }
  });
  
  // Billing and Invoices Routes
  app.get("/api/billing/invoices", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const invoices = await storage.getInvoices(req.user.id);
      res.json(invoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to fetch invoices" });
    }
  });
  
  app.get("/api/billing/invoices/:id", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const invoiceId = parseInt(req.params.id);
      if (isNaN(invoiceId)) {
        return res.status(400).json({ message: "Invalid invoice ID" });
      }
      
      const invoice = await storage.getInvoice(invoiceId);
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      
      // Check if user has access to this invoice
      if (invoice.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You do not have permission to view this invoice" });
      }
      
      res.json(invoice);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to fetch invoice" });
    }
  });
  
  app.get("/api/billing/invoices/:id/pdf", async (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    try {
      const invoiceId = parseInt(req.params.id);
      if (isNaN(invoiceId)) {
        return res.status(400).json({ message: "Invalid invoice ID" });
      }
      
      const invoice = await storage.getInvoice(invoiceId);
      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }
      
      // Check if user has access to this invoice
      if (invoice.userId !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You do not have permission to view this invoice" });
      }
      
      // Get user data
      const user = await storage.getUser(invoice.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Generate PDF
      const invoiceData = {
        ...invoice,
        user: {
          name: user.name || user.username,
          companyName: user.companyName,
          email: user.email,
          country: user.country
        }
      };
      
      const pdfDataUri = await generateInvoicePdf(invoiceData);
      
      // Create a sanitized filename for the invoice
      const safeInvoiceNumber = invoice.invoiceNumber.replace(/[\/\\:*?"<>|]/g, '-');
      const fileName = `Window-Craft-Pro-Invoice-${safeInvoiceNumber}.pdf`;
      
      // Return the PDF with filename suggestion
      res.json({ 
        pdfDataUri,
        fileName: fileName
      });
    } catch (error) {
      console.error("Error generating invoice PDF:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to generate invoice PDF" });
    }
  });
  
  // Admin only route for creating invoices
  app.post("/api/admin/invoices", isAdmin, async (req: Request, res: Response) => {
    try {
      const { userId, amount, description, subscriptionPlan, subscriptionPeriod, paymentMethod, status } = req.body;
      
      // Validate user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Generate invoice number
      const invoiceNumber = `INV-${Date.now()}-${userId}`;
      
      // Create invoice
      const invoice = await storage.createInvoice({
        userId,
        invoiceNumber,
        amount,
        description,
        subscriptionPlan,
        subscriptionPeriod,
        issuedDate: new Date(),
        dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // Due in 15 days
        paidDate: status === 'paid' ? new Date() : null,
        paymentMethod,
        status,
        items: [
          {
            description,
            amount,
            type: subscriptionPlan,
            period: subscriptionPeriod
          }
        ]
      });
      
      res.status(201).json(invoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to create invoice" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
