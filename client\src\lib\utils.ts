import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines multiple class values into a single className string
 * @param inputs - CSS class values to combine
 * @returns A merged className string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formats a number as a pixel value
 * @param value - The number to format
 * @returns A string with 'px' appended
 */
export function px(value: number): string {
  return `${value}px`;
}

/**
 * Formats a number as a percentage value
 * @param value - The number to format (0-1)
 * @returns A string with '%' appended
 */
export function percent(value: number): string {
  return `${(value * 100).toFixed(1)}%`;
}

/**
 * Generates a unique ID
 * @returns A unique string ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 9);
}

/**
 * Formats a date as a readable string
 * @param date - The date to format
 * @returns A formatted date string
 */
export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

/**
 * Formats a number with commas for thousands
 * @param num - The number to format
 * @returns A formatted number string
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('en-US');
}

/**
 * Safely parses a string to a number, returns default if invalid
 * @param value - The string to parse
 * @param defaultValue - The default value to use if parsing fails
 * @returns The parsed number or default value
 */
export function parseNumber(value: string, defaultValue: number = 0): number {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Clamps a number between min and max values
 * @param num - The number to clamp
 * @param min - The minimum value
 * @param max - The maximum value
 * @returns The clamped number
 */
export function clamp(num: number, min: number, max: number): number {
  return Math.min(Math.max(num, min), max);
}
