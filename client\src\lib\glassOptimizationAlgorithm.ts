import { 
  GlassPiece, 
  GlassStockSettings, 
  GlassOptimizationResult,
  GlassTypeResult,
  GlassSheetPlan,
  GlassCutItem
} from './types';

/**
 * Bin-packing algorithm for 2D cutting optimization
 * Uses a modified Guillotine cutting algorithm with different strategies
 */

// Node for the Binary Space Partitioning Tree
interface BspNode {
  x: number;
  y: number;
  width: number;
  height: number;
  used: boolean;
  right?: BspNode; // Right (or bottom) split
  down?: BspNode;  // Down (or right) split
}

// Enum for split strategy
enum SplitDirection {
  HORIZONTAL, // Split along the Y axis
  VERTICAL    // Split along the X axis
}

/**
 * Optimizes cutting for a specific glass type
 */
export function optimizeGlassCuttingForType(
  glassList: GlassPiece[],
  settings: GlassStockSettings,
  glassType: string
): GlassTypeResult {
  // Filter and expand the glass list based on glass type and quantity
  const expandedGlassList: GlassPiece[] = [];
  
  glassList
    .filter(piece => piece.glassType === glassType)
    .forEach(piece => {
      for (let i = 0; i < piece.quantity; i++) {
        expandedGlassList.push({
          ...piece,
          quantity: 1 // Each expanded piece has quantity 1
        });
      }
    });
  
  // If no pieces of this glass type, return empty result
  if (expandedGlassList.length === 0) {
    return {
      sheetsCount: 0,
      materialUsagePercent: 0,
      totalWasteArea: 0,
      sheetPlans: [],
      glassType
    };
  }
  
  // Sort pieces by area in descending order (larger pieces first)
  // This is a common heuristic for 2D bin packing problems
  expandedGlassList.sort((a, b) => {
    const areaA = a.width * a.height;
    const areaB = b.width * b.height;
    return areaB - areaA;
  });
  
  // Account for edge trim in effective sheet dimensions
  const effectiveWidth = settings.sheetWidth - (2 * settings.edgeTrim);
  const effectiveHeight = settings.sheetHeight - (2 * settings.edgeTrim);
  
  // Initialize sheets (bins)
  const sheetPlans: GlassSheetPlan[] = [];
  const bspRoots: BspNode[] = [];
  
  // Process each piece
  expandedGlassList.forEach(piece => {
    // Account for kerf in the piece dimensions
    const pieceWidth = piece.width + settings.kerf;
    const pieceHeight = piece.height + settings.kerf;
    
    // Try to fit the piece into an existing sheet
    let placed = false;
    
    // Try to place the piece without rotation first
    for (let i = 0; i < bspRoots.length; i++) {
      const node = findNode(bspRoots[i], pieceWidth, pieceHeight);
      if (node) {
        // Place the piece
        const placedNode = splitNode(node, pieceWidth, pieceHeight);
        
        // Add to the sheet's cuts
        sheetPlans[i].cuts.push({
          width: piece.width,
          height: piece.height,
          description: piece.description,
          glassType: piece.glassType,
          x: placedNode.x + settings.edgeTrim,
          y: placedNode.y + settings.edgeTrim,
          rotated: false
        });
        
        placed = true;
        break;
      }
    }
    
    // Try to place the piece with rotation if allowed and not yet placed
    if (!placed && piece.canRotate) {
      for (let i = 0; i < bspRoots.length; i++) {
        const node = findNode(bspRoots[i], pieceHeight, pieceWidth);
        if (node) {
          // Place the piece
          const placedNode = splitNode(node, pieceHeight, pieceWidth);
          
          // Add to the sheet's cuts
          sheetPlans[i].cuts.push({
            width: piece.height,  // Swapped
            height: piece.width,  // Swapped
            description: piece.description,
            glassType: piece.glassType,
            x: placedNode.x + settings.edgeTrim,
            y: placedNode.y + settings.edgeTrim,
            rotated: true
          });
          
          placed = true;
          break;
        }
      }
    }
    
    // If still not placed, create a new sheet
    if (!placed) {
      // Create a new sheet
      const newSheet: GlassSheetPlan = {
        sheetId: sheetPlans.length + 1,
        cuts: [],
        wasteArea: effectiveWidth * effectiveHeight,
        materialUsagePercent: 0,
        glassType
      };
      
      // Create a new BSP root node for the new sheet
      const root: BspNode = {
        x: 0,
        y: 0,
        width: effectiveWidth,
        height: effectiveHeight,
        used: false
      };
      
      // Try to place without rotation first
      if (pieceWidth <= effectiveWidth && pieceHeight <= effectiveHeight) {
        const placedNode = splitNode(root, pieceWidth, pieceHeight);
        
        newSheet.cuts.push({
          width: piece.width,
          height: piece.height,
          description: piece.description,
          glassType: piece.glassType,
          x: placedNode.x + settings.edgeTrim,
          y: placedNode.y + settings.edgeTrim,
          rotated: false
        });
      } 
      // Try with rotation if allowed
      else if (piece.canRotate && pieceHeight <= effectiveWidth && pieceWidth <= effectiveHeight) {
        const placedNode = splitNode(root, pieceHeight, pieceWidth);
        
        newSheet.cuts.push({
          width: piece.height,  // Swapped
          height: piece.width,  // Swapped
          description: piece.description,
          glassType: piece.glassType,
          x: placedNode.x + settings.edgeTrim,
          y: placedNode.y + settings.edgeTrim,
          rotated: true
        });
      } 
      // If still can't place, the piece is too big for the sheet
      else {
        console.error(`Piece is too large for sheet: ${piece.width}x${piece.height}`);
        // Skip this piece
        return;
      }
      
      sheetPlans.push(newSheet);
      bspRoots.push(root);
    }
  });
  
  // Calculate usage and waste for each sheet
  sheetPlans.forEach(sheet => {
    const totalArea = settings.sheetWidth * settings.sheetHeight;
    const usedArea = sheet.cuts.reduce((sum, cut) => sum + (cut.width * cut.height), 0);
    
    sheet.wasteArea = totalArea - usedArea;
    sheet.materialUsagePercent = usedArea / totalArea;
  });
  
  // Calculate overall statistics
  const totalArea = sheetPlans.length * (settings.sheetWidth * settings.sheetHeight);
  const totalUsedArea = sheetPlans.reduce((sum, sheet) => 
    sum + sheet.cuts.reduce((cutSum, cut) => cutSum + (cut.width * cut.height), 0), 0);
  const totalWasteArea = totalArea - totalUsedArea;
  const materialUsagePercent = totalUsedArea / totalArea;
  
  return {
    sheetsCount: sheetPlans.length,
    materialUsagePercent,
    totalWasteArea,
    sheetPlans,
    glassType
  };
}

/**
 * Find a node in the BSP tree that can fit the given width and height
 */
function findNode(root: BspNode, width: number, height: number): BspNode | null {
  // If this node is already used, try its children
  if (root.used) {
    return findNode(root.right!, width, height) || findNode(root.down!, width, height);
  }
  
  // If the piece fits in this node, return it
  if (width <= root.width && height <= root.height) {
    return root;
  }
  
  // Doesn't fit
  return null;
}

/**
 * Split a node to place a piece
 */
function splitNode(node: BspNode, width: number, height: number): BspNode {
  // Mark the node as used
  node.used = true;
  
  // Create right split (the remaining width)
  node.right = {
    x: node.x + width,
    y: node.y,
    width: node.width - width,
    height,
    used: false
  };
  
  // Create down split (the remaining height)
  node.down = {
    x: node.x,
    y: node.y + height,
    width: node.width,
    height: node.height - height,
    used: false
  };
  
  return node;
}

/**
 * Gets unique glass types from the cut list
 */
function getUniqueGlassTypes(glassList: GlassPiece[]): string[] {
  const glassSet = new Set<string>();
  glassList.forEach(piece => {
    if (piece.glassType && piece.glassType.trim() !== '') {
      glassSet.add(piece.glassType.trim());
    } else {
      // Default glass type if none specified
      glassSet.add('Clear');
    }
  });
  return Array.from(glassSet);
}

/**
 * Ensures all glass pieces have a glass type
 */
function ensureGlassTypes(glassList: GlassPiece[]): GlassPiece[] {
  return glassList.map(piece => {
    if (!piece.glassType || piece.glassType.trim() === '') {
      return {
        ...piece,
        glassType: 'Clear'
      };
    }
    return piece;
  });
}

/**
 * Main optimization function that handles all glass types separately
 */
export function optimizeGlassCutting(
  glassList: GlassPiece[],
  settings: GlassStockSettings
): GlassOptimizationResult {
  // Validate inputs
  if (!glassList || glassList.length === 0) {
    throw new Error("Glass list is empty");
  }
  
  // Ensure glass types are set for all pieces
  const processedGlassList = ensureGlassTypes(glassList);
  
  // Get all unique glass types
  const glassTypes = getUniqueGlassTypes(processedGlassList);
  
  // Process each glass type separately
  const glassTypeResults: GlassTypeResult[] = glassTypes.map(glassType => 
    optimizeGlassCuttingForType(processedGlassList, settings, glassType)
  );
  
  // Calculate total sheets
  const totalSheets = glassTypeResults.reduce(
    (total, result) => total + result.sheetsCount, 
    0
  );
  
  return {
    glassTypeResults,
    totalSheets
  };
}

/**
 * Apply different heuristics for glass optimization
 * Can be expanded for more complex optimization strategies
 */
export function optimizeGlassCuttingAdvanced(
  glassList: GlassPiece[],
  settings: GlassStockSettings
): GlassOptimizationResult {
  // For now, use the same approach as the basic optimization
  return optimizeGlassCutting(glassList, settings);
}