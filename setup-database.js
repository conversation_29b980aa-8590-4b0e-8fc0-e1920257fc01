import { execSync } from 'child_process';

console.log('🚀 Aluminum Cut Optimizer - Database Setup\n');

// Check if Docker is available
function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'ignore' });
    
    // Check if <PERSON><PERSON> is running
    try {
      execSync('docker info', { stdio: 'ignore' });
      console.log('✅ Docker is installed and running');
      return true;
    } catch (error) {
      console.log('⚠️  Docker is installed but not running');
      console.log('Please start Docker Desktop and try again');
      return false;
    }
  } catch (error) {
    console.log('❌ Docker is not installed');
    return false;
  }
}

// Check if PostgreSQL is installed locally
function checkPostgreSQL() {
  try {
    execSync('psql --version', { stdio: 'ignore' });
    console.log('✅ PostgreSQL is installed locally');
    return true;
  } catch (error) {
    console.log('❌ PostgreSQL is not installed locally');
    return false;
  }
}

function main() {
  console.log('🔍 Checking available database options...\n');
  
  const hasDocker = checkDocker();
  const hasPostgreSQL = checkPostgreSQL();
  
  console.log('\n📋 Setup Options:\n');
  
  if (hasDocker) {
    console.log('🐳 Option 1: Docker Setup (Recommended)');
    console.log('   - Easiest setup');
    console.log('   - Isolated environment');
    console.log('   - Run: node setup-local-db.js');
    console.log('');
  }
  
  if (hasPostgreSQL) {
    console.log('🗄️  Option 2: Use Existing PostgreSQL');
    console.log('   - Uses your existing PostgreSQL installation');
    console.log('   - Run: node setup-local-db-windows.js');
    console.log('');
  }
  
  if (!hasDocker && !hasPostgreSQL) {
    console.log('📥 Option 3: Install PostgreSQL');
    console.log('   1. Download from: https://www.postgresql.org/download/windows/');
    console.log('   2. Install with password "admin123" for postgres user');
    console.log('   3. Run: node setup-local-db-windows.js');
    console.log('');
    
    console.log('🐳 Option 4: Install Docker Desktop');
    console.log('   1. Download from: https://www.docker.com/products/docker-desktop');
    console.log('   2. Install and start Docker Desktop');
    console.log('   3. Run: node setup-local-db.js');
    console.log('');
  }
  
  console.log('💡 Current Status:');
  console.log(`   - Docker: ${hasDocker ? '✅ Available' : '❌ Not available'}`);
  console.log(`   - PostgreSQL: ${hasPostgreSQL ? '✅ Available' : '❌ Not available'}`);
  
  console.log('\n🎯 Recommended Action:');
  if (hasDocker) {
    console.log('   Run: node setup-local-db.js');
  } else if (hasPostgreSQL) {
    console.log('   Run: node setup-local-db-windows.js');
  } else {
    console.log('   Install Docker Desktop (easier) or PostgreSQL (more control)');
  }
  
  console.log('\n📚 For detailed instructions, see: LOCAL_DATABASE_SETUP.md');
}

main();
