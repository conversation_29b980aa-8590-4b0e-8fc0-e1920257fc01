import pg from 'pg';
import path from 'path';
import dotenv from 'dotenv';

const { Pool } = pg;
dotenv.config();

async function generateUserInvoice() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  let client;
  try {
    client = await pool.connect();
    console.log('Connected to database');

    // Get currently logged in user
    const userRes = await client.query(`
      SELECT * FROM users 
      ORDER BY id DESC 
      LIMIT 1
    `);

    if (userRes.rows.length === 0) {
      console.log('No users found');
      return;
    }

    const userId = userRes.rows[0].id;
    const userName = userRes.rows[0].name;
    console.log(`Found user with ID: ${userId}, name: ${userName}`);
    
    // Generate invoice number with timestamp
    const now = new Date();
    const invoiceNumber = `INV-${Date.now()}-${userId}`;
    
    // Set due date to 15 days in the future
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 15);
    
    // Create invoice data with monthly subscription
    const invoiceData = {
      user_id: userId,
      invoice_number: invoiceNumber,
      amount: 175.00,
      currency: 'USD',
      status: 'paid',
      description: 'Window Craft Pro Monthly Subscription',
      subscription_plan: 'pro',
      subscription_period: 'monthly',
      issued_date: now,
      due_date: dueDate,
      paid_date: now,
      payment_method: 'credit_card',
      items: JSON.stringify([
        {
          description: 'Pro Plan Monthly Subscription',
          amount: 175.00,
          type: 'pro',
          period: 'monthly'
        }
      ])
    };
    
    // Insert the invoice
    const insertQuery = `
      INSERT INTO invoices (
        user_id, invoice_number, amount, currency, status, description,
        subscription_plan, subscription_period, issued_date, due_date, paid_date,
        payment_method, items
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
      ) RETURNING id
    `;
    
    const insertValues = [
      invoiceData.user_id,
      invoiceData.invoice_number,
      invoiceData.amount,
      invoiceData.currency,
      invoiceData.status,
      invoiceData.description,
      invoiceData.subscription_plan,
      invoiceData.subscription_period,
      invoiceData.issued_date,
      invoiceData.due_date,
      invoiceData.paid_date,
      invoiceData.payment_method,
      invoiceData.items
    ];
    
    const result = await client.query(insertQuery, insertValues);
    const invoiceId = result.rows[0].id;
    
    console.log(`Created sample invoice with ID: ${invoiceId} for user ${userName}`);
    console.log('Sample invoice details:');
    console.log(JSON.stringify(invoiceData, null, 2));

    // Also create a pending invoice
    const pendingInvoiceNumber = `INV-${Date.now()}-P-${userId}`;
    const pendingInvoiceData = {
      user_id: userId,
      invoice_number: pendingInvoiceNumber,
      amount: 2100.00,
      currency: 'USD',
      status: 'pending',
      description: 'Window Craft Pro Annual Subscription',
      subscription_plan: 'pro',
      subscription_period: 'annual',
      issued_date: now,
      due_date: dueDate,
      paid_date: null,
      payment_method: 'credit_card',
      items: JSON.stringify([
        {
          description: 'Pro Plan Annual Subscription',
          amount: 2100.00,
          type: 'pro',
          period: 'annual'
        }
      ])
    };
    
    const pendingResult = await client.query(insertQuery, [
      pendingInvoiceData.user_id,
      pendingInvoiceData.invoice_number,
      pendingInvoiceData.amount,
      pendingInvoiceData.currency,
      pendingInvoiceData.status,
      pendingInvoiceData.description,
      pendingInvoiceData.subscription_plan,
      pendingInvoiceData.subscription_period,
      pendingInvoiceData.issued_date,
      pendingInvoiceData.due_date,
      pendingInvoiceData.paid_date,
      pendingInvoiceData.payment_method,
      pendingInvoiceData.items
    ]);
    
    const pendingInvoiceId = pendingResult.rows[0].id;
    console.log(`Created pending invoice with ID: ${pendingInvoiceId} for user ${userName}`);
    
  } catch (error) {
    console.error('Error creating user invoice:', error);
  } finally {
    if (client) client.release();
    await pool.end();
  }
}

generateUserInvoice();