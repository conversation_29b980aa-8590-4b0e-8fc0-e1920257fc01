# Aluminum Optimization Web Application

A web application for optimizing aluminum processing parameters based on alloy type, dimensions, and other factors.

## Features

- Input aluminum processing parameters (alloy type, dimensions, temperature range)
- Calculate optimal processing parameters
- View optimization results including:
  - Optimal temperature
  - Processing time
  - Energy consumption
  - Estimated cost
  - Quality score
  - Recommendations for improvement

## Technologies Used

- React
- TypeScript
- Vite
- CSS

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn
   ```

### Running the Application

1. Start the development server:
   ```
   npm run dev
   ```
   or
   ```
   yarn dev
   ```
2. Open your browser and navigate to `http://localhost:5173`

## Building for Production

```
npm run build
```
or
```
yarn build
```

## License

This project is licensed under the MIT License.
