@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
html[dir='rtl'] {
  text-align: right;
}

html[dir='rtl'] .MuiDataGrid-columnHeaderTitleContainer {
  flex-direction: row-reverse;
}

html[dir='rtl'] .MuiDataGrid-cellContent {
  text-align: right;
}

html[dir='rtl'] .ltr-text {
  direction: ltr;
  text-align: left;
}

html[dir='rtl'] .rtl-mirror {
  transform: scaleX(-1);
}

html[dir='rtl'] .rtl-rotate-180 {
  transform: rotate(180deg);
}

/* Ensure forms work properly in RTL */
html[dir='rtl'] input,
html[dir='rtl'] textarea {
  text-align: right;
}

/* Fix for flex direction in RTL mode */
html[dir='rtl'] .flex-row:not(.rtl-ignore) {
  flex-direction: row-reverse;
}

html[dir='rtl'] .mr-2:not(.rtl-ignore) {
  margin-right: 0;
  margin-left: 0.5rem;
}

html[dir='rtl'] .ml-2:not(.rtl-ignore) {
  margin-left: 0;
  margin-right: 0.5rem;
}

html[dir='rtl'] .mr-3:not(.rtl-ignore) {
  margin-right: 0;
  margin-left: 0.75rem;
}

html[dir='rtl'] .ml-3:not(.rtl-ignore) {
  margin-left: 0;
  margin-right: 0.75rem;
}

html[dir='rtl'] .rtl-keep-ltr {
  direction: ltr;
  text-align: left;
}

/* Keep numbers in LTR even in RTL mode */
html[dir='rtl'] .numbers-ltr {
  direction: ltr;
  display: inline-block;
}

/* Add special class for language menu to preserve correct positioning */
html[dir='rtl'] .language-menu {
  left: auto;
  right: 0;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-gray-50 text-foreground;
  }

  /* Excel-like table styles */
  .cut-list-table td input {
    @apply w-full h-full py-2 px-4 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset;
  }
  
  .cut-list-table th {
    @apply px-4 py-2 border-b border-r border-neutral-300 font-medium text-sm;
  }
  
  .cut-list-table .row-number {
    @apply w-14 border-r border-neutral-300 bg-neutral-100 text-center py-2;
  }
  
  /* Stock visualization styles */
  .stock-visualization {
    @apply relative h-16 bg-neutral-200 rounded overflow-hidden;
  }
  
  .cut-piece {
    @apply absolute h-full bg-primary flex items-center justify-center text-white text-xs font-medium border-r-2 border-white border-dashed;
  }
  
  .waste-piece {
    @apply absolute right-0 h-full bg-danger flex items-center justify-center text-white text-xs font-medium;
  }
}
