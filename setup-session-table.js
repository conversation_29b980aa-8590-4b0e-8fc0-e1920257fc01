import dotenv from 'dotenv';
import pg from 'pg';

dotenv.config();
const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function setupSessionTable() {
  try {
    console.log('Creating session table...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS session (
        sid varchar NOT NULL COLLATE "default",
        sess json NOT NULL,
        expire timestamp(6) NOT NULL
      )
      WITH (OIDS=FALSE);
    `);
    
    await pool.query(`
      ALTER TABLE session DROP CONSTRAINT IF EXISTS session_pkey;
    `);
    
    await pool.query(`
      ALTER TABLE session ADD CONSTRAINT session_pkey PRIMARY KEY (sid) NOT DEFERRABLE INITIALLY IMMEDIATE;
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS IDX_session_expire ON session(expire);
    `);
    
    console.log('Session table created successfully');
  } catch (error) {
    console.error('Error creating session table:', error);
  } finally {
    await pool.end();
  }
}

setupSessionTable();
