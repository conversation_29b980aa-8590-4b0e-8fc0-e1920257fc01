import { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { PasswordChangeForm } from "@/components/password-change-form";
import { Loader2, ShieldCheck } from "lucide-react";

export default function ChangePasswordPage() {
  const { user, isLoading } = useAuth();
  const [location, setLocation] = useLocation();
  
  // Redirect if user doesn't need to change password or is not logged in
  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        // Not logged in, redirect to auth page
        setLocation("/auth");
      } else if (user.passwordChanged !== false) {
        // Password has already been changed or not a temporary password
        setLocation("/");
      }
    }
  }, [user, isLoading, setLocation]);
  
  // Handle redirection after successful password change
  const handlePasswordChangeSuccess = () => {
    setLocation("/");
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // If user is not null and needs to change password
  if (user && user.passwordChanged === false) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-6">
            <div className="mx-auto h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <ShieldCheck className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="mt-3 text-2xl font-bold text-gray-900">Change Your Password</h2>
            <p className="mt-2 text-sm text-gray-600">
              Your administrator has reset your password. Please create a new secure password to continue.
            </p>
          </div>
          
          <PasswordChangeForm onSuccess={handlePasswordChangeSuccess} />
        </div>
      </div>
    );
  }
  
  // This should not be visible due to the redirect effect
  return null;
}