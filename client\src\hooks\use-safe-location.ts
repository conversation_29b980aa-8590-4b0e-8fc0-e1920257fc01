import { useLocation as useWouterLocation } from "wouter";
import { useCallback } from "react";

// Safe wrapper around wouter's useLocation hook to prevent "match" errors
export function useSafeLocation(): [string, (to: string) => void] {
  try {
    const [location, setLocation] = useWouterLocation();
    
    const safeSetLocation = useCallback((to: string) => {
      try {
        setLocation(to);
      } catch (error) {
        console.error("Error setting location:", error);
        // Fallback to window.location if wouter fails
        window.location.href = to;
      }
    }, [setLocation]);
    
    return [location, safeSetLocation];
  } catch (error) {
    console.error("Error using wouter location:", error);
    
    // Fallback implementation using window.location
    const fallbackLocation = window.location.pathname;
    const fallbackSetLocation = useCallback((to: string) => {
      window.location.href = to;
    }, []);
    
    return [fallbackLocation, fallbackSetLocation];
  }
}
